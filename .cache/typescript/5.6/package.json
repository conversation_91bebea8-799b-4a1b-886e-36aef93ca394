{"private": true, "dependencies": {"types-registry": "^0.1.723"}, "devDependencies": {"@types/accepts": "^1.3.7", "@types/ansi-align": "^3.0.5", "@types/argparse": "^2.0.17", "@types/babel__helper-validator-identifier": "^7.15.2", "@types/balanced-match": "^3.0.2", "@types/body-parser": "^1.19.5", "@types/boolbase": "^1.0.3", "@types/brace-expansion": "^1.1.2", "@types/braces": "^3.0.5", "@types/bytes": "^3.1.5", "@types/camelcase-css": "^2.0.2", "@types/caniuse-lite": "^1.0.5", "@types/chai": "^5.2.2", "@types/check-error": "^1.0.3", "@types/cli-spinners": "^1.3.3", "@types/color-convert": "^2.0.4", "@types/color-name": "^2.0.0", "@types/combined-stream": "^1.0.6", "@types/concat-map": "^0.0.3", "@types/content-disposition": "^0.5.8", "@types/content-type": "^1.1.8", "@types/cookie-signature": "^1.1.2", "@types/cors": "^2.8.18", "@types/cross-spawn": "^6.0.6", "@types/cssesc": "^3.0.2", "@types/debug": "^4.1.12", "@types/deep-eql": "^4.0.2", "@types/depd": "^1.1.37", "@types/destroy": "^1.0.3", "@types/didyoumean": "^1.2.3", "@types/diff-match-patch": "^1.0.36", "@types/dlv": "^1.1.5", "@types/doctrine": "^0.0.9", "@types/ee-first": "^1.1.3", "@types/electron-to-chromium": "^1.5.0", "@types/encodeurl": "^1.0.2", "@types/escape-html": "^1.0.4", "@types/eslint-scope": "^8.3.0", "@types/espree": "^10.1.0", "@types/esquery": "^1.5.4", "@types/esrecurse": "^4.3.0", "@types/estraverse": "^5.1.7", "@types/esutils": "^2.0.2", "@types/etag": "^1.8.3", "@types/express": "^5.0.2", "@types/fast-levenshtein": "^0.0.4", "@types/figlet": "^1.7.0", "@types/file-entry-cache": "^5.0.4", "@types/fill-range": "^7.0.3", "@types/finalhandler": "^1.2.3", "@types/flat-cache": "^2.0.2", "@types/follow-redirects": "^1.14.4", "@types/forwarded": "^0.1.3", "@types/fresh": "^0.5.2", "@types/function-bind": "^1.1.10", "@types/get-func-name": "^2.0.4", "@types/get-intrinsic": "^1.2.3", "@types/glob-parent": "^5.1.3", "@types/gopd": "^1.0.3", "@types/http-errors": "^2.0.4", "@types/humanize-ms": "^1.2.5", "@types/imurmurhash": "^0.1.4", "@types/inflight": "^1.0.1", "@types/inherits": "^2.0.0", "@types/is-core-module": "^2.2.2", "@types/is-extglob": "^2.1.0", "@types/is-glob": "^4.0.4", "@types/is-number": "^7.0.5", "@types/isexe": "^2.0.4", "@types/js-yaml": "^4.0.9", "@types/jsdom": "^21.1.7", "@types/json-buffer": "^3.0.2", "@types/json-schema": "^7.0.15", "@types/json-stable-stringify-without-jsonify": "^1.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/jwa": "^2.0.3", "@types/jws": "^3.2.10", "@types/less": "^3.0.8", "@types/levn": "^0.4.0", "@types/lodash": "^4.17.17", "@types/lodash.includes": "^4.3.9", "@types/lodash.isboolean": "^3.0.9", "@types/lodash.isinteger": "^4.0.9", "@types/lodash.isnumber": "^3.0.9", "@types/lodash.isplainobject": "^4.0.9", "@types/lodash.isstring": "^4.0.9", "@types/lodash.merge": "^4.6.9", "@types/lodash.once": "^4.1.9", "@types/media-typer": "^1.1.3", "@types/merge-descriptors": "^1.0.3", "@types/merge-stream": "^2.0.0", "@types/merge2": "^1.4.4", "@types/methods": "^1.1.4", "@types/micromatch": "^4.0.9", "@types/mime-db": "^1.43.5", "@types/mime-types": "^3.0.0", "@types/ms": "^2.1.0", "@types/mute-stream": "^0.0.4", "@types/mz": "^2.7.8", "@types/natural-compare": "^1.4.3", "@types/negotiator": "^0.6.3", "@types/node-fetch": "^2.6.12", "@types/normalize-path": "^3.0.2", "@types/object-assign": "^4.0.33", "@types/object-hash": "^3.0.6", "@types/object-inspect": "^1.13.0", "@types/on-finished": "^2.3.4", "@types/once": "^1.4.5", "@types/os-tmpdir": "^1.0.2", "@types/parseurl": "^1.3.3", "@types/path-is-absolute": "^1.0.2", "@types/path-parse": "^1.0.22", "@types/pathval": "^1.1.2", "@types/picomatch": "^4.0.0", "@types/pkgjs__parseargs": "^0.10.3", "@types/postcss-import": "^14.0.3", "@types/postcss-js": "^4.0.4", "@types/prelude-ls": "^1.1.34", "@types/proxy-addr": "^2.0.3", "@types/proxy-from-env": "^1.0.4", "@types/punycode": "^2.1.4", "@types/qs": "^6.14.0", "@types/range-parser": "^1.2.7", "@types/react": "^19.1.6", "@types/react-is": "^19.0.0", "@types/require-directory": "^2.1.6", "@types/require-from-string": "^1.2.3", "@types/resolve": "^1.20.6", "@types/run-parallel": "^1.1.2", "@types/safer-buffer": "^2.1.3", "@types/semver": "^7.7.0", "@types/send": "^0.17.4", "@types/serve-static": "^1.15.7", "@types/shebang-command": "^1.2.2", "@types/speakingurl": "^13.0.6", "@types/statuses": "^2.0.5", "@types/stylus": "^0.48.43", "@types/text-table": "^0.2.5", "@types/tinycolor2": "^1.4.6", "@types/tmp": "^0.2.6", "@types/to-regex-range": "^5.0.3", "@types/toidentifier": "^1.0.2", "@types/tr46": "^5.0.1", "@types/type-check": "^0.3.31", "@types/type-detect": "^4.0.3", "@types/type-is": "^1.6.7", "@types/ungap__structured-clone": "^1.2.0", "@types/uri-templates": "^0.1.34", "@types/use-sync-external-store": "^1.5.0", "@types/util-deprecate": "^1.0.4", "@types/utils-merge": "^1.0.0", "@types/uuid": "^10.0.0", "@types/vary": "^1.1.3", "@types/webidl-conversions": "^7.0.3", "@types/whatwg-url": "^13.0.0", "@types/which": "^3.0.4", "@types/xml-name-validator": "^4.0.3", "@types/yargs": "^17.0.33", "@types/yargs-parser": "^21.0.3"}}