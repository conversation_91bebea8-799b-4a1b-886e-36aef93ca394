import{r as x,f as S,A as Z,Q as ae,h as Y,o as r,c as l,g as i,j as e,M as re,t as n,n as E,R as le,K as oe,w as W,m as M,s as K,z as ee,H as te,F as B,k as A,v as j,C as ne,u as ie,x as D,G as de,a as ue,l as ce}from"./vendor.js";import{u as ve}from"./personnel.js";import{_ as ge,c as P,a as pe}from"./app.js";const me={class:"flex items-center"},xe={class:"flex-shrink-0 mr-3"},ye={class:"flex-1"},fe={class:"text-sm font-medium"},he={key:0,class:"text-sm opacity-90"},be={__name:"Toast",props:{type:{type:String,default:"success",validator:c=>["success","error","warning","info"].includes(c)},title:{type:String,required:!0},message:{type:String,default:""},duration:{type:Number,default:3e3},closable:{type:Boolean,default:!0}},emits:["close"],setup(c,{expose:H,emit:C}){const o=c,_=C,u=x(!1);let m=null;const h={success:"bg-green-500 text-white",error:"bg-red-500 text-white",warning:"bg-yellow-500 text-black",info:"bg-blue-500 text-white"},p=S(()=>{const v={success:"CheckCircleIcon",error:"XCircleIcon",warning:"ExclamationTriangleIcon",info:"InformationCircleIcon"};return v[o.type]||v.success}),b=()=>{u.value=!0,o.duration>0&&(m=setTimeout(()=>{w()},o.duration))},w=()=>{u.value=!1,m&&(clearTimeout(m),m=null),setTimeout(()=>{_("close")},300)};return Z(()=>{setTimeout(b,10)}),ae(()=>{m&&clearTimeout(m)}),H({show:b,close:w}),(v,y)=>(r(),Y(le,{to:"body"},[u.value?(r(),l("div",{key:0,class:E([["fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform",h[c.type]||h.success,u.value?"translate-x-0 opacity-100":"translate-x-full opacity-0"],"max-w-sm"])},[e("div",me,[e("div",xe,[(r(),Y(re(p.value),{class:"w-5 h-5"}))]),e("div",ye,[e("p",fe,n(c.title),1),c.message?(r(),l("p",he,n(c.message),1)):i("",!0)]),c.closable?(r(),l("button",{key:0,onClick:w,class:"flex-shrink-0 ml-3 opacity-70 hover:opacity-100 transition-opacity"},y[0]||(y[0]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]))):i("",!0)])],2)):i("",!0)]))}};x([]);function ke(){const c=u=>{const{type:m="success",title:h,message:p="",duration:b=3e3,closable:w=!0}=u,v=document.createElement("div");document.body.appendChild(v);const y=oe(be,{type:m,title:h,message:p,duration:b,closable:w,onClose:()=>{y.unmount(),document.body.removeChild(v)}});y.mount(v)};return{showToast:c,success:(u,m="")=>{c({type:"success",title:u,message:m})},error:(u,m="")=>{c({type:"error",title:u,message:m})},warning:(u,m="")=>{c({type:"warning",title:u,message:m})},info:(u,m="")=>{c({type:"info",title:u,message:m})}}}const _e={class:"space-y-6"},we={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ce={class:"flex items-center justify-between mb-4"},ze={key:1,class:"flex items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"},Ve={class:"flex-1"},Me={class:"text-sm font-medium text-gray-900 dark:text-white"},$e={class:"text-xs text-gray-500 dark:text-gray-400"},Te={key:0,class:"mt-1"},Be={class:"flex space-x-2"},Ae={key:2,class:"mt-4"},Ee={class:"flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-2"},He={class:"flex items-center"},Le={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-blue-600",fill:"none",viewBox:"0 0 24 24"},je={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2"},Pe={key:0,class:"bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6"},Se={class:"flex items-center justify-between mb-4"},Ie={class:"flex items-center space-x-3"},Ue={key:0,class:"mb-4"},Ne={class:"text-sm text-purple-700 dark:text-purple-300 bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700"},De={key:1,class:"mb-4"},Oe={class:"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-800 dark:text-purple-200"},Fe={key:2},Re={class:"grid grid-cols-1 md:grid-cols-2 gap-3 mb-3"},qe={class:"flex items-center space-x-2"},Xe={class:"text-sm font-medium text-gray-900 dark:text-white"},Je={class:"flex items-center space-x-2"},Qe={key:0,class:"text-xs text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-2 py-1 rounded"},Ge={class:"text-xs text-gray-500 dark:text-gray-400"},Ke={key:0,class:"text-center"},We={class:"text-sm text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900 px-3 py-1 rounded-full"},Ye={class:"mt-3"},Ze={class:"flex items-center justify-between mb-4"},et={class:"max-h-96 overflow-y-auto mb-4"},tt={class:"space-y-2"},st=["id","value"],at=["for"],rt={class:"text-sm font-medium text-gray-900 dark:text-white"},lt={class:"text-xs text-gray-500 dark:text-gray-400"},ot={key:0},nt={key:0,class:"text-xs text-gray-400 mt-1"},it={class:"flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600"},dt=["disabled"],ut={__name:"CVTab",props:{user:{type:Object,required:!0},canEdit:{type:Boolean,default:!1}},emits:["user-updated"],setup(c,{emit:H}){const C=c,o=H,{success:_,error:u,info:m}=ke(),h=x(!1),p=x(!1),b=x(0),w=x(!1),v=x(!1),y=x([]),f=x(!1),$=x(null),I=x(0),k=S(()=>{var a;if(!((a=C.user.profile)!=null&&a.cv_analysis_data))return null;try{return JSON.parse(C.user.profile.cv_analysis_data)}catch(t){return console.error("Error parsing CV analysis data:",t),null}}),X=a=>a?new Date(a).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",U=a=>({beginner:"Principiante",intermediate:"Intermedio",advanced:"Avanzato",expert:"Esperto",1:"Principiante",2:"Base",3:"Intermedio",4:"Avanzato",5:"Esperto"})[a]||"Intermedio",O=()=>{var a;(a=$.value)==null||a.click()},J=a=>{const t=a.target.files[0];t&&R(t)},F=a=>{a.preventDefault(),w.value=!1;const t=a.dataTransfer.files;t.length>0&&R(t[0])},R=async a=>{var s;if(!["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/msword","text/plain"].includes(a.type)){u("Formato file non supportato","Usa PDF, DOCX, DOC o TXT.");return}if(a.size>10*1024*1024){u("File troppo grande","Dimensione massima: 10MB.");return}h.value=!0,b.value=0;try{const d=new FormData;d.append("cv_file",a),d.append("analyze_skills","true"),d.append("auto_add_skills","false");const z=setInterval(()=>{b.value<90&&(b.value+=Math.random()*15)},200),L=await P.post(`/api/personnel/users/${C.user.id}/cv/upload`,d,{headers:{"Content-Type":"multipart/form-data"}});clearInterval(z),b.value=100;const V=L.data;if(console.log("Upload response:",V),V.success){if(h.value=!1,o("user-updated",V.data),(s=V.data.profile)!=null&&s.cv_analysis_data)try{const g=JSON.parse(V.data.profile.cv_analysis_data);console.log("Analysis data:",g),g.skills&&g.skills.length>0?_("CV caricato e analizzato con successo!",`Trovate ${g.skills.length} competenze`):_("CV caricato con successo!","Nessuna competenza estratta dall'AI")}catch(g){console.error("Error parsing analysis data:",g),_("CV caricato con successo!","Errore nel parsing dei dati AI")}else _("CV caricato con successo!","Analisi AI non disponibile"),console.log("No AI analysis data found in response. Profile data:",V.data.profile);$.value&&($.value.value="")}else throw new Error(V.message||"Errore durante il caricamento")}catch(d){console.error("Errore durante il caricamento del CV:",d),u("Errore durante il caricamento del CV",d.message)}finally{h.value=!1,b.value=0}},Q=async()=>{try{const t=(await P.get(`/api/personnel/users/${C.user.id}/cv/download`,{responseType:"blob"})).data,s=window.URL.createObjectURL(t),d=document.createElement("a");d.href=s,d.download=`CV_${C.user.full_name}.pdf`,document.body.appendChild(d),d.click(),window.URL.revokeObjectURL(s),document.body.removeChild(d)}catch(a){console.error("Errore durante il download del CV:",a),alert("Errore durante il download del CV: "+a.message)}},N=async()=>{if(confirm("Sei sicuro di voler eliminare il CV? Questa azione non può essere annullata."))try{const t=(await P.delete(`/api/personnel/users/${C.user.id}/cv`)).data;if(t.success)o("user-updated",t.data),_("CV eliminato con successo");else throw new Error(t.message||"Errore durante l'eliminazione")}catch(a){console.error("Errore durante l'eliminazione del CV:",a),u("Errore durante l'eliminazione del CV",a.message)}},q=async()=>{if(y.value.length!==0){f.value=!0;try{const a=y.value.map(d=>{const z=k.value.skills[d];return{...z,level:G(z.level)}}),s=(await P.post(`/api/personnel/users/${C.user.id}/skills/from-cv`,{selected_skills:a})).data;if(s.success){const{total_added:d,total_skipped:z}=s.data;d>0&&_(`Aggiunte ${d} competenze al profilo!`),z>0&&m(`${z} competenze erano già presenti nel profilo`),v.value=!1,y.value=[],o("user-updated")}else throw new Error(s.message||"Errore durante l'aggiunta delle competenze")}catch(a){console.error("Errore durante l'aggiunta delle competenze:",a),u("Errore durante l'aggiunta delle competenze",a.message)}finally{f.value=!1}}},G=a=>{const t={beginner:1,intermediate:3,advanced:4,expert:5};return typeof a=="number"?Math.max(1,Math.min(5,a)):typeof a=="string"&&t[a.toLowerCase()]||3};return W(h,a=>{a||(b.value=0)}),W(p,a=>{a||(I.value=0)}),(a,t)=>{var s,d,z,L,V;return r(),l("div",_e,[e("div",we,[e("div",Ce,[t[9]||(t[9]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"CV Attuale",-1)),c.canEdit&&!h.value?(r(),l("button",{key:0,onClick:O,class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm transition-colors duration-200"},[t[8]||(t[8]=e("svg",{class:"w-4 h-4 inline mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)),M(" "+n((s=c.user.profile)!=null&&s.current_cv_path?"Aggiorna CV":"Carica CV"),1)])):i("",!0)]),!((d=c.user.profile)!=null&&d.current_cv_path)&&c.canEdit?(r(),l("div",{key:0,onDrop:F,onDragover:t[0]||(t[0]=K(()=>{},["prevent"])),onDragenter:t[1]||(t[1]=K(()=>{},["prevent"])),class:E(["border-2 border-dashed rounded-lg p-8 text-center transition-colors duration-200",w.value?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-300 dark:border-gray-600"])},[t[11]||(t[11]=e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1)),t[12]||(t[12]=e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Carica il tuo CV",-1)),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},[t[10]||(t[10]=M(" Trascina qui il file o ")),e("button",{onClick:O,class:"text-blue-600 hover:text-blue-500"},"sfoglia")]),t[13]||(t[13]=e("p",{class:"mt-1 text-xs text-gray-400"},"PDF, DOCX, DOC, TXT (max 10MB)",-1))],34)):(z=c.user.profile)!=null&&z.current_cv_path?(r(),l("div",ze,[t[17]||(t[17]=e("svg",{class:"w-8 h-8 text-red-600 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"})],-1)),e("div",Ve,[e("p",Me,"CV_"+n(c.user.full_name)+".pdf",1),e("p",$e," Caricato il "+n(X(c.user.profile.cv_last_updated)),1),c.user.profile.cv_analysis_data?(r(),l("div",Te,t[14]||(t[14]=[e("span",{class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"}," ✨ Analisi AI completata ",-1)]))):i("",!0)]),e("div",Be,[e("button",{onClick:Q,class:"text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300",title:"Scarica CV"},t[15]||(t[15]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)])),c.canEdit?(r(),l("button",{key:0,onClick:N,class:"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300",title:"Elimina CV"},t[16]||(t[16]=[e("svg",{class:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M9 2a1 1 0 000 2h2a1 1 0 100-2H9z","clip-rule":"evenodd"}),e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 012 0v4a1 1 0 11-2 0V7zM12 7a1 1 0 112 0v4a1 1 0 11-2 0V7z","clip-rule":"evenodd"})],-1)]))):i("",!0)])])):i("",!0),h.value||p.value?(r(),l("div",Ae,[e("div",Ee,[e("div",He,[p.value?(r(),l("svg",Le,t[18]||(t[18]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):i("",!0),e("span",null,n(p.value?"Analisi AI in corso...":"Caricamento in corso..."),1)]),e("span",null,n(p.value?I.value:b.value)+"%",1)]),e("div",je,[e("div",{class:E(["h-2 rounded-full transition-all duration-300",p.value?"bg-purple-600":"bg-blue-600"]),style:ee({width:(p.value?I.value:b.value)+"%"})},null,6)])])):i("",!0)]),k.value?(r(),l("div",Pe,[e("div",Se,[t[20]||(t[20]=te('<div class="flex items-center" data-v-294be0fe><div class="w-8 h-8 bg-purple-100 dark:bg-purple-800 rounded-full flex items-center justify-center mr-3" data-v-294be0fe><svg class="w-4 h-4 text-purple-600 dark:text-purple-300" fill="currentColor" viewBox="0 0 20 20" data-v-294be0fe><path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" data-v-294be0fe></path></svg></div><h3 class="text-lg font-medium text-purple-900 dark:text-purple-100" data-v-294be0fe> Analisi AI del CV </h3></div>',1)),e("div",Ie,[c.canEdit&&((L=k.value.skills)==null?void 0:L.length)>0?(r(),l("button",{key:0,onClick:t[2]||(t[2]=g=>v.value=!0),class:"text-sm bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-md transition-colors duration-200 flex items-center"},t[19]||(t[19]=[e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),M(" Aggiungi Competenze ")]))):i("",!0)])]),k.value.summary?(r(),l("div",Ue,[t[21]||(t[21]=e("h4",{class:"text-sm font-medium text-purple-800 dark:text-purple-200 mb-2"},"Profilo Professionale",-1)),e("p",Ne,n(k.value.summary),1)])):i("",!0),k.value.experience_years?(r(),l("div",De,[e("span",Oe,[t[22]||(t[22]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})],-1)),M(" "+n(k.value.experience_years)+" anni di esperienza ",1)])])):i("",!0),((V=k.value.skills)==null?void 0:V.length)>0?(r(),l("div",Fe,[t[24]||(t[24]=e("h4",{class:"text-sm font-medium text-purple-800 dark:text-purple-200 mb-3"},"Competenze Estratte",-1)),e("div",Re,[(r(!0),l(B,null,A(k.value.skills.slice(0,8),(g,T)=>(r(),l("div",{key:T,class:"flex items-center justify-between bg-white dark:bg-gray-800 rounded-lg p-3 border border-purple-200 dark:border-purple-700"},[e("div",qe,[t[23]||(t[23]=e("div",{class:"w-2 h-2 bg-purple-500 rounded-full"},null,-1)),e("span",Xe,n(g.name),1)]),e("div",Je,[g.category?(r(),l("span",Qe,n(g.category),1)):i("",!0),e("span",Ge,n(U(g.level)),1)])]))),128))]),k.value.skills.length>8?(r(),l("div",Ke,[e("span",We," +"+n(k.value.skills.length-8)+" altre competenze disponibili ",1)])):i("",!0)])):i("",!0)])):i("",!0),e("input",{ref_key:"fileInput",ref:$,type:"file",accept:".pdf,.docx,.doc,.txt",onChange:J,class:"hidden"},null,544),v.value?(r(),l("div",{key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:t[7]||(t[7]=g=>v.value=!1)},[e("div",{class:"relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[6]||(t[6]=K(()=>{},["stop"]))},[e("div",Ye,[e("div",Ze,[t[26]||(t[26]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Seleziona Competenze da Aggiungere ",-1)),e("button",{onClick:t[3]||(t[3]=g=>v.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[25]||(t[25]=[e("svg",{class:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]))]),e("div",et,[e("div",tt,[(r(!0),l(B,null,A(k.value.skills,(g,T)=>(r(),l("div",{key:T,class:"flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"},[j(e("input",{type:"checkbox",id:`skill-${T}`,"onUpdate:modelValue":t[4]||(t[4]=se=>y.value=se),value:T,class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,8,st),[[ne,y.value]]),e("label",{for:`skill-${T}`,class:"ml-3 flex-1 cursor-pointer"},[e("div",rt,n(g.name),1),e("div",lt,[M(n(g.category)+" • Livello "+n(g.level||3)+" ",1),g.years_experience?(r(),l("span",ot," • "+n(g.years_experience)+" anni",1)):i("",!0)]),g.context?(r(),l("div",nt,n(g.context),1)):i("",!0)],8,at)]))),128))])]),e("div",it,[e("button",{onClick:t[5]||(t[5]=g=>v.value=!1),class:"px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md transition-colors duration-200"}," Annulla "),e("button",{onClick:q,disabled:y.value.length===0||f.value,class:"px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-md transition-colors duration-200"},n(f.value?"Aggiungendo...":`Aggiungi ${y.value.length} competenze`),9,dt)])])])])):i("",!0)])}}},ct=ge(ut,[["__scopeId","data-v-294be0fe"]]),vt={class:"personnel-profile"},gt={key:0,class:"flex justify-center items-center h-64"},pt={key:1,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6"},mt={class:"flex"},xt={class:"text-sm text-red-700 dark:text-red-300 mt-1"},yt={key:2,class:"space-y-6"},ft={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},ht={class:"bg-gradient-to-r from-blue-500 to-purple-600 px-6 py-8"},bt={class:"flex items-center space-x-6"},kt={class:"flex-shrink-0"},_t={class:"w-24 h-24 bg-white rounded-full flex items-center justify-center shadow-lg"},wt=["src","alt"],Ct={key:1,class:"w-24 h-24 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center"},zt={class:"flex-1 text-white"},Vt={class:"text-3xl font-bold"},Mt={class:"text-blue-100 text-lg"},$t={class:"flex items-center space-x-4 mt-2"},Tt={key:0,class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white"},Bt={class:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-white/20 text-white"},At={class:"flex-shrink-0"},Et={key:0,class:"px-6 py-4 bg-gray-50 dark:bg-gray-700"},Ht={class:"flex items-center justify-between mb-2"},Lt={class:"text-sm text-gray-500 dark:text-gray-400"},jt={class:"w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2"},Pt={class:"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6"},St={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},It={class:"flex items-center justify-between mb-4"},Ut={key:0,class:"space-y-3"},Nt={class:"flex items-center"},Dt={class:"text-gray-900 dark:text-white"},Ot={key:0,class:"flex items-center"},Ft={class:"text-gray-900 dark:text-white"},Rt={key:1,class:"flex items-center"},qt={class:"text-gray-900 dark:text-white"},Xt={key:1,class:"space-y-4"},Jt={class:"flex space-x-2"},Qt=["disabled"],Gt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Kt={key:0,class:"space-y-3"},Wt={class:"text-sm font-medium text-gray-900 dark:text-white"},Yt={class:"flex items-center space-x-2"},Zt={class:"flex space-x-1"},es={key:0,class:"text-xs text-green-600 dark:text-green-400"},ts={key:0,class:"text-sm text-gray-500 dark:text-gray-400 text-center pt-2"},ss={key:1,class:"text-gray-500 dark:text-gray-400 text-sm"},as={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},rs={class:"flex items-center justify-between mb-4"},ls={key:0,class:"space-y-3"},os={key:0,class:"flex items-center"},ns={class:"text-sm font-medium text-gray-900 dark:text-white"},is={key:1,class:"flex items-center"},ds={class:"text-sm font-medium text-gray-900 dark:text-white"},us={key:2,class:"flex items-center"},cs={class:"text-sm font-medium text-gray-900 dark:text-white"},vs={key:3,class:"flex items-center"},gs={class:"text-sm font-medium text-gray-900 dark:text-white"},ps={key:1,class:"space-y-4"},ms={key:0,class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6 mb-6"},xs={key:0,class:"text-gray-700 dark:text-gray-300 text-sm leading-relaxed"},ys={class:"w-full"},fs={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},hs={class:"border-b border-gray-200 dark:border-gray-700"},bs={class:"-mb-px flex space-x-8 px-6","aria-label":"Tabs"},ks=["onClick"],_s={key:0,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},ws={key:1,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Cs={key:2,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},zs={key:3,class:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},Vs={key:4,class:"ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 py-0.5 px-2 rounded-full text-xs"},Ms={class:"p-6"},$s={key:0,class:"space-y-6"},Ts={key:0},Bs={class:"flex items-center justify-between mb-4"},As={class:"text-lg font-medium text-gray-900 dark:text-white"},Es={class:"text-sm text-gray-500 dark:text-gray-400"},Hs={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},Ls={class:"flex items-start justify-between mb-3"},js={class:"flex-1"},Ps={class:"font-medium text-gray-900 dark:text-white mb-1"},Ss={class:"text-sm text-gray-500 dark:text-gray-400"},Is={class:"space-y-2 text-sm"},Us={key:0,class:"flex items-center text-gray-600 dark:text-gray-400"},Ns={key:1,class:"flex items-center text-gray-600 dark:text-gray-400"},Ds={key:1,class:"text-center py-12"},Os={key:1,class:"space-y-6"},Fs={key:0},Rs={class:"flex items-center justify-between mb-4"},qs={class:"text-lg font-medium text-gray-900 dark:text-white"},Xs={class:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400"},Js={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},Qs={class:"flex items-start justify-between mb-3"},Gs={class:"flex-1"},Ks={class:"font-medium text-gray-900 dark:text-white mb-1"},Ws={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},Ys={class:"flex flex-col space-y-1 ml-2"},Zs={key:0,class:"flex items-center text-sm text-gray-500 dark:text-gray-400"},ea={key:1,class:"text-center py-12"},ta={key:2,class:"space-y-6"},sa={key:0},aa={class:"flex items-center justify-between mb-4"},ra={class:"flex items-center space-x-4"},la={class:"text-lg font-medium text-gray-900 dark:text-white"},oa={class:"text-sm text-gray-500 dark:text-gray-400"},na={class:"text-sm text-gray-500 dark:text-gray-400"},ia={class:"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"},da={class:"flex items-center justify-between mb-2"},ua={class:"font-medium text-gray-900 dark:text-white"},ca={key:0,class:"text-green-600 dark:text-green-400 text-sm font-medium"},va={key:0,class:"text-sm text-gray-500 dark:text-gray-400 mb-3"},ga={class:"flex items-center justify-between"},pa={class:"flex items-center space-x-2"},ma={class:"flex space-x-1"},xa={class:"text-xs text-gray-500 dark:text-gray-400"},ya={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},fa={key:0,class:"flex items-center justify-between border-t border-gray-200 dark:border-gray-700 pt-4"},ha={class:"flex items-center space-x-2"},ba=["disabled"],ka={class:"text-sm text-gray-700 dark:text-gray-300"},_a=["disabled"],wa={class:"text-sm text-gray-500 dark:text-gray-400"},Ca={key:1,class:"text-center py-8"},za={key:3},Ta={__name:"PersonnelProfile",setup(c){const H=ie();ce(),ve();const{hasPermission:C}=pe(),o=x(null),_=x([]),u=x([]),m=x(!1),h=x(null),p=x(!1),b=x(!1),w=x("projects"),v=x(1),y=x(6),f=x({phone:"",bio:"",employee_id:"",job_title:"",employment_type:"",work_location:"",weekly_hours:40,address:"",emergency_contact_name:"",emergency_contact_phone:"",emergency_contact_relationship:""}),$=S(()=>{if(!o.value)return!1;try{return C.value&&typeof C.value=="function"?C.value("edit_personnel_data"):!1}catch(a){return console.warn("Permission check failed:",a),!1}}),I=S(()=>{var s;if(!((s=o.value)!=null&&s.skills))return[];const a=(v.value-1)*y.value,t=a+y.value;return o.value.skills.slice(a,t)}),k=S(()=>{var a;return(a=o.value)!=null&&a.skills?Math.ceil(o.value.skills.length/y.value):0}),X=S(()=>{var a,t;return[{id:"projects",name:"Progetti",count:_.value.length},{id:"tasks",name:"Task",count:u.value.length},{id:"skills",name:"Competenze",count:((t=(a=o.value)==null?void 0:a.skills)==null?void 0:t.length)||0},{id:"cv",name:"CV"}]}),U=a=>a?new Date(a).toLocaleDateString("it-IT",{year:"numeric",month:"long",day:"numeric"}):"",O=a=>({full_time:"Tempo Pieno",part_time:"Part Time",contractor:"Consulente",intern:"Stagista"})[a]||a,J=a=>{w.value=a,a==="skills"&&(v.value=1)},F=()=>{var a,t,s,d,z,L,V,g,T;o.value&&(f.value={phone:o.value.phone||"",bio:o.value.bio||"",employee_id:((a=o.value.profile)==null?void 0:a.employee_id)||"",job_title:((t=o.value.profile)==null?void 0:t.job_title)||"",employment_type:((s=o.value.profile)==null?void 0:s.employment_type)||"",work_location:((d=o.value.profile)==null?void 0:d.work_location)||"",weekly_hours:((z=o.value.profile)==null?void 0:z.weekly_hours)||40,address:((L=o.value.profile)==null?void 0:L.address)||"",emergency_contact_name:((V=o.value.profile)==null?void 0:V.emergency_contact_name)||"",emergency_contact_phone:((g=o.value.profile)==null?void 0:g.emergency_contact_phone)||"",emergency_contact_relationship:((T=o.value.profile)==null?void 0:T.emergency_contact_relationship)||""})},R=()=>{p.value=!1,F()},Q=async()=>{if(o.value){b.value=!0;try{const t=(await P.put(`/api/personnel/users/${o.value.id}`,f.value)).data;if(t.success)o.value=t.data.user,p.value=!1,console.log("Profilo aggiornato con successo");else throw new Error(t.message||"Errore durante il salvataggio")}catch(a){console.error("Errore durante il salvataggio:",a),h.value=a.message}finally{b.value=!1}}},N=async a=>{m.value=!0,h.value=null;try{const s=(await P.get(`/api/personnel/users/${a}`)).data;if(s.success)o.value=s.data.user,F();else throw new Error(s.message||"Errore nel caricamento del profilo")}catch(t){console.error("Error fetching user profile:",t),h.value=t.message}finally{m.value=!1}},q=async a=>{try{const s=(await P.get(`/api/tasks?assignee_id=${a}&limit=20`)).data;s.success&&(u.value=s.data.tasks||[])}catch(t){console.error("Error fetching user tasks:",t)}},G=async a=>{a?a.id&&a.profile?o.value=a:(a.cv_path&&(o.value.profile.current_cv_path=a.cv_path),a.cv_last_updated!==void 0&&(o.value.profile.cv_last_updated=a.cv_last_updated),a.analysis&&(o.value.profile.cv_analysis_data=JSON.stringify(a.analysis)),a.profile_completion!==void 0&&(o.value.profile.profile_completion=a.profile_completion),a.cv_path===null&&(o.value.profile.current_cv_path=null,o.value.profile.cv_last_updated=null,o.value.profile.cv_analysis_data=null)):await N(o.value.id)};return Z(async()=>{const a=H.params.id;if(!a){h.value="ID utente non specificato";return}await N(a),o.value&&(_.value=o.value.projects||[],await q(a))}),W(()=>H.params.id,async a=>{a&&(await N(a),o.value&&(_.value=o.value.projects||[],await q(a)))}),(a,t)=>(r(),l("div",vt,[m.value?(r(),l("div",gt,t[11]||(t[11]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):h.value?(r(),l("div",pt,[e("div",mt,[t[13]||(t[13]=e("svg",{class:"w-5 h-5 text-red-400 mr-2 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[12]||(t[12]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento del profilo",-1)),e("p",xt,n(h.value),1)])])])):o.value?(r(),l("div",yt,[e("div",ft,[e("div",ht,[e("div",bt,[e("div",kt,[e("div",_t,[o.value.profile_image?(r(),l("img",{key:0,src:o.value.profile_image,alt:o.value.full_name,class:"w-24 h-24 rounded-full object-cover"},null,8,wt)):(r(),l("div",Ct,t[14]||(t[14]=[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-gray-300",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})],-1)])))])]),e("div",zt,[e("h1",Vt,n(o.value.full_name),1),e("p",Mt,n(o.value.position||"Posizione non specificata"),1),e("div",$t,[o.value.department?(r(),l("span",Tt,[t[15]||(t[15]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),M(" "+n(o.value.department.name),1)])):i("",!0),e("span",Bt,[t[16]||(t[16]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z","clip-rule":"evenodd"})],-1)),M(" "+n(o.value.role),1)])])]),e("div",At,[$.value?(r(),l("button",{key:0,onClick:t[0]||(t[0]=s=>p.value=!p.value),class:"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors duration-200"},[t[17]||(t[17]=e("svg",{class:"w-5 h-5 inline mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)),M(" "+n(p.value?"Annulla":"Modifica"),1)])):i("",!0)])])]),o.value.profile&&o.value.profile.profile_completion!==void 0?(r(),l("div",Et,[e("div",Ht,[t[18]||(t[18]=e("span",{class:"text-sm font-medium text-gray-700 dark:text-gray-300"},"Completamento Profilo",-1)),e("span",Lt,n(o.value.profile.profile_completion)+"%",1)]),e("div",jt,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:ee({width:o.value.profile.profile_completion+"%"})},null,4)])])):i("",!0)]),e("div",Pt,[e("div",St,[e("div",It,[t[20]||(t[20]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Informazioni di Contatto",-1)),$.value&&!p.value?(r(),l("button",{key:0,onClick:t[1]||(t[1]=s=>p.value=!0),class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"},t[19]||(t[19]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]))):i("",!0)]),p.value?(r(),l("div",Xt,[e("div",null,[t[24]||(t[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Telefono",-1)),j(e("input",{"onUpdate:modelValue":t[2]||(t[2]=s=>f.value.phone=s),type:"tel",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[D,f.value.phone]])]),e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Bio",-1)),j(e("textarea",{"onUpdate:modelValue":t[3]||(t[3]=s=>f.value.bio=s),rows:"3",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[D,f.value.bio]])]),e("div",Jt,[e("button",{onClick:Q,disabled:b.value,class:"flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md transition-colors duration-200"},n(b.value?"Salvataggio...":"Salva"),9,Qt),e("button",{onClick:R,class:"flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md transition-colors duration-200"}," Annulla ")])])):(r(),l("div",Ut,[e("div",Nt,[t[21]||(t[21]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),e("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})],-1)),e("span",Dt,n(o.value.email),1)]),o.value.phone?(r(),l("div",Ot,[t[22]||(t[22]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"})],-1)),e("span",Ft,n(o.value.phone),1)])):i("",!0),o.value.hire_date?(r(),l("div",Rt,[t[23]||(t[23]=e("svg",{class:"w-5 h-5 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})],-1)),e("span",qt,"Assunto il "+n(U(o.value.hire_date)),1)])):i("",!0)]))]),e("div",Gt,[t[26]||(t[26]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Competenze Principali",-1)),o.value.skills&&o.value.skills.length>0?(r(),l("div",Kt,[(r(!0),l(B,null,A(o.value.skills.slice(0,4),s=>(r(),l("div",{key:s.id,class:"flex items-center justify-between"},[e("span",Wt,n(s.name),1),e("div",Yt,[e("div",Zt,[(r(),l(B,null,A(5,d=>e("div",{key:d,class:E(["w-2 h-2 rounded-full",d<=s.proficiency_level?"bg-blue-500":"bg-gray-300 dark:bg-gray-600"])},null,2)),64))]),s.certified?(r(),l("span",es,"✓")):i("",!0)])]))),128)),o.value.skills.length>4?(r(),l("div",ts," +"+n(o.value.skills.length-4)+" altre competenze ",1)):i("",!0)])):(r(),l("div",ss," Nessuna competenza registrata "))]),o.value.profile?(r(),l("div",as,[e("div",rs,[t[28]||(t[28]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Informazioni HR",-1)),$.value&&!p.value?(r(),l("button",{key:0,onClick:t[4]||(t[4]=s=>p.value=!0),class:"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"},t[27]||(t[27]=[e("svg",{class:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]))):i("",!0)]),p.value?(r(),l("div",ps,[e("div",null,[t[37]||(t[37]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"ID Dipendente",-1)),j(e("input",{"onUpdate:modelValue":t[5]||(t[5]=s=>f.value.employee_id=s),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[D,f.value.employee_id]])]),e("div",null,[t[38]||(t[38]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Titolo Lavoro",-1)),j(e("input",{"onUpdate:modelValue":t[6]||(t[6]=s=>f.value.job_title=s),type:"text",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[D,f.value.job_title]])]),e("div",null,[t[40]||(t[40]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Tipo Contratto",-1)),j(e("select",{"onUpdate:modelValue":t[7]||(t[7]=s=>f.value.employment_type=s),class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[39]||(t[39]=[te('<option value="">Seleziona tipo</option><option value="full_time">Tempo Pieno</option><option value="part_time">Part Time</option><option value="contractor">Consulente</option><option value="intern">Stagista</option>',5)]),512),[[de,f.value.employment_type]])]),e("div",null,[t[41]||(t[41]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore Settimanali",-1)),j(e("input",{"onUpdate:modelValue":t[8]||(t[8]=s=>f.value.weekly_hours=s),type:"number",min:"1",max:"60",step:"0.5",class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[D,f.value.weekly_hours]])])])):(r(),l("div",ls,[o.value.profile.employee_id?(r(),l("div",os,[t[30]||(t[30]=e("svg",{class:"w-4 h-4 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})],-1)),e("div",null,[t[29]||(t[29]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400"},"ID Dipendente",-1)),e("p",ns,n(o.value.profile.employee_id),1)])])):i("",!0),o.value.profile.job_title?(r(),l("div",is,[t[32]||(t[32]=e("svg",{class:"w-4 h-4 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z","clip-rule":"evenodd"})],-1)),e("div",null,[t[31]||(t[31]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400"},"Titolo",-1)),e("p",ds,n(o.value.profile.job_title),1)])])):i("",!0),o.value.profile.employment_type?(r(),l("div",us,[t[34]||(t[34]=e("svg",{class:"w-4 h-4 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),e("div",null,[t[33]||(t[33]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400"},"Tipo Contratto",-1)),e("p",cs,n(O(o.value.profile.employment_type)),1)])])):i("",!0),o.value.profile.weekly_hours?(r(),l("div",vs,[t[36]||(t[36]=e("svg",{class:"w-4 h-4 text-gray-400 mr-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})],-1)),e("div",null,[t[35]||(t[35]=e("span",{class:"text-xs text-gray-500 dark:text-gray-400"},"Ore Settimanali",-1)),e("p",gs,n(o.value.profile.weekly_hours)+"h",1)])])):i("",!0)]))])):i("",!0)]),o.value.bio||p.value?(r(),l("div",ms,[t[42]||(t[42]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Bio",-1)),p.value?i("",!0):(r(),l("p",xs,n(o.value.bio||"Nessuna bio disponibile"),1))])):i("",!0),e("div",ys,[e("div",fs,[e("div",hs,[e("nav",bs,[(r(!0),l(B,null,A(X.value,s=>(r(),l("button",{key:s.id,onClick:d=>J(s.id),class:E([w.value===s.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300","whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center"])},[s.id==="projects"?(r(),l("svg",_s,t[43]||(t[43]=[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]))):s.id==="tasks"?(r(),l("svg",ws,t[44]||(t[44]=[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"},null,-1)]))):s.id==="skills"?(r(),l("svg",Cs,t[45]||(t[45]=[e("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},null,-1)]))):s.id==="cv"?(r(),l("svg",zs,t[46]||(t[46]=[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z","clip-rule":"evenodd"},null,-1)]))):i("",!0),M(" "+n(s.name)+" ",1),s.count!==void 0?(r(),l("span",Vs,n(s.count),1)):i("",!0)],10,ks))),128))])]),e("div",Ms,[w.value==="projects"?(r(),l("div",$s,[_.value.length>0?(r(),l("div",Ts,[e("div",Bs,[e("h3",As," Progetti Assegnati ("+n(_.value.length)+") ",1),e("div",Es,n(_.value.filter(s=>s.status==="active").length)+" attivi ",1)]),e("div",Hs,[(r(!0),l(B,null,A(_.value,s=>(r(),l("div",{key:s.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer"},[e("div",Ls,[e("div",js,[e("h4",Ps,n(s.name),1),e("p",Ss,n(s.role||"Team Member"),1)]),e("span",{class:E(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ml-2",s.status==="active"?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":s.status==="completed"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":s.status==="on_hold"?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},n(s.status),3)]),e("div",Is,[s.client?(r(),l("div",Us,[t[47]||(t[47]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})],-1)),M(" "+n(s.client),1)])):i("",!0),s.deadline?(r(),l("div",Ns,[t[48]||(t[48]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})],-1)),M(" "+n(U(s.deadline)),1)])):i("",!0)])]))),128))])])):(r(),l("div",Ds,t[49]||(t[49]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun progetto",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"L'utente non è assegnato a nessun progetto.",-1)])))])):i("",!0),w.value==="tasks"?(r(),l("div",Os,[u.value.length>0?(r(),l("div",Fs,[e("div",Rs,[e("h3",qs," Task Assegnati ("+n(u.value.length)+") ",1),e("div",Xs,[e("span",null,n(u.value.filter(s=>s.status==="in-progress").length)+" in corso",1),e("span",null,n(u.value.filter(s=>s.status==="done").length)+" completati",1)])]),e("div",Js,[(r(!0),l(B,null,A(u.value,s=>(r(),l("div",{key:s.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all duration-200"},[e("div",Qs,[e("div",Gs,[e("h4",Ks,n(s.name),1),s.project_name?(r(),l("p",Ws,n(s.project_name),1)):i("",!0)]),e("div",Ys,[e("span",{class:E(["inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",s.priority==="urgent"?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":s.priority==="high"?"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200":s.priority==="medium"?"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200":"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"])},n(s.priority),3),e("span",{class:E(["inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",s.status==="done"?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":s.status==="in-progress"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":s.status==="review"?"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},n(s.status),3)])]),s.due_date?(r(),l("div",Zs,[t[50]||(t[50]=e("svg",{class:"w-4 h-4 mr-2",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z","clip-rule":"evenodd"})],-1)),M(" Scadenza: "+n(U(s.due_date)),1)])):i("",!0)]))),128))])])):(r(),l("div",ea,t[51]||(t[51]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun task",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non sono stati assegnati task a questo utente.",-1)])))])):i("",!0),w.value==="skills"?(r(),l("div",ta,[o.value.skills&&o.value.skills.length>0?(r(),l("div",sa,[e("div",aa,[e("div",ra,[e("h3",la," Competenze ("+n(o.value.skills.length)+") ",1),e("span",oa," Pagina "+n(v.value)+" di "+n(k.value),1)]),e("div",na,n(o.value.skills.filter(s=>s.certified).length)+" certificate ",1)]),e("div",ia,[(r(!0),l(B,null,A(I.value,s=>(r(),l("div",{key:s.id,class:"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"},[e("div",da,[e("h4",ua,n(s.name),1),s.certified?(r(),l("span",ca,"✓ Certificato")):i("",!0)]),s.category?(r(),l("p",va,n(s.category),1)):i("",!0),e("div",ga,[e("div",pa,[t[52]||(t[52]=e("span",{class:"text-sm text-gray-600 dark:text-gray-400"},"Livello:",-1)),e("div",ma,[(r(),l(B,null,A(5,d=>e("div",{key:d,class:E(["w-3 h-3 rounded-full",d<=s.proficiency_level?"bg-blue-500":"bg-gray-300 dark:bg-gray-600"])},null,2)),64))]),e("span",xa,"("+n(s.proficiency_level)+"/5)",1)]),s.years_experience?(r(),l("span",ya,n(s.years_experience)+n(s.years_experience===1?" anno":" anni"),1)):i("",!0)])]))),128))]),k.value>1?(r(),l("div",fa,[e("div",ha,[e("button",{onClick:t[9]||(t[9]=s=>v.value=Math.max(1,v.value-1)),disabled:v.value===1,class:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"}," Precedente ",8,ba),e("span",ka," Pagina "+n(v.value)+" di "+n(k.value),1),e("button",{onClick:t[10]||(t[10]=s=>v.value=Math.min(k.value,v.value+1)),disabled:v.value===k.value,class:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"}," Successiva ",8,_a)]),e("div",wa," Mostrando "+n((v.value-1)*y.value+1)+"-"+n(Math.min(v.value*y.value,o.value.skills.length))+" di "+n(o.value.skills.length)+" competenze ",1)])):i("",!0)])):(r(),l("div",Ca,t[53]||(t[53]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessuna competenza",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},"Non sono state registrate competenze per questo utente.",-1)])))])):i("",!0),w.value==="cv"?(r(),l("div",za,[ue(ct,{user:o.value,"can-edit":$.value,onUserUpdated:G},null,8,["user","can-edit"])])):i("",!0)])])])])):i("",!0)]))}};export{Ta as default};
