import{e as T}from"./vendor.js";import{u as c}from"./app.js";const g=T("timesheet",{state:()=>({stats:{weeklyHours:0,monthlyHours:0,pendingApprovals:0,efficiency:0},recentActivities:[],pendingApprovals:[],myStatus:{status:"draft",totalHours:0,billableHours:0},currentMonth:new Date().getMonth()+1,currentYear:new Date().getFullYear(),projectTasks:[],monthlyEntries:{},availableProjects:[],loading:{dashboard:!1,monthlyData:!1,saving:!1},error:null,lastFetch:{dashboard:null,monthlyData:null,projects:null}}),getters:{totalHours:t=>Object.values(t.monthlyEntries).reduce((e,r)=>e+Object.values(r).reduce((s,o)=>s+(o||0),0),0),billableHours:t=>Object.values(t.monthlyEntries).reduce((e,r)=>e+Object.values(r).reduce((s,o,a)=>{var n;const i=t.projectTasks[Math.floor(a/((n=t.daysInMonth)==null?void 0:n.length)||31)];return s+(i!=null&&i.billable&&o||0)},0),0),pendingHours:t=>t.myStatus.status==="submitted"?t.myStatus.totalHours:0,activeProjects:t=>t.projectTasks.length,daysInMonth:t=>{const e=new Date(t.currentYear,t.currentMonth,0).getDate();return Array.from({length:e},(r,s)=>s+1)},canApprove:()=>{var e,r;const t=c();return((e=t.user)==null?void 0:e.role)==="manager"||((r=t.user)==null?void 0:r.role)==="admin"},needsRefresh:t=>({dashboard:!t.lastFetch.dashboard||Date.now()-t.lastFetch.dashboard>3e5,monthlyData:!t.lastFetch.monthlyData||Date.now()-t.lastFetch.monthlyData>3e5,projects:!t.lastFetch.projects||Date.now()-t.lastFetch.projects>3e5})},actions:{async loadDashboardStats(){var t,e,r,s,o,a;if(!(this.loading.dashboard||!this.needsRefresh.dashboard)){this.loading.dashboard=!0,this.error=null;try{const i=c(),n=await fetch("/api/dashboard/stats",{headers:{"Content-Type":"application/json","X-CSRFToken":i.csrfToken}});if(!n.ok)throw new Error(`HTTP ${n.status}: ${n.statusText}`);const h=await n.json();this.stats={weeklyHours:((e=(t=h.data)==null?void 0:t.activities)==null?void 0:e.recent_timesheets)||0,monthlyHours:((s=(r=h.data)==null?void 0:r.activities)==null?void 0:s.recent_timesheets)||0,pendingApprovals:((a=(o=h.data)==null?void 0:o.activities)==null?void 0:a.unread_notifications)||0,efficiency:85},this.lastFetch.dashboard=Date.now()}catch(i){this.error=`Errore caricamento statistiche: ${i.message}`,console.error("Error loading dashboard stats:",i)}finally{this.loading.dashboard=!1}}},async loadRecentActivities(){try{const t=c(),e=await fetch("/api/timesheets/?per_page=5&page=1",{headers:{"Content-Type":"application/json","X-CSRFToken":t.csrfToken}});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const s=(await e.json()).data||[];this.recentActivities=s.map(o=>({id:o.id,description:`${o.project_name||"Progetto"} - ${o.task_name||"Task"}`,hours:o.hours,created_at:o.created_at,date:o.date}))}catch(t){console.error("Error loading recent activities:",t),this.recentActivities=[]}},async loadPendingApprovals(){if(this.canApprove)try{const t=c(),e=await fetch("/api/monthly-timesheets/?status=submitted",{headers:{"Content-Type":"application/json","X-CSRFToken":t.csrfToken}});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const r=await e.json();this.pendingApprovals=r.data||[]}catch(t){console.error("Error loading pending approvals:",t),this.pendingApprovals=[]}},async loadMyStatus(){var t,e,r;if(!this.canApprove)try{const s=c(),o=await fetch("/api/monthly-timesheets/generate",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":s.csrfToken},body:JSON.stringify({year:this.currentYear,month:this.currentMonth})});if(!o.ok)throw new Error(`HTTP ${o.status}: ${o.statusText}`);const a=await o.json();this.myStatus={status:((t=a.data)==null?void 0:t.status)||"draft",totalHours:((e=a.data)==null?void 0:e.total_hours)||0,billableHours:((r=a.data)==null?void 0:r.billable_hours)||0}}catch(s){console.error("Error loading my status:",s)}},async loadMonthlyData(t=this.currentYear,e=this.currentMonth){var r;if(this.loading.monthlyData||!this.needsRefresh.monthlyData&&t===this.currentYear&&e===this.currentMonth)return{entries:this.monthlyEntries,projects:this.projectTasks};this.loading.monthlyData=!0,this.error=null;try{const s=c(),o=new Date(t,e-1,1).toISOString().split("T")[0],a=new Date(t,e,0).toISOString().split("T")[0],i=((r=s.user)==null?void 0:r.id)||"",n=await fetch(`/api/timesheets?start_date=${o}&end_date=${a}&user_id=${i}`,{headers:{"Content-Type":"application/json","X-CSRFToken":s.csrfToken}});if(!n.ok)throw new Error(`HTTP ${n.status}: ${n.statusText}`);const h=await n.json();if(this.currentYear=t,this.currentMonth=e,!h.success)throw new Error(h.message||"API response indicates failure");const d=h.data||[],u={},p=new Set;return d.forEach(l=>{const m=l.date,f=`${l.project_id}-${l.task_id||"notask"}`;u[m]||(u[m]={}),u[m][f]||(u[m][f]=0),u[m][f]+=parseFloat(l.hours||0),p.add(JSON.stringify({id:f,project_id:l.project_id,task_id:l.task_id||null,project_name:l.project_name||"Progetto Sconosciuto",task_name:l.task_name||"Attività Generica",billable:l.billable||!1}))}),this.monthlyEntries=u,this.projectTasks=Array.from(p).map(l=>JSON.parse(l)),this.lastFetch.monthlyData=Date.now(),console.log("Loaded monthly data:",{entries:Object.keys(this.monthlyEntries).length,projects:this.projectTasks.length,totalTimesheets:d.length}),{entries:this.monthlyEntries,projects:this.projectTasks}}catch(s){return this.error=`Errore caricamento dati mensili: ${s.message}`,console.error("Error loading monthly data:",s),{entries:{},projects:[]}}finally{this.loading.monthlyData=!1}},async loadAvailableProjects(){if(this.availableProjects.length>0&&!this.needsRefresh.projects)return this.availableProjects;try{const t=c(),e=await fetch("/api/projects/",{headers:{"Content-Type":"application/json","X-CSRFToken":t.csrfToken}});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);const r=await e.json();let s=[];return r.data&&Array.isArray(r.data.projects)?s=r.data.projects:r.data&&Array.isArray(r.data.items)?s=r.data.items:r.data&&Array.isArray(r.data)?s=r.data:Array.isArray(r)&&(s=r),this.availableProjects=s.filter(o=>o.status==="active"||!o.status),this.lastFetch.projects=Date.now(),console.log("Loaded available projects:",this.availableProjects.length,"projects"),this.availableProjects}catch(t){return console.error("Error loading available projects:",t),this.availableProjects=[],[]}},async saveEntry(t,e,r){this.loading.saving=!0;try{const s=c(),[o,a]=t.split("-"),i=`${this.currentYear}-${this.currentMonth.toString().padStart(2,"0")}-${e.toString().padStart(2,"0")}`,n={date:i,project_id:parseInt(o),task_id:a!=="notask"?parseInt(a):null,hours:parseFloat(r),description:`Lavoro del ${i}`},h=await fetch("/api/timesheets",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":s.csrfToken},body:JSON.stringify(n)});if(!h.ok)throw new Error(`HTTP ${h.status}: ${h.statusText}`);const d=await h.json();if(!d.success)throw new Error(d.message||"Errore durante il salvataggio");const u=i;return this.monthlyEntries[u]||(this.monthlyEntries[u]={}),this.monthlyEntries[u][t]=parseFloat(r)||0,console.log("Saved timesheet entry:",{date:i,project:o,task:a,hours:r,success:!0}),!0}catch(s){return this.error=`Errore salvataggio ore: ${s.message}`,console.error("Error saving entry:",s),!1}finally{this.loading.saving=!1}},async addProjectToTimesheet(t,e){try{const r=c(),s=await fetch("/api/timesheet-projects/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":r.csrfToken},body:JSON.stringify({project_id:t,task_id:e})});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);return await this.loadMonthlyData(this.currentYear,this.currentMonth),!0}catch(r){return this.error=`Errore aggiunta progetto: ${r.message}`,console.error("Error adding project to timesheet:",r),!1}},async approveTimesheet(t){try{const e=c(),r=await fetch(`/api/monthly-timesheets/${t}/approve`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":e.csrfToken}});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);return await this.loadPendingApprovals(),!0}catch(e){return this.error=`Errore approvazione: ${e.message}`,console.error("Error approving timesheet:",e),!1}},navigateMonth(t){t==="next"?this.currentMonth===12?(this.currentMonth=1,this.currentYear++):this.currentMonth++:this.currentMonth===1?(this.currentMonth=12,this.currentYear--):this.currentMonth--,this.loadMonthlyData(this.currentYear,this.currentMonth)},async refreshAll(){this.lastFetch={dashboard:null,monthlyData:null,projects:null},await Promise.all([this.loadDashboardStats(),this.loadRecentActivities(),this.loadPendingApprovals(),this.loadMyStatus(),this.loadMonthlyData()])},async loadPendingTimesheets(t={}){try{const e=c(),r=new URLSearchParams({month:t.month||new Date().getMonth()+1,year:t.year||new Date().getFullYear()});t.status&&r.append("status",t.status),t.user_id&&r.append("user_id",t.user_id),t.search&&r.append("search",t.search),t.anomalies_only&&r.append("anomalies_only","true");const s=await fetch(`/api/monthly-timesheets/?${r}`,{headers:{"Content-Type":"application/json","X-CSRFToken":e.csrfToken}});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);return(await s.json()).data||[]}catch(e){return this.error=`Errore caricamento timesheet: ${e.message}`,console.error("Error loading pending timesheets:",e),[]}},async rejectTimesheet(t,e){try{const r=c(),s=await fetch(`/api/monthly-timesheets/${t}/reject`,{method:"PUT",headers:{"Content-Type":"application/json","X-CSRFToken":r.csrfToken},body:JSON.stringify({reason:e})});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);return!0}catch(r){return this.error=`Errore rifiuto timesheet: ${r.message}`,console.error("Error rejecting timesheet:",r),!1}},async loadAnalyticsData(t={}){try{const e=c(),r=new URLSearchParams;t.start_date&&r.append("start_date",t.start_date),t.end_date&&r.append("end_date",t.end_date),t.department_id&&r.append("department_id",t.department_id),t.project_id&&r.append("project_id",t.project_id);const s=await fetch(`/api/timesheets/?${r}`,{headers:{"Content-Type":"application/json","X-CSRFToken":e.csrfToken}});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);const a=(await s.json()).data||[],i={};return a.forEach(n=>{var d,u,p,l;const h=n.user_id||((d=n.user)==null?void 0:d.id)||"unknown";i[h]||(i[h]={id:h,full_name:((u=n.user)==null?void 0:u.full_name)||((p=n.user)==null?void 0:p.name)||`User ${h}`,department:((l=n.user)==null?void 0:l.department)||"N/A",total_hours:0,billable_hours:0,projects:new Set,entries:[]}),i[h].total_hours+=n.hours,n.billable&&(i[h].billable_hours+=n.hours),n.project_id&&i[h].projects.add(n.project_id),i[h].entries.push(n)}),Object.values(i).map(n=>({...n,active_projects:n.projects.size,productivity:n.total_hours>0?Math.round(n.billable_hours/n.total_hours*100):0,revenue:Math.round(n.billable_hours*50)}))}catch(e){return this.error=`Errore caricamento analytics: ${e.message}`,console.error("Error loading analytics data:",e),[]}},async loadTeamMembers(){var t;try{const e=c(),r=await fetch("/api/personnel/users",{headers:{"Content-Type":"application/json","X-CSRFToken":e.csrfToken}});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const s=await r.json();return((t=s.data)==null?void 0:t.users)||s.data||[]}catch(e){return this.error=`Errore caricamento team: ${e.message}`,console.error("Error loading team members:",e),[]}},async loadDepartments(){var t;try{const e=c(),r=await fetch("/api/personnel/departments",{headers:{"Content-Type":"application/json","X-CSRFToken":e.csrfToken}});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const s=await r.json();return((t=s.data)==null?void 0:t.departments)||s.data||[]}catch(e){return console.error("Error loading departments:",e),[]}},async bulkApproveTimesheets(t){try{const e=c(),r=await Promise.allSettled(t.map(a=>this.approveTimesheet(a))),s=r.filter(a=>a.status==="fulfilled"&&a.value).length,o=r.length-s;return o>0&&(this.error=`${s} approvati, ${o} falliti`),{successful:s,failed:o}}catch(e){return this.error=`Errore approvazione multipla: ${e.message}`,{successful:0,failed:t.length}}},async bulkRejectTimesheets(t,e){try{const r=await Promise.allSettled(t.map(a=>this.rejectTimesheet(a,e))),s=r.filter(a=>a.status==="fulfilled"&&a.value).length,o=r.length-s;return o>0&&(this.error=`${s} rifiutati, ${o} falliti`),{successful:s,failed:o}}catch(r){return this.error=`Errore rifiuto multiplo: ${r.message}`,{successful:0,failed:t.length}}},async exportTimesheetData(t={}){try{const e=c(),r=new URLSearchParams(t),s=await fetch(`/api/timesheets/export?${r}`,{headers:{"Content-Type":"application/json","X-CSRFToken":e.csrfToken}});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);const o=await s.blob(),a=window.URL.createObjectURL(o),i=document.createElement("a");return i.href=a,i.download=`timesheet_export_${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(i),i.click(),window.URL.revokeObjectURL(a),document.body.removeChild(i),!0}catch(e){return this.error=`Errore export: ${e.message}`,console.error("Error exporting data:",e),!1}},async loadTimesheetHistory(t={}){try{const e=c(),r=new URLSearchParams;Object.keys(t).forEach(a=>{t[a]&&r.append(a,t[a])});const s=await fetch(`/api/timesheets/?${r}`,{headers:{"Content-Type":"application/json","X-CSRFToken":e.csrfToken}});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);return(await s.json()).data||[]}catch(e){return this.error=`Errore caricamento storico: ${e.message}`,console.error("Error loading timesheet history:",e),[]}},async loadAnalyticsData(t={}){try{const e=c(),r=new URLSearchParams;Object.keys(t).forEach(a=>{t[a]&&r.append(a,t[a])});const s=await fetch(`/api/timesheets/analytics/?${r}`,{headers:{"Content-Type":"application/json","X-CSRFToken":e.csrfToken}});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);return(await s.json()).data||[]}catch(e){return this.error=`Errore caricamento analytics: ${e.message}`,console.error("Error loading analytics data:",e),[]}},async loadDepartments(){try{const t=c(),e=await fetch("/api/personnel/departments/",{headers:{"Content-Type":"application/json","X-CSRFToken":t.csrfToken}});if(!e.ok)throw new Error(`HTTP ${e.status}: ${e.statusText}`);return(await e.json()).data||[]}catch(t){return console.error("Error loading departments:",t),[]}},async exportTimesheetData(t={}){try{const e=c(),r=new URLSearchParams;Object.keys(t).forEach(n=>{t[n]&&r.append(n,t[n])});const s=await fetch(`/api/timesheets/export/?${r}`,{headers:{"Content-Type":"application/json","X-CSRFToken":e.csrfToken}});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);const o=await s.blob(),a=window.URL.createObjectURL(o),i=document.createElement("a");return i.href=a,i.download=`timesheet-export-${new Date().toISOString().split("T")[0]}.xlsx`,document.body.appendChild(i),i.click(),document.body.removeChild(i),window.URL.revokeObjectURL(a),!0}catch(e){return this.error=`Errore esportazione: ${e.message}`,console.error("Error exporting timesheet data:",e),!1}},async loadTimeOffRequests(t={}){try{const e=c(),r=new URLSearchParams;Object.keys(t).forEach(a=>{t[a]&&r.append(a,t[a])});const s=await fetch(`/api/time-off-requests/?${r}`,{headers:{"Content-Type":"application/json","X-CSRFToken":e.csrfToken}});if(!s.ok)throw new Error(`HTTP ${s.status}: ${s.statusText}`);return(await s.json()).data||[]}catch(e){return this.error=`Errore caricamento richieste: ${e.message}`,console.error("Error loading time off requests:",e),[]}},async createTimeOffRequest(t){try{const e=c(),r=await fetch("/api/time-off-requests/",{method:"POST",headers:{"Content-Type":"application/json","X-CSRFToken":e.csrfToken},body:JSON.stringify(t)});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);return!0}catch(e){return this.error=`Errore creazione richiesta: ${e.message}`,console.error("Error creating time off request:",e),!1}},async deleteTimeOffRequest(t){try{const e=c(),r=await fetch(`/api/time-off-requests/${t}`,{method:"DELETE",headers:{"Content-Type":"application/json","X-CSRFToken":e.csrfToken}});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);return!0}catch(e){return this.error=`Errore eliminazione richiesta: ${e.message}`,console.error("Error deleting time off request:",e),!1}},clearError(){this.error=null}}});export{g as u};
