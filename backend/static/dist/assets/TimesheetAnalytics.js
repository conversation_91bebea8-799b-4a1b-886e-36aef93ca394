import{r as d,f as E,A as I,c as i,g as L,j as t,t as a,m as v,v as f,G as _,H as N,F as M,k as j,n as p,o as u,z as O}from"./vendor.js";import{u as G}from"./timesheet.js";import"./app.js";const Q={class:"space-y-6"},$={key:0,class:"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4"},J={class:"flex"},K={class:"ml-3"},W={class:"text-sm text-red-800 dark:text-red-200"},X={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Z={class:"flex items-center justify-between"},tt={class:"flex items-center space-x-3"},et=["disabled"],rt=["disabled"],st={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},at={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},ot=["value"],lt=["value"],nt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},dt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},it={class:"flex items-center"},ut={class:"ml-4"},gt={class:"text-2xl font-semibold text-gray-900 dark:text-white"},ct={class:"text-xs text-gray-500 dark:text-gray-400"},xt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},vt={class:"flex items-center"},pt={class:"ml-4"},mt={class:"text-2xl font-semibold text-gray-900 dark:text-white"},yt={class:"text-xs text-gray-500 dark:text-gray-400"},ht={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},kt={class:"flex items-center"},bt={class:"ml-4"},wt={class:"text-2xl font-semibold text-gray-900 dark:text-white"},ft={class:"text-xs text-gray-500 dark:text-gray-400"},_t={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ct={class:"flex items-center"},Dt={class:"ml-4"},Mt={class:"text-2xl font-semibold text-gray-900 dark:text-white"},jt={class:"text-xs text-gray-500 dark:text-gray-400"},zt={class:"grid grid-cols-1 lg:grid-cols-2 gap-6"},Pt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Tt={class:"flex items-center justify-between mb-4"},Ft={class:"flex items-center space-x-2"},Ht={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},At={class:"overflow-x-auto"},St={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Bt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Yt={key:0},Vt={key:1},Rt={class:"px-6 py-4 whitespace-nowrap"},Ut={class:"flex items-center"},qt={class:"flex-shrink-0 h-10 w-10"},Et={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},It={class:"text-sm font-medium text-gray-700 dark:text-gray-300"},Lt={class:"ml-4"},Nt={class:"text-sm font-medium text-gray-900 dark:text-white"},Ot={class:"text-sm text-gray-500 dark:text-gray-400"},Gt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Qt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},$t={class:"text-xs text-gray-500 dark:text-gray-400"},Jt={class:"px-6 py-4 whitespace-nowrap"},Kt={class:"flex items-center"},Wt={class:"flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2"},Xt={class:"text-sm text-gray-900 dark:text-white"},Zt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},te={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},oe={__name:"TimesheetAnalytics",setup(ee){const c=G(),x=d(!1),z=d([]),P=d([]),g=d([]),h=d("month"),C=d("current_month"),k=d(""),b=d(""),D=d("productivity"),o=d({totalHours:0,totalHoursChange:0,avgProductivity:0,productivityChange:0,revenue:0,revenueChange:0,utilization:0,utilizationChange:0}),T=E(()=>c.error),F=()=>{const s=new Date;let e,r;switch(C.value){case"current_month":e=new Date(s.getFullYear(),s.getMonth(),1),r=new Date(s.getFullYear(),s.getMonth()+1,0);break;case"last_month":e=new Date(s.getFullYear(),s.getMonth()-1,1),r=new Date(s.getFullYear(),s.getMonth(),0);break;case"current_quarter":const w=Math.floor(s.getMonth()/3);e=new Date(s.getFullYear(),w*3,1),r=new Date(s.getFullYear(),(w+1)*3,0);break;case"last_quarter":const y=Math.floor(s.getMonth()/3)-1,l=y<0?s.getFullYear()-1:s.getFullYear(),n=y<0?3:y;e=new Date(l,n*3,1),r=new Date(l,(n+1)*3,0);break;case"current_year":e=new Date(s.getFullYear(),0,1),r=new Date(s.getFullYear(),11,31);break;default:e=new Date(s.getFullYear(),s.getMonth(),1),r=new Date(s.getFullYear(),s.getMonth()+1,0)}return{startDate:e.toISOString().split("T")[0],endDate:r.toISOString().split("T")[0]}},A=()=>{const s=g.value.reduce((l,n)=>l+n.total_hours,0),e=g.value.reduce((l,n)=>l+n.billable_hours,0),r=g.value.reduce((l,n)=>l+n.revenue,0),w=g.value.length>0?g.value.reduce((l,n)=>l+n.productivity,0)/g.value.length:0,y=s>0?e/s*100:0;o.value={totalHours:Math.round(s),totalHoursChange:0,avgProductivity:Math.round(w),productivityChange:0,revenue:Math.round(r),revenueChange:0,utilization:Math.round(y),utilizationChange:0}},m=async()=>{x.value=!0;try{await Promise.all([S(),B(),Y()]),A()}finally{x.value=!1}},S=async()=>{const{startDate:s,endDate:e}=F(),r={start_date:s,end_date:e,department_id:k.value,project_id:b.value};g.value=await c.loadAnalyticsData(r)},B=async()=>{z.value=await c.loadDepartments()},Y=async()=>{P.value=c.availableProjects.length>0?c.availableProjects:await c.loadAvailableProjects()},V=async()=>{const{startDate:s,endDate:e}=F(),r={start_date:s,end_date:e,department_id:k.value,project_id:b.value,analysis_type:D.value};await c.exportTimesheetData(r)},R=s=>s?s.split(" ").map(e=>e[0]).join("").toUpperCase():"??",U=s=>s>=80?"bg-green-500":s>=60?"bg-yellow-500":"bg-red-500",H=s=>new Intl.NumberFormat("it-IT").format(s),q=()=>{c.clearError()};return I(()=>{m()}),(s,e)=>(u(),i("div",Q,[T.value?(u(),i("div",$,[t("div",J,[e[7]||(e[7]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-5 w-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),t("div",K,[t("p",W,a(T.value),1)]),t("div",{class:"ml-auto pl-3"},[t("div",{class:"-mx-1.5 -my-1.5"},[t("button",{onClick:q,class:"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900"},e[6]||(e[6]=[t("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])])])])):L("",!0),t("div",X,[t("div",Z,[e[10]||(e[10]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Analytics Timesheet"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Report avanzati e analisi delle performance del team ")],-1)),t("div",tt,[t("button",{onClick:V,disabled:x.value,class:"bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},[e[8]||(e[8]=t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1)),v(" "+a(x.value?"Esportando...":"Esporta Report"),1)],8,et),t("button",{onClick:m,disabled:x.value,class:"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},[e[9]||(e[9]=t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1)),v(" "+a(x.value?"Caricando...":"Aggiorna"),1)],8,rt)])])]),t("div",st,[t("div",at,[t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Periodo",-1)),f(t("select",{"onUpdate:modelValue":e[0]||(e[0]=r=>C.value=r),onChange:m,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},e[11]||(e[11]=[N('<option value="current_month">Mese Corrente</option><option value="last_month">Mese Scorso</option><option value="current_quarter">Trimestre Corrente</option><option value="last_quarter">Trimestre Scorso</option><option value="current_year">Anno Corrente</option>',5)]),544),[[_,C.value]])]),t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Dipartimento",-1)),f(t("select",{"onUpdate:modelValue":e[1]||(e[1]=r=>k.value=r),onChange:m,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[e[13]||(e[13]=t("option",{value:""},"Tutti",-1)),(u(!0),i(M,null,j(z.value,r=>(u(),i("option",{key:r.id,value:r.id},a(r.name),9,ot))),128))],544),[[_,k.value]])]),t("div",null,[e[16]||(e[16]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Progetto",-1)),f(t("select",{"onUpdate:modelValue":e[2]||(e[2]=r=>b.value=r),onChange:m,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[e[15]||(e[15]=t("option",{value:""},"Tutti",-1)),(u(!0),i(M,null,j(P.value,r=>(u(),i("option",{key:r.id,value:r.id},a(r.name),9,lt))),128))],544),[[_,b.value]])]),t("div",null,[e[18]||(e[18]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Tipo Analisi",-1)),f(t("select",{"onUpdate:modelValue":e[3]||(e[3]=r=>D.value=r),onChange:m,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},e[17]||(e[17]=[t("option",{value:"productivity"},"Produttività",-1),t("option",{value:"utilization"},"Utilizzo",-1),t("option",{value:"billing"},"Fatturazione",-1),t("option",{value:"trends"},"Trend",-1)]),544),[[_,D.value]])])])]),t("div",nt,[t("div",dt,[t("div",it,[e[21]||(e[21]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",ut,[e[20]||(e[20]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Ore Totali",-1)),t("p",gt,a(o.value.totalHours)+"h",1),t("p",ct,[t("span",{class:p(o.value.totalHoursChange>=0?"text-green-600":"text-red-600")},a(o.value.totalHoursChange>=0?"+":"")+a(o.value.totalHoursChange)+"% ",3),e[19]||(e[19]=v(" vs periodo precedente "))])])])]),t("div",xt,[t("div",vt,[e[24]||(e[24]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])])],-1)),t("div",pt,[e[23]||(e[23]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Produttività Media",-1)),t("p",mt,a(o.value.avgProductivity)+"%",1),t("p",yt,[t("span",{class:p(o.value.productivityChange>=0?"text-green-600":"text-red-600")},a(o.value.productivityChange>=0?"+":"")+a(o.value.productivityChange)+"% ",3),e[22]||(e[22]=v(" vs periodo precedente "))])])])]),t("div",ht,[t("div",kt,[e[27]||(e[27]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})])])],-1)),t("div",bt,[e[26]||(e[26]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Revenue Generato",-1)),t("p",wt,"€"+a(H(o.value.revenue)),1),t("p",ft,[t("span",{class:p(o.value.revenueChange>=0?"text-green-600":"text-red-600")},a(o.value.revenueChange>=0?"+":"")+a(o.value.revenueChange)+"% ",3),e[25]||(e[25]=v(" vs periodo precedente "))])])])]),t("div",_t,[t("div",Ct,[e[30]||(e[30]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"})])])],-1)),t("div",Dt,[e[29]||(e[29]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Utilizzo Risorse",-1)),t("p",Mt,a(o.value.utilization)+"%",1),t("p",jt,[t("span",{class:p(o.value.utilizationChange>=0?"text-green-600":"text-red-600")},a(o.value.utilizationChange>=0?"+":"")+a(o.value.utilizationChange)+"% ",3),e[28]||(e[28]=v(" vs periodo precedente "))])])])])]),t("div",zt,[t("div",Pt,[t("div",Tt,[e[31]||(e[31]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Trend Produttività",-1)),t("div",Ft,[t("button",{onClick:e[4]||(e[4]=r=>h.value="week"),class:p([h.value==="week"?"bg-blue-100 text-blue-700":"text-gray-500","px-3 py-1 rounded text-sm"])}," Settimana ",2),t("button",{onClick:e[5]||(e[5]=r=>h.value="month"),class:p([h.value==="month"?"bg-blue-100 text-blue-700":"text-gray-500","px-3 py-1 rounded text-sm"])}," Mese ",2)])]),e[32]||(e[32]=t("div",{class:"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded"},[t("p",{class:"text-gray-500 dark:text-gray-400"},"Grafico Produttività (Chart.js da implementare)")],-1))]),e[33]||(e[33]=t("div",{class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Performance Team"),t("div",{class:"h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded"},[t("p",{class:"text-gray-500 dark:text-gray-400"},"Grafico Performance Team (Chart.js da implementare)")])],-1))]),t("div",Ht,[e[37]||(e[37]=t("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Analisi Dettagliata per Dipendente")],-1)),t("div",At,[t("table",St,[e[36]||(e[36]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendente "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Lavorate "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Fatturabili "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Produttività "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Revenue "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Progetti Attivi ")])],-1)),t("tbody",Bt,[x.value?(u(),i("tr",Yt,e[34]||(e[34]=[t("td",{colspan:"6",class:"px-6 py-4 text-center text-gray-500 dark:text-gray-400"}," Caricamento dati analytics... ",-1)]))):g.value.length===0?(u(),i("tr",Vt,e[35]||(e[35]=[t("td",{colspan:"6",class:"px-6 py-4 text-center text-gray-500 dark:text-gray-400"}," Nessun dato disponibile per il periodo selezionato ",-1)]))):(u(!0),i(M,{key:2},j(g.value,r=>(u(),i("tr",{key:r.id},[t("td",Rt,[t("div",Ut,[t("div",qt,[t("div",Et,[t("span",It,a(R(r.full_name)),1)])]),t("div",Lt,[t("div",Nt,a(r.full_name),1),t("div",Ot,a(r.department),1)])])]),t("td",Gt,a(r.total_hours)+"h ",1),t("td",Qt,[v(a(r.billable_hours)+"h ",1),t("span",$t," ("+a(Math.round(r.billable_hours/r.total_hours*100))+"%) ",1)]),t("td",Jt,[t("div",Kt,[t("div",Wt,[t("div",{class:p(["h-2 rounded-full",U(r.productivity)]),style:O({width:r.productivity+"%"})},null,6)]),t("span",Xt,a(r.productivity)+"%",1)])]),t("td",Zt," €"+a(H(r.revenue)),1),t("td",te,a(r.active_projects),1)]))),128))])])])])]))}};export{oe as default};
