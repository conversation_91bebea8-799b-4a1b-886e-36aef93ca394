import{r as i,f as c,A as T,w as G,c as l,g as b,j as t,t as a,v as m,G as V,x as w,F as J,k as K,H as Q,s as j,o as n,n as A,p as C}from"./vendor.js";import{u as X}from"./timesheet.js";import{b as Y,c as Z,d as M}from"./timesheet2.js";import"./app.js";const tt={class:"space-y-6"},et={key:0,class:"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4"},st={class:"flex"},rt={class:"ml-3"},at={class:"text-sm text-red-800 dark:text-red-200"},dt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ot={class:"flex justify-between items-center"},it={class:"flex space-x-3"},lt=["disabled"],nt=["disabled"],ut=["disabled"],gt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},ct={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},mt={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},xt={class:"overflow-x-auto"},pt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},yt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},vt={class:"px-6 py-4 whitespace-nowrap"},kt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},ft={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},ht={class:"px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate"},bt={class:"px-6 py-4 whitespace-nowrap"},wt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},_t={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Rt=["onClick"],Ct=["onClick"],Mt={key:2,class:"text-gray-400 dark:text-gray-500"},St={key:0,class:"text-center py-8"},Tt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Vt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},jt={class:"flex items-center"},At={class:"ml-5 w-0 flex-1"},Dt={class:"text-lg font-medium text-gray-900 dark:text-white"},zt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Bt={class:"flex items-center"},Ft={class:"ml-5 w-0 flex-1"},Nt={class:"text-lg font-medium text-gray-900 dark:text-white"},$t={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},qt={class:"flex items-center"},Lt={class:"ml-5 w-0 flex-1"},Pt={class:"text-lg font-medium text-gray-900 dark:text-white"},Ut={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Wt={class:"flex items-center"},Ht={class:"ml-5 w-0 flex-1"},It={class:"text-lg font-medium text-gray-900 dark:text-white"},Et={class:"mt-3"},Ot={class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},Gt={class:"grid grid-cols-1 gap-4"},Jt={class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},Kt=["required","placeholder"],Qt={class:"flex justify-end space-x-3 mt-6"},Xt=["disabled"],re={__name:"TimesheetRequests",setup(Yt){const x=X(),d=i([]),p=i(!1),y=i(!1),v=i(!1),u=i(""),k=i(""),f=i(""),h=i(""),o=i({start_date:"",end_date:"",notes:""}),S=c(()=>x.error),D=c(()=>20),z=c(()=>Array.isArray(d.value)?d.value.filter(s=>s.request_type==="leave"&&s.status==="approved").reduce((s,e)=>s+(e.duration_days||0),0):0),B=c(()=>26),F=c(()=>Array.isArray(d.value)?d.value.filter(s=>s.request_type==="smartworking"&&s.status==="approved").reduce((s,e)=>s+(e.duration_days||0),0):0),N=c(()=>Array.isArray(d.value)?d.value.filter(s=>s.status==="pending").length:0),g=async()=>{p.value=!0;try{const s={request_type:k.value,status:f.value,start_date:h.value};d.value=await x.loadTimeOffRequests(s)}finally{p.value=!1}},_=s=>{u.value=s,o.value={start_date:"",end_date:"",notes:""},y.value=!0},R=()=>{y.value=!1,u.value=""},$=()=>{switch(u.value){case"vacation":return"Richiesta Ferie";case"leave":return"Richiesta Permesso";case"smart_working":return"Richiesta Smart Working";default:return"Nuova Richiesta"}},q=async()=>{v.value=!0;try{const s={request_type:u.value,...o.value};await x.createTimeOffRequest(s)&&(await g(),R())}finally{v.value=!1}},L=s=>{u.value=s.request_type,o.value={start_date:s.start_date,end_date:s.end_date,notes:s.notes||""},y.value=!0},P=async s=>{if(!confirm("Sei sicuro di voler eliminare questa richiesta?"))return;await x.deleteTimeOffRequest(s)&&await g()},U=s=>`${M(s.start_date)} - ${M(s.end_date)}`,W=s=>`${s.duration_days||0} giorni`,H=s=>{switch(s){case"vacation":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"leave":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"smartworking":return"bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"}},I=s=>{switch(s){case"vacation":return"Ferie";case"leave":return"Permesso";case"smartworking":return"Smart Working";default:return"Altro"}},E=()=>{x.clearError()};return T(()=>{g()}),G([k,f,h],()=>{g()}),T(()=>{g()}),(s,e)=>(n(),l("div",tt,[S.value?(n(),l("div",et,[t("div",st,[e[11]||(e[11]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-5 w-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),t("div",rt,[t("p",at,a(S.value),1)]),t("div",{class:"ml-auto pl-3"},[t("div",{class:"-mx-1.5 -my-1.5"},[t("button",{onClick:E,class:"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900"},e[10]||(e[10]=[t("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])])])])):b("",!0),t("div",dt,[t("div",ot,[e[12]||(e[12]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Richieste"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Gestisci le tue richieste di ferie, permessi e smart working ")],-1)),t("div",it,[t("button",{onClick:e[0]||(e[0]=r=>_("vacation")),disabled:p.value,class:"bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium"}," Richiedi Ferie ",8,lt),t("button",{onClick:e[1]||(e[1]=r=>_("leave")),disabled:p.value,class:"bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-4 py-2 rounded-md text-sm font-medium"}," Richiedi Permesso ",8,nt),t("button",{onClick:e[2]||(e[2]=r=>_("smartworking")),disabled:p.value,class:"bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-4 py-2 rounded-md text-sm font-medium"}," Smart Working ",8,ut)])])]),t("div",gt,[t("div",ct,[t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tipo Richiesta ",-1)),m(t("select",{"onUpdate:modelValue":e[3]||(e[3]=r=>k.value=r),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},e[13]||(e[13]=[t("option",{value:""},"Tutti i tipi",-1),t("option",{value:"vacation"},"Ferie",-1),t("option",{value:"leave"},"Permessi",-1),t("option",{value:"smartworking"},"Smart Working",-1)]),512),[[V,k.value]])]),t("div",null,[e[16]||(e[16]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Stato ",-1)),m(t("select",{"onUpdate:modelValue":e[4]||(e[4]=r=>f.value=r),class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},e[15]||(e[15]=[t("option",{value:""},"Tutti gli stati",-1),t("option",{value:"pending"},"In Attesa",-1),t("option",{value:"approved"},"Approvato",-1),t("option",{value:"rejected"},"Rifiutato",-1)]),512),[[V,f.value]])]),t("div",null,[e[17]||(e[17]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Da Data ",-1)),m(t("input",{"onUpdate:modelValue":e[5]||(e[5]=r=>h.value=r),type:"date",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[w,h.value]])]),t("div",{class:"flex items-end"},[t("button",{onClick:g,class:"w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Filtra ")])])]),t("div",mt,[e[20]||(e[20]=t("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Le Mie Richieste ")],-1)),t("div",xt,[t("table",pt,[e[18]||(e[18]=t("thead",{class:"bg-gray-50 dark:bg-gray-700"},[t("tr",null,[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Tipo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Periodo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Durata "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Motivo "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato "),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Richiesta il "),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ")])],-1)),t("tbody",yt,[(n(!0),l(J,null,K(d.value,r=>(n(),l("tr",{key:r.id},[t("td",vt,[t("span",{class:A(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",H(r.request_type)])},a(I(r.request_type)),3)]),t("td",kt,a(U(r)),1),t("td",ft,a(W(r)),1),t("td",ht,a(r.notes||"N/A"),1),t("td",bt,[t("span",{class:A(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",C(Y)(r.status)])},a(C(Z)(r.status)),3)]),t("td",wt,a(C(M)(r.created_at)),1),t("td",_t,[r.status==="pending"?(n(),l("button",{key:0,onClick:O=>L(r),class:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3"}," Modifica ",8,Rt)):b("",!0),r.status==="pending"?(n(),l("button",{key:1,onClick:O=>P(r.id),class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"}," Elimina ",8,Ct)):(n(),l("span",Mt,a(r.status==="approved"?"Approvata":"Rifiutata"),1))])]))),128))])]),d.value.length===0?(n(),l("div",St,e[19]||(e[19]=[Q('<div class="mx-auto h-12 w-12 text-gray-400"><svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg></div><h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessuna richiesta</h3><p class="mt-1 text-sm text-gray-500 dark:text-gray-400"> Non hai ancora effettuato richieste per il periodo selezionato. </p>',3)]))):b("",!0)])]),t("div",Tt,[t("div",Vt,[t("div",jt,[e[22]||(e[22]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])])],-1)),t("div",At,[t("dl",null,[e[21]||(e[21]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ferie Rimanenti ",-1)),t("dd",Dt,a(D.value)+" giorni ",1)])])])]),t("div",zt,[t("div",Bt,[e[24]||(e[24]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",Ft,[t("dl",null,[e[23]||(e[23]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Permessi Usati ",-1)),t("dd",Nt,a(z.value)+" / "+a(B.value)+" giorni ",1)])])])]),t("div",$t,[t("div",qt,[e[26]||(e[26]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2v0a2 2 0 012-2h6.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H19a2 2 0 012 2v0a2 2 0 00-2-2z"})])])],-1)),t("div",Lt,[t("dl",null,[e[25]||(e[25]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Smart Working ",-1)),t("dd",Pt,a(F.value)+" giorni ",1)])])])]),t("div",Ut,[t("div",Wt,[e[28]||(e[28]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",Ht,[t("dl",null,[e[27]||(e[27]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," In Attesa ",-1)),t("dd",It,a(N.value),1)])])])])]),y.value?(n(),l("div",{key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:R},[t("div",{class:"relative top-20 mx-auto p-5 border w-[500px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:e[9]||(e[9]=j(()=>{},["stop"]))},[t("div",Et,[t("h3",Ot,a($()),1),t("form",{onSubmit:j(q,["prevent"])},[t("div",Gt,[t("div",null,[e[29]||(e[29]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data Inizio ",-1)),m(t("input",{"onUpdate:modelValue":e[6]||(e[6]=r=>o.value.start_date=r),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[w,o.value.start_date]])]),t("div",null,[e[30]||(e[30]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Data Fine ",-1)),m(t("input",{"onUpdate:modelValue":e[7]||(e[7]=r=>o.value.end_date=r),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[w,o.value.end_date]])]),t("div",null,[t("label",Jt,a(u.value==="smartworking"?"Note (opzionale)":"Motivo"),1),m(t("textarea",{"onUpdate:modelValue":e[8]||(e[8]=r=>o.value.notes=r),rows:"3",required:u.value!=="smartworking",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500",placeholder:u.value==="smartworking"?"Note aggiuntive...":"Descrivi il motivo della richiesta..."},null,8,Kt),[[w,o.value.notes]])])]),t("div",Qt,[t("button",{type:"button",onClick:R,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),t("button",{type:"submit",disabled:v.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},a(v.value?"Invio...":"Invia Richiesta"),9,Xt)])],32)])])])):b("",!0)]))}};export{re as default};
