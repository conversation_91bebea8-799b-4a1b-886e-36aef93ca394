import{r as z,f as R,b as J,c as l,o as s,j as e,g as p,n as O,t as d,F as N,k as V,h as K,m as B,w as X,A as ee,q as W,Q as te,H as re,v as I,x as ae,G as Q,a as Y,i as se,l as le}from"./vendor.js";import{_ as P}from"./app.js";const oe={class:"department-node"},ne={class:"department-header bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm p-4 mb-4 min-w-80"},ie={class:"flex items-center justify-between"},de={class:"flex-1"},ue={class:"flex items-center"},ce={class:"flex items-center"},ge={class:"text-lg font-semibold text-gray-900 dark:text-white"},me={key:0,class:"text-sm text-gray-500 dark:text-gray-400"},ve={class:"flex items-center mt-1 space-x-4 text-xs text-gray-500 dark:text-gray-400"},pe={key:0},xe={key:0,class:"ml-4 flex items-center"},ye={class:"text-right mr-3"},he={class:"text-sm font-medium text-gray-900 dark:text-white"},be={key:0,class:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"},fe={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3"},ke=["onClick"],we={class:"flex-1 min-w-0"},Ce={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},_e={class:"text-xs text-gray-500 dark:text-gray-400 truncate"},ze={key:0,class:"ml-2"},$e={key:0,class:"mt-3 text-center"},Me={key:0,class:"ml-8 space-y-4"},De={class:"relative"},U=6,Ee={__name:"DepartmentNode",props:{department:{type:Object,required:!0},expanded:{type:Set,required:!0},searchQuery:{type:String,default:""}},emits:["toggle-node","employee-click"],setup(i,{emit:j}){const C=i,E=j,k=z(!1),x=R(()=>C.expanded.has(C.department.id)),o=R(()=>k.value||C.department.employees.length<=U?C.department.employees:C.department.employees.slice(0,U)),_=()=>{E("toggle-node",C.department.id)},g=w=>new Intl.NumberFormat("it-IT").format(w);return(w,c)=>{const H=J("DepartmentNode",!0);return s(),l("div",oe,[e("div",ne,[e("div",ie,[e("div",de,[e("div",ue,[i.department.subdepartments.length>0?(s(),l("button",{key:0,onClick:_,class:"mr-2 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"},[(s(),l("svg",{class:O(["w-4 h-4 text-gray-500 transition-transform",x.value?"transform rotate-90":""]),fill:"currentColor",viewBox:"0 0 20 20"},c[3]||(c[3]=[e("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"},null,-1)]),2))])):p("",!0),e("div",ce,[c[4]||(c[4]=e("div",{class:"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})])],-1)),e("div",null,[e("h3",ge,d(i.department.name),1),i.department.description?(s(),l("p",me,d(i.department.description),1)):p("",!0),e("div",ve,[e("span",null,d(i.department.employee_count)+" dipendenti",1),i.department.budget>0?(s(),l("span",pe,"Budget: €"+d(g(i.department.budget)),1)):p("",!0)])])])])]),i.department.manager?(s(),l("div",xe,[e("div",ye,[e("p",he,d(i.department.manager.full_name),1),c[5]||(c[5]=e("p",{class:"text-xs text-gray-500 dark:text-gray-400"},"Manager",-1))]),c[6]||(c[6]=e("div",{class:"w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-purple-600 dark:text-purple-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])],-1))])):p("",!0)]),i.department.employees.length>0?(s(),l("div",be,[e("div",fe,[(s(!0),l(N,null,V(o.value,n=>(s(),l("div",{key:n.id,onClick:$=>w.$emit("employee-click",n),class:"flex items-center p-2 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"},[c[8]||(c[8]=e("div",{class:"w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3"},[e("svg",{class:"w-4 h-4 text-gray-500 dark:text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])],-1)),e("div",we,[e("p",Ce,d(n.full_name),1),e("p",_e,d(n.position||"Dipendente"),1)]),n.is_manager?(s(),l("div",ze,c[7]||(c[7]=[e("span",{class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"}," Manager ",-1)]))):p("",!0)],8,ke))),128))]),i.department.employees.length>U?(s(),l("div",$e,[e("button",{onClick:c[0]||(c[0]=n=>k.value=!k.value),class:"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"},d(k.value?"Mostra meno":`Mostra altri ${i.department.employees.length-U}`),1)])):p("",!0)])):p("",!0)]),x.value&&i.department.subdepartments.length>0?(s(),l("div",Me,[e("div",De,[c[9]||(c[9]=e("div",{class:"absolute -left-4 top-0 bottom-0 w-px bg-gray-300 dark:bg-gray-600"},null,-1)),c[10]||(c[10]=e("div",{class:"absolute -left-4 top-6 w-4 h-px bg-gray-300 dark:bg-gray-600"},null,-1)),(s(!0),l(N,null,V(i.department.subdepartments,n=>(s(),K(H,{key:n.id,department:n,expanded:i.expanded,"search-query":i.searchQuery,onToggleNode:c[1]||(c[1]=$=>w.$emit("toggle-node",$)),onEmployeeClick:c[2]||(c[2]=$=>w.$emit("employee-click",$))},null,8,["department","expanded","search-query"]))),128))])])):p("",!0)])}}},Le=P(Ee,[["__scopeId","data-v-31c5ce5d"]]),Se={class:"department-list"},Ae={class:"p-6 border-b border-gray-200 dark:border-gray-700"},Be={class:"flex items-center justify-between"},He={class:"flex items-center"},Ne={class:"text-xl font-semibold text-gray-900 dark:text-white"},Ve={key:0,class:"text-sm text-gray-500 dark:text-gray-400 mt-1"},je={class:"flex items-center mt-2 space-x-6 text-sm text-gray-500 dark:text-gray-400"},Te={class:"flex items-center"},Fe={key:0,class:"flex items-center"},Oe={key:1,class:"flex items-center"},qe={key:0,class:"flex items-center bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3"},Ie={class:"text-sm font-medium text-gray-900 dark:text-white"},Re={class:"text-xs text-gray-500 dark:text-gray-400"},Ge={key:0,class:"p-6"},Qe={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"},Ue=["onClick"],Pe={class:"flex items-center"},Ze={class:"flex-1 min-w-0"},We={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},Ye={class:"text-xs text-gray-500 dark:text-gray-400 truncate"},Je={class:"text-xs text-gray-500 dark:text-gray-400 truncate"},Ke={class:"mt-3 flex items-center justify-between"},Xe={class:"flex items-center space-x-2"},et={key:0,class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"},tt={key:0,class:"text-xs text-gray-500 dark:text-gray-400"},rt={key:1,class:"border-t border-gray-200 dark:border-gray-700"},at={class:"p-6"},st={class:"space-y-4"},lt={__name:"DepartmentList",props:{department:{type:Object,required:!0},searchQuery:{type:String,default:""}},emits:["employee-click"],setup(i,{emit:j}){const C=x=>new Intl.NumberFormat("it-IT").format(x),E=x=>x?new Date(x).toLocaleDateString("it-IT",{year:"numeric",month:"short"}):"",k=x=>({admin:"Admin",manager:"Manager",employee:"Dipendente"})[x]||x;return(x,o)=>{const _=J("DepartmentList",!0);return s(),l("div",Se,[e("div",Ae,[e("div",Be,[e("div",He,[o[4]||(o[4]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-4"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})])],-1)),e("div",null,[e("h3",Ne,d(i.department.name),1),i.department.description?(s(),l("p",Ve,d(i.department.description),1)):p("",!0),e("div",je,[e("span",Te,[o[1]||(o[1]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"})],-1)),B(" "+d(i.department.employee_count)+" dipendenti ",1)]),i.department.budget>0?(s(),l("span",Fe,[o[2]||(o[2]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"}),e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.51-1.31c-.562-.649-1.413-1.076-2.353-1.253V5z","clip-rule":"evenodd"})],-1)),B(" Budget: €"+d(C(i.department.budget)),1)])):p("",!0),i.department.subdepartments.length>0?(s(),l("span",Oe,[o[3]||(o[3]=e("svg",{class:"w-4 h-4 mr-1",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"})],-1)),B(" "+d(i.department.subdepartments.length)+" sotto-dipartimenti ",1)])):p("",!0)])])]),i.department.manager?(s(),l("div",qe,[o[6]||(o[6]=e("div",{class:"w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mr-3"},[e("svg",{class:"w-5 h-5 text-purple-600 dark:text-purple-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])],-1)),e("div",null,[e("p",Ie,d(i.department.manager.full_name),1),o[5]||(o[5]=e("p",{class:"text-xs text-gray-500 dark:text-gray-400"},"Manager del Dipartimento",-1)),e("p",Re,d(i.department.manager.email),1)])])):p("",!0)])]),i.department.employees.length>0?(s(),l("div",Ge,[o[8]||(o[8]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Dipendenti",-1)),e("div",Qe,[(s(!0),l(N,null,V(i.department.employees,g=>(s(),l("div",{key:g.id,onClick:w=>x.$emit("employee-click",g),class:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer transition-colors"},[e("div",Pe,[o[7]||(o[7]=e("div",{class:"w-10 h-10 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center mr-3"},[e("svg",{class:"w-5 h-5 text-gray-500 dark:text-gray-400",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])],-1)),e("div",Ze,[e("p",We,d(g.full_name),1),e("p",Ye,d(g.position||"Dipendente"),1),e("p",Je,d(g.email),1)])]),e("div",Ke,[e("div",Xe,[g.is_manager?(s(),l("span",et," Manager ")):p("",!0),e("span",{class:O(["inline-flex items-center px-2 py-0.5 rounded text-xs font-medium",g.role==="admin"?"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200":g.role==="manager"?"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"])},d(k(g.role)),3)]),g.hire_date?(s(),l("div",tt,d(E(g.hire_date)),1)):p("",!0)])],8,Ue))),128))])])):p("",!0),i.department.subdepartments.length>0?(s(),l("div",rt,[e("div",at,[o[9]||(o[9]=e("h4",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"},"Sotto-dipartimenti",-1)),e("div",st,[(s(!0),l(N,null,V(i.department.subdepartments,g=>(s(),K(_,{key:g.id,department:g,"search-query":i.searchQuery,onEmployeeClick:o[0]||(o[0]=w=>x.$emit("employee-click",w)),class:"ml-6 border-l-2 border-gray-200 dark:border-gray-700 pl-6"},null,8,["department","search-query"]))),128))])])])):p("",!0)])}}},ot=P(lt,[["__scopeId","data-v-d1eb3eb4"]]),nt={class:"org-chart-diagram"},it={class:"mt-4 flex justify-center space-x-4"},dt={__name:"OrgChartDiagram",props:{orgData:{type:Array,required:!0},searchQuery:{type:String,default:""}},emits:["employee-click","department-click"],setup(i,{emit:j}){const C=i,E=j,k=z(null),x=z(!0);let o=null;const _=()=>{if(!k.value||!C.orgData.length)return;k.value.innerHTML="";const y=k.value,b=y.clientWidth,D=Math.max(600,y.clientHeight);o=document.createElementNS("http://www.w3.org/2000/svg","svg"),o.setAttribute("width",b),o.setAttribute("height",D),o.setAttribute("viewBox",`0 0 ${b} ${D}`),o.style.background="transparent";const M=document.createElementNS("http://www.w3.org/2000/svg","g");o.appendChild(M);const v=[],S=[],T=(u,h=null,f=0)=>{const r={id:`dept-${u.id}`,type:"department",name:u.name,description:u.description,level:f,employees:u.employees.length,budget:u.budget,manager:u.manager,x:0,y:0};v.push(r),h&&S.push({source:h.id,target:r.id}),u.employees.forEach((t,m)=>{const a={id:`emp-${t.id}`,type:"employee",name:t.full_name,position:t.position,isManager:t.is_manager,level:f+1,x:0,y:0,employee:t};v.push(a),S.push({source:r.id,target:a.id})}),u.subdepartments.forEach(t=>{T(t,r,f+1)})};C.orgData.forEach(u=>T(u)),g(v,b,D),S.forEach(u=>{const h=v.find(r=>r.id===u.source),f=v.find(r=>r.id===u.target);if(h&&f){const r=document.createElementNS("http://www.w3.org/2000/svg","line");r.setAttribute("x1",h.x),r.setAttribute("y1",h.y),r.setAttribute("x2",f.x),r.setAttribute("y2",f.y),r.setAttribute("stroke","#d1d5db"),r.setAttribute("stroke-width","2"),M.appendChild(r)}}),v.forEach(u=>{const h=document.createElementNS("http://www.w3.org/2000/svg","g");h.setAttribute("transform",`translate(${u.x}, ${u.y})`),h.style.cursor="pointer";const f=document.createElementNS("http://www.w3.org/2000/svg","circle");f.setAttribute("r",u.type==="department"?"25":"15"),f.setAttribute("fill",w(u)),f.setAttribute("stroke","#fff"),f.setAttribute("stroke-width","2"),h.appendChild(f);const r=document.createElementNS("http://www.w3.org/2000/svg","text");r.setAttribute("text-anchor","middle"),r.setAttribute("dy",u.type==="department"?"35":"25"),r.setAttribute("font-size","12"),r.setAttribute("fill","#374151"),r.textContent=c(u.name,15),h.appendChild(r),h.addEventListener("click",()=>{u.type==="department"?E("department-click",{id:u.id.replace("dept-",""),name:u.name}):E("employee-click",u.employee)}),h.addEventListener("mouseenter",()=>{f.setAttribute("stroke-width","3"),f.setAttribute("stroke","#3b82f6")}),h.addEventListener("mouseleave",()=>{f.setAttribute("stroke-width","2"),f.setAttribute("stroke","#fff")}),M.appendChild(h)}),y.appendChild(o)},g=(y,b,D)=>{const M={};y.forEach(v=>{M[v.level]||(M[v.level]=[]),M[v.level].push(v)}),Object.keys(M).forEach(v=>{const S=M[v],T=x.value?parseInt(v)*(D/Object.keys(M).length)+50:D/2;S.forEach((u,h)=>{x.value?(u.x=b/(S.length+1)*(h+1),u.y=T):(u.x=parseInt(v)*(b/Object.keys(M).length)+50,u.y=D/(S.length+1)*(h+1))})})},w=y=>y.type==="department"?"#3b82f6":y.isManager?"#8b5cf6":"#10b981",c=(y,b)=>y.length>b?y.substring(0,b)+"...":y,H=()=>{if(o){const y=L();q(y*1.2)}},n=()=>{if(o){const y=L();q(y*.8)}},$=()=>{o&&q(1)},L=()=>{const D=(o.querySelector("g").getAttribute("transform")||"").match(/scale\(([^)]+)\)/);return D?parseFloat(D[1]):1},q=y=>{o.querySelector("g").setAttribute("transform",`scale(${y})`)},G=()=>{x.value=!x.value,W(()=>{_()})};return X(()=>C.orgData,()=>{W(()=>{_()})},{deep:!0}),ee(()=>{W(()=>{_()}),window.addEventListener("resize",_)}),te(()=>{window.removeEventListener("resize",_)}),(y,b)=>(s(),l("div",nt,[e("div",{ref_key:"chartContainer",ref:k,class:"chart-container bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 rounded-lg p-6 min-h-96"},null,512),e("div",it,[e("button",{onClick:H,class:"px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"},b[0]||(b[0]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1)])),e("button",{onClick:n,class:"px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"},b[1]||(b[1]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M20 12H4"})],-1)])),e("button",{onClick:$,class:"px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"}," Reset "),e("button",{onClick:G,class:"px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"},d(x.value?"Layout Orizzontale":"Layout Verticale"),1)]),b[2]||(b[2]=re('<div class="mt-4 bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700" data-v-c85a321c><h4 class="text-sm font-medium text-gray-900 dark:text-white mb-2" data-v-c85a321c>Legenda</h4><div class="flex flex-wrap gap-4 text-xs" data-v-c85a321c><div class="flex items-center" data-v-c85a321c><div class="w-4 h-4 bg-blue-500 rounded mr-2" data-v-c85a321c></div><span class="text-gray-600 dark:text-gray-400" data-v-c85a321c>Dipartimento</span></div><div class="flex items-center" data-v-c85a321c><div class="w-4 h-4 bg-purple-500 rounded mr-2" data-v-c85a321c></div><span class="text-gray-600 dark:text-gray-400" data-v-c85a321c>Manager</span></div><div class="flex items-center" data-v-c85a321c><div class="w-4 h-4 bg-green-500 rounded mr-2" data-v-c85a321c></div><span class="text-gray-600 dark:text-gray-400" data-v-c85a321c>Dipendente</span></div></div></div>',1))]))}},ut=P(dt,[["__scopeId","data-v-c85a321c"]]),ct={class:"space-y-6"},gt={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between"},mt={class:"mt-4 sm:mt-0 flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-3"},vt={class:"relative"},pt={class:"flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1"},xt=["disabled"],yt={key:0,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},ht={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"},bt={key:0,class:"mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"},ft={class:"flex flex-wrap gap-2"},kt=["onClick"],wt={key:1,class:"grid grid-cols-1 md:grid-cols-3 gap-6"},Ct={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},_t={class:"flex items-center"},zt={class:"ml-4"},$t={class:"text-sm font-medium text-gray-500 dark:text-gray-400"},Mt={class:"text-2xl font-bold text-gray-900 dark:text-white"},Dt={key:0,class:"text-xs text-gray-400"},Et={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},Lt={class:"flex items-center"},St={class:"ml-4"},At={class:"text-sm font-medium text-gray-500 dark:text-gray-400"},Bt={class:"text-2xl font-bold text-gray-900 dark:text-white"},Ht={key:0,class:"text-xs text-gray-400"},Nt={class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"},Vt={class:"flex items-center"},jt={class:"ml-4"},Tt={class:"text-sm font-medium text-gray-500 dark:text-gray-400"},Ft={class:"text-2xl font-bold text-gray-900 dark:text-white"},Ot={key:0,class:"text-xs text-gray-400"},qt={key:2,class:"flex justify-center items-center py-12"},It={key:3,class:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},Rt={class:"flex"},Gt={class:"mt-1 text-sm text-red-700 dark:text-red-300"},Qt={key:4,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700"},Ut={key:0,class:"p-6"},Pt={class:"orgchart-container overflow-x-auto"},Zt={class:"orgchart-tree min-w-max"},Wt={key:1,class:"divide-y divide-gray-200 dark:divide-gray-700"},Yt={key:2,class:"p-6"},Jt={key:5,class:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12"},Kt={class:"text-center"},Xt={class:"mt-6"},er={__name:"PersonnelOrgChart",setup(i){const j=le(),C=z(!1),E=z(null),k=z([]),x=z(null),o=z(null),_=z(""),g=z("tree"),w=z(new Set),c=z(!1),H=z(!1),n=z({hierarchyLevel:"",departmentSize:"",budgetRange:"",hasManager:""}),$=R(()=>{let r=k.value;if(L.value&&(r=r.map(t=>G(t)).filter(t=>t!==null)),_.value.trim()){const t=_.value.toLowerCase();r=r.map(m=>y(m,t)).filter(m=>m!==null)}return r}),L=R(()=>Object.values(n.value).some(r=>r!=="")),q=R(()=>{const r=[],t={hierarchyLevel:{managers:"Solo Manager",employees:"Solo Dipendenti",top_level:"Dirigenti"},departmentSize:{small:"Piccoli Dipartimenti",medium:"Medi Dipartimenti",large:"Grandi Dipartimenti"},budgetRange:{low:"Budget Basso",medium:"Budget Medio",high:"Budget Alto"},hasManager:{true:"Con Manager",false:"Senza Manager"}};return Object.entries(n.value).forEach(([m,a])=>{a&&t[m]&&t[m][a]&&r.push({key:m,label:t[m][a]})}),r}),G=r=>{if(n.value.departmentSize){const a=r.employee_count;if(n.value.departmentSize==="small"&&a>5||n.value.departmentSize==="medium"&&(a<6||a>15)||n.value.departmentSize==="large"&&a<16)return null}if(n.value.budgetRange&&r.budget){const a=r.budget;if(n.value.budgetRange==="low"&&a>=5e4||n.value.budgetRange==="medium"&&(a<5e4||a>2e5)||n.value.budgetRange==="high"&&a<=2e5)return null}if(n.value.hasManager){const a=r.manager_id!==null;if(n.value.hasManager==="true"&&!a||n.value.hasManager==="false"&&a)return null}let t=r.employees;n.value.hierarchyLevel&&(n.value.hierarchyLevel==="managers"?t=r.employees.filter(a=>a.is_manager||a.role==="manager"||a.role==="admin"):n.value.hierarchyLevel==="employees"?t=r.employees.filter(a=>!a.is_manager&&a.role==="employee"):n.value.hierarchyLevel==="top_level"&&(t=r.employees.filter(a=>a.role==="admin")));const m=r.subdepartments.map(a=>G(a)).filter(a=>a!==null);return t.length>0||m.length>0?{...r,employees:t,subdepartments:m,employee_count:t.length}:null},y=(r,t)=>{const m=r.name.toLowerCase().includes(t)||r.description&&r.description.toLowerCase().includes(t),a=r.employees.filter(A=>A.full_name.toLowerCase().includes(t)||A.email.toLowerCase().includes(t)||A.position&&A.position.toLowerCase().includes(t)),F=r.subdepartments.map(A=>y(A,t)).filter(A=>A!==null);return m||a.length>0||F.length>0?{...r,employees:a.length>0?a:r.employees,subdepartments:F}:null},b=async()=>{C.value=!0,E.value=null;try{const r=await fetch("/api/personnel/orgchart",{credentials:"include"});if(!r.ok)throw new Error(`HTTP ${r.status}: ${r.statusText}`);const t=await r.json();if(t.success)k.value=t.data.orgchart||[],x.value=t.data.stats||{},v(),k.value.length>0&&k.value.forEach(m=>{w.value.add(m.id)});else throw new Error(t.message||"Errore nel caricamento dell'organigramma")}catch(r){console.error("Error loading org chart:",r),E.value=r.message}finally{C.value=!1}},D=r=>{w.value.has(r)?w.value.delete(r):w.value.add(r)},M=()=>{if(c.value)w.value.clear(),c.value=!1;else{const r=t=>{t.forEach(m=>{w.value.add(m.id),m.subdepartments&&m.subdepartments.length>0&&r(m.subdepartments)})};r(k.value),c.value=!0}},v=()=>{const r=t=>{let m=0,a=0,F=0;const A=Z=>{a++,m+=Z.employees.length,Z.manager_id&&F++,Z.subdepartments.forEach(A)};return t.forEach(A),{total_employees:m,total_departments:a,total_managers:F}};o.value=r($.value)},S=r=>{g.value=r,r==="chart"&&(c.value=!1)},T=()=>{n.value={hierarchyLevel:"",departmentSize:"",budgetRange:"",hasManager:""},v()},u=r=>{n.value[r]="",v()},h=r=>{j.push(`/app/personnel/${r.id}`)},f=r=>{console.log("Department clicked:",r.name)};return X($,()=>{v()},{deep:!0}),ee(()=>{b()}),(r,t)=>{const m=J("router-link");return s(),l("div",ct,[e("div",gt,[t[14]||(t[14]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white flex items-center"},[e("svg",{class:"w-8 h-8 mr-3 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})]),B(" Organigramma Aziendale ")]),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-gray-400"}," Struttura organizzativa e gerarchia aziendale ")],-1)),e("div",mt,[e("div",vt,[I(e("input",{"onUpdate:modelValue":t[0]||(t[0]=a=>_.value=a),onInput:v,type:"text",placeholder:"Cerca dipendente o dipartimento...",class:"w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,544),[[ae,_.value]]),t[9]||(t[9]=e("svg",{class:"absolute left-3 top-2.5 w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))]),e("div",pt,[e("button",{onClick:t[1]||(t[1]=a=>S("tree")),class:O(["px-3 py-1 rounded-md text-sm font-medium transition-colors",g.value==="tree"?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"])},t[10]||(t[10]=[e("svg",{class:"w-4 h-4 mr-1 inline",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M3 4a1 1 0 000 2h11a1 1 0 100-2H3zM3 8a1 1 0 000 2h6a1 1 0 100-2H3zM14 7a1 1 0 011 1v8a1 1 0 11-2 0V9.414l-1.293 1.293a1 1 0 01-1.414-1.414L12.586 7H14z"})],-1),B(" Albero ")]),2),e("button",{onClick:t[2]||(t[2]=a=>S("list")),class:O(["px-3 py-1 rounded-md text-sm font-medium transition-colors",g.value==="list"?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"])},t[11]||(t[11]=[e("svg",{class:"w-4 h-4 mr-1 inline",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z","clip-rule":"evenodd"})],-1),B(" Lista ")]),2),e("button",{onClick:t[3]||(t[3]=a=>S("chart")),class:O(["px-3 py-1 rounded-md text-sm font-medium transition-colors",g.value==="chart"?"bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"])},t[12]||(t[12]=[e("svg",{class:"w-4 h-4 mr-1 inline",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"})],-1),B(" Chart ")]),2)]),g.value==="tree"?(s(),l("button",{key:0,onClick:M,disabled:$.value.length===0,class:"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg transition-colors duration-200"},d(c.value?"Comprimi Tutto":"Espandi Tutto"),9,xt)):p("",!0),e("button",{onClick:t[4]||(t[4]=a=>H.value=!H.value),class:O(["px-4 py-2 rounded-lg transition-colors duration-200 flex items-center",H.value?"bg-green-600 hover:bg-green-700 text-white":"bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-300"])},t[13]||(t[13]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"})],-1),B(" Filtri ")]),2)])]),H.value?(s(),l("div",yt,[e("div",{class:"flex items-center justify-between mb-4"},[t[15]||(t[15]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Filtri Avanzati",-1)),e("button",{onClick:T,class:"text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300"}," Cancella tutti ")]),e("div",ht,[e("div",null,[t[17]||(t[17]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Livello Gerarchico",-1)),I(e("select",{"onUpdate:modelValue":t[5]||(t[5]=a=>n.value.hierarchyLevel=a),onChange:v,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[16]||(t[16]=[e("option",{value:""},"Tutti i livelli",-1),e("option",{value:"managers"},"Solo Manager",-1),e("option",{value:"employees"},"Solo Dipendenti",-1),e("option",{value:"top_level"},"Dirigenti",-1)]),544),[[Q,n.value.hierarchyLevel]])]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Dimensione Dipartimento",-1)),I(e("select",{"onUpdate:modelValue":t[6]||(t[6]=a=>n.value.departmentSize=a),onChange:v,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[18]||(t[18]=[e("option",{value:""},"Tutte le dimensioni",-1),e("option",{value:"small"},"Piccoli (1-5 dipendenti)",-1),e("option",{value:"medium"},"Medi (6-15 dipendenti)",-1),e("option",{value:"large"},"Grandi (16+ dipendenti)",-1)]),544),[[Q,n.value.departmentSize]])]),e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Budget Dipartimento",-1)),I(e("select",{"onUpdate:modelValue":t[7]||(t[7]=a=>n.value.budgetRange=a),onChange:v,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[20]||(t[20]=[e("option",{value:""},"Tutti i budget",-1),e("option",{value:"low"},"Basso (< €50k)",-1),e("option",{value:"medium"},"Medio (€50k - €200k)",-1),e("option",{value:"high"},"Alto (> €200k)",-1)]),544),[[Q,n.value.budgetRange]])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"},"Stato Manager",-1)),I(e("select",{"onUpdate:modelValue":t[8]||(t[8]=a=>n.value.hasManager=a),onChange:v,class:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[22]||(t[22]=[e("option",{value:""},"Tutti",-1),e("option",{value:"true"},"Con Manager",-1),e("option",{value:"false"},"Senza Manager",-1)]),544),[[Q,n.value.hasManager]])])]),L.value?(s(),l("div",bt,[e("div",ft,[t[25]||(t[25]=e("span",{class:"text-sm text-gray-500 dark:text-gray-400 mr-2"},"Filtri attivi:",-1)),(s(!0),l(N,null,V(q.value,a=>(s(),l("span",{key:a.key,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},[B(d(a.label)+" ",1),e("button",{onClick:F=>u(a.key),class:"ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"},t[24]||(t[24]=[e("svg",{class:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]),8,kt)]))),128))])])):p("",!0)])):p("",!0),o.value?(s(),l("div",wt,[e("div",Ct,[e("div",_t,[t[26]||(t[26]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{d:"M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"})])],-1)),e("div",zt,[e("p",$t," Dipendenti "+d(L.value?"Filtrati":"Totali"),1),e("p",Mt,d(o.value.total_employees),1),L.value?(s(),l("p",Dt," di "+d(x.value.total_employees)+" totali ",1)):p("",!0)])])]),e("div",Et,[e("div",Lt,[t[27]||(t[27]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-green-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 104 0 2 2 0 00-4 0zm8 0a2 2 0 104 0 2 2 0 00-4 0z","clip-rule":"evenodd"})])],-1)),e("div",St,[e("p",At," Dipartimenti "+d(L.value?"Filtrati":"Totali"),1),e("p",Bt,d(o.value.total_departments),1),L.value?(s(),l("p",Ht," di "+d(x.value.total_departments)+" totali ",1)):p("",!0)])])]),e("div",Nt,[e("div",Vt,[t[28]||(t[28]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"w-8 h-8 text-purple-600",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z","clip-rule":"evenodd"})])],-1)),e("div",jt,[e("p",Tt," Manager "+d(L.value?"Filtrati":"Totali"),1),e("p",Ft,d(o.value.total_managers),1),L.value?(s(),l("p",Ot," di "+d(x.value.total_managers)+" totali ",1)):p("",!0)])])])])):p("",!0),C.value?(s(),l("div",qt,t[29]||(t[29]=[e("div",{class:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"},null,-1)]))):E.value?(s(),l("div",It,[e("div",Rt,[t[31]||(t[31]=e("svg",{class:"w-5 h-5 text-red-400 mr-3 mt-0.5",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z","clip-rule":"evenodd"})],-1)),e("div",null,[t[30]||(t[30]=e("h3",{class:"text-sm font-medium text-red-800 dark:text-red-200"},"Errore nel caricamento",-1)),e("p",Gt,d(E.value),1)])])])):k.value.length>0?(s(),l("div",Qt,[g.value==="tree"?(s(),l("div",Ut,[e("div",Pt,[e("div",Zt,[(s(!0),l(N,null,V($.value,a=>(s(),K(Le,{key:a.id,department:a,expanded:w.value,"search-query":_.value,onToggleNode:D,onEmployeeClick:h},null,8,["department","expanded","search-query"]))),128))])])])):g.value==="list"?(s(),l("div",Wt,[(s(!0),l(N,null,V($.value,a=>(s(),l("div",{key:a.id},[Y(ot,{department:a,"search-query":_.value,onEmployeeClick:h},null,8,["department","search-query"])]))),128))])):g.value==="chart"?(s(),l("div",Yt,[Y(ut,{"org-data":$.value,"search-query":_.value,onEmployeeClick:h,onDepartmentClick:f},null,8,["org-data","search-query"])])):p("",!0)])):C.value?p("",!0):(s(),l("div",Jt,[e("div",Kt,[t[33]||(t[33]=e("svg",{class:"mx-auto h-16 w-16 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),t[34]||(t[34]=e("h3",{class:"mt-4 text-lg font-medium text-gray-900 dark:text-white"},"Nessun dipartimento configurato",-1)),t[35]||(t[35]=e("p",{class:"mt-2 text-sm text-gray-500 dark:text-gray-400"}," Inizia creando la struttura organizzativa aziendale ",-1)),e("div",Xt,[Y(m,{to:"/app/personnel/departments",class:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},{default:se(()=>t[32]||(t[32]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),B(" Gestisci Dipartimenti ")])),_:1,__:[32]})])])]))])}}},ar=P(er,[["__scopeId","data-v-cf560a80"]]);export{ar as default};
