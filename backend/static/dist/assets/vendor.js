var ip=Object.defineProperty;var op=(e,t,n)=>t in e?ip(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var N=(e,t,n)=>op(e,typeof t!="symbol"?t+"":t,n);/**
* @vue/shared v3.5.16
* (c) 2018-present <PERSON><PERSON> (<PERSON>) <PERSON> and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function ya(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const pt={},Zn=[],Le=()=>{},rp=()=>!1,Po=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),xa=e=>e.startsWith("onUpdate:"),Lt=Object.assign,va=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},ap=Object.prototype.hasOwnProperty,ut=(e,t)=>ap.call(e,t),q=Array.isArray,ts=e=>ms(e)==="[object Map]",gs=e=>ms(e)==="[object Set]",dl=e=>ms(e)==="[object Date]",lp=e=>ms(e)==="[object RegExp]",tt=e=>typeof e=="function",Mt=e=>typeof e=="string",ye=e=>typeof e=="symbol",gt=e=>e!==null&&typeof e=="object",Zu=e=>(gt(e)||tt(e))&&tt(e.then)&&tt(e.catch),tf=Object.prototype.toString,ms=e=>tf.call(e),cp=e=>ms(e).slice(8,-1),ef=e=>ms(e)==="[object Object]",wa=e=>Mt(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Ns=ya(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),ko=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},up=/-(\w)/g,ge=ko(e=>e.replace(up,(t,n)=>n?n.toUpperCase():"")),fp=/\B([A-Z])/g,yn=ko(e=>e.replace(fp,"-$1").toLowerCase()),Ao=ko(e=>e.charAt(0).toUpperCase()+e.slice(1)),tr=ko(e=>e?`on${Ao(e)}`:""),fn=(e,t)=>!Object.is(e,t),es=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},nf=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},io=e=>{const t=parseFloat(e);return isNaN(t)?e:t},hp=e=>{const t=Mt(e)?Number(e):NaN;return isNaN(t)?e:t};let pl;const Oo=()=>pl||(pl=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Sa(e){if(q(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],i=Mt(s)?mp(s):Sa(s);if(i)for(const o in i)t[o]=i[o]}return t}else if(Mt(e)||gt(e))return e}const dp=/;(?![^(]*\))/g,pp=/:([^]+)/,gp=/\/\*[^]*?\*\//g;function mp(e){const t={};return e.replace(gp,"").split(dp).forEach(n=>{if(n){const s=n.split(pp);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Ma(e){let t="";if(Mt(e))t=e;else if(q(e))for(let n=0;n<e.length;n++){const s=Ma(e[n]);s&&(t+=s+" ")}else if(gt(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const bp="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",_p=ya(bp);function sf(e){return!!e||e===""}function yp(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=Vn(e[s],t[s]);return n}function Vn(e,t){if(e===t)return!0;let n=dl(e),s=dl(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=ye(e),s=ye(t),n||s)return e===t;if(n=q(e),s=q(t),n||s)return n&&s?yp(e,t):!1;if(n=gt(e),s=gt(t),n||s){if(!n||!s)return!1;const i=Object.keys(e).length,o=Object.keys(t).length;if(i!==o)return!1;for(const r in e){const a=e.hasOwnProperty(r),l=t.hasOwnProperty(r);if(a&&!l||!a&&l||!Vn(e[r],t[r]))return!1}}return String(e)===String(t)}function Ca(e,t){return e.findIndex(n=>Vn(n,t))}const of=e=>!!(e&&e.__v_isRef===!0),xp=e=>Mt(e)?e:e==null?"":q(e)||gt(e)&&(e.toString===tf||!tt(e.toString))?of(e)?xp(e.value):JSON.stringify(e,rf,2):String(e),rf=(e,t)=>of(t)?rf(e,t.value):ts(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,i],o)=>(n[er(s,o)+" =>"]=i,n),{})}:gs(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>er(n))}:ye(t)?er(t):gt(t)&&!q(t)&&!ef(t)?String(t):t,er=(e,t="")=>{var n;return ye(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let zt;class af{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=zt,!t&&zt&&(this.index=(zt.scopes||(zt.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=zt;try{return zt=this,t()}finally{zt=n}}}on(){++this._on===1&&(this.prevScope=zt,zt=this)}off(){this._on>0&&--this._on===0&&(zt=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0}}}function lf(e){return new af(e)}function cf(){return zt}function vp(e,t=!1){zt&&zt.cleanups.push(e)}let _t;const nr=new WeakSet;class uf{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,zt&&zt.active&&zt.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,nr.has(this)&&(nr.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||hf(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,gl(this),df(this);const t=_t,n=be;_t=this,be=!0;try{return this.fn()}finally{pf(this),_t=t,be=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)ka(t);this.deps=this.depsTail=void 0,gl(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?nr.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Rr(this)&&this.run()}get dirty(){return Rr(this)}}let ff=0,Bs,zs;function hf(e,t=!1){if(e.flags|=8,t){e.next=zs,zs=e;return}e.next=Bs,Bs=e}function Ea(){ff++}function Pa(){if(--ff>0)return;if(zs){let t=zs;for(zs=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Bs;){let t=Bs;for(Bs=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function df(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function pf(e){let t,n=e.depsTail,s=n;for(;s;){const i=s.prevDep;s.version===-1?(s===n&&(n=i),ka(s),wp(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=i}e.deps=t,e.depsTail=n}function Rr(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(gf(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function gf(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Js)||(e.globalVersion=Js,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Rr(e))))return;e.flags|=2;const t=e.dep,n=_t,s=be;_t=e,be=!0;try{df(e);const i=e.fn(e._value);(t.version===0||fn(i,e._value))&&(e.flags|=128,e._value=i,t.version++)}catch(i){throw t.version++,i}finally{_t=n,be=s,pf(e),e.flags&=-3}}function ka(e,t=!1){const{dep:n,prevSub:s,nextSub:i}=e;if(s&&(s.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)ka(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function wp(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let be=!0;const mf=[];function Xe(){mf.push(be),be=!1}function Ge(){const e=mf.pop();be=e===void 0?!0:e}function gl(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=_t;_t=void 0;try{t()}finally{_t=n}}}let Js=0;class Sp{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Aa{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!_t||!be||_t===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==_t)n=this.activeLink=new Sp(_t,this),_t.deps?(n.prevDep=_t.depsTail,_t.depsTail.nextDep=n,_t.depsTail=n):_t.deps=_t.depsTail=n,bf(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=_t.depsTail,n.nextDep=void 0,_t.depsTail.nextDep=n,_t.depsTail=n,_t.deps===n&&(_t.deps=s)}return n}trigger(t){this.version++,Js++,this.notify(t)}notify(t){Ea();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Pa()}}}function bf(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)bf(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const oo=new WeakMap,Fn=Symbol(""),Tr=Symbol(""),Qs=Symbol("");function Ht(e,t,n){if(be&&_t){let s=oo.get(e);s||oo.set(e,s=new Map);let i=s.get(n);i||(s.set(n,i=new Aa),i.map=s,i.key=n),i.track()}}function We(e,t,n,s,i,o){const r=oo.get(e);if(!r){Js++;return}const a=l=>{l&&l.trigger()};if(Ea(),t==="clear")r.forEach(a);else{const l=q(e),c=l&&wa(n);if(l&&n==="length"){const u=Number(s);r.forEach((f,h)=>{(h==="length"||h===Qs||!ye(h)&&h>=u)&&a(f)})}else switch((n!==void 0||r.has(void 0))&&a(r.get(n)),c&&a(r.get(Qs)),t){case"add":l?c&&a(r.get("length")):(a(r.get(Fn)),ts(e)&&a(r.get(Tr)));break;case"delete":l||(a(r.get(Fn)),ts(e)&&a(r.get(Tr)));break;case"set":ts(e)&&a(r.get(Fn));break}}Pa()}function Mp(e,t){const n=oo.get(e);return n&&n.get(t)}function Kn(e){const t=lt(e);return t===e?t:(Ht(t,"iterate",Qs),he(e)?t:t.map(Nt))}function Ro(e){return Ht(e=lt(e),"iterate",Qs),e}const Cp={__proto__:null,[Symbol.iterator](){return sr(this,Symbol.iterator,Nt)},concat(...e){return Kn(this).concat(...e.map(t=>q(t)?Kn(t):t))},entries(){return sr(this,"entries",e=>(e[1]=Nt(e[1]),e))},every(e,t){return Ie(this,"every",e,t,void 0,arguments)},filter(e,t){return Ie(this,"filter",e,t,n=>n.map(Nt),arguments)},find(e,t){return Ie(this,"find",e,t,Nt,arguments)},findIndex(e,t){return Ie(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ie(this,"findLast",e,t,Nt,arguments)},findLastIndex(e,t){return Ie(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ie(this,"forEach",e,t,void 0,arguments)},includes(...e){return ir(this,"includes",e)},indexOf(...e){return ir(this,"indexOf",e)},join(e){return Kn(this).join(e)},lastIndexOf(...e){return ir(this,"lastIndexOf",e)},map(e,t){return Ie(this,"map",e,t,void 0,arguments)},pop(){return xs(this,"pop")},push(...e){return xs(this,"push",e)},reduce(e,...t){return ml(this,"reduce",e,t)},reduceRight(e,...t){return ml(this,"reduceRight",e,t)},shift(){return xs(this,"shift")},some(e,t){return Ie(this,"some",e,t,void 0,arguments)},splice(...e){return xs(this,"splice",e)},toReversed(){return Kn(this).toReversed()},toSorted(e){return Kn(this).toSorted(e)},toSpliced(...e){return Kn(this).toSpliced(...e)},unshift(...e){return xs(this,"unshift",e)},values(){return sr(this,"values",Nt)}};function sr(e,t,n){const s=Ro(e),i=s[t]();return s!==e&&!he(e)&&(i._next=i.next,i.next=()=>{const o=i._next();return o.value&&(o.value=n(o.value)),o}),i}const Ep=Array.prototype;function Ie(e,t,n,s,i,o){const r=Ro(e),a=r!==e&&!he(e),l=r[t];if(l!==Ep[t]){const f=l.apply(e,o);return a?Nt(f):f}let c=n;r!==e&&(a?c=function(f,h){return n.call(this,Nt(f),h,e)}:n.length>2&&(c=function(f,h){return n.call(this,f,h,e)}));const u=l.call(r,c,s);return a&&i?i(u):u}function ml(e,t,n,s){const i=Ro(e);let o=n;return i!==e&&(he(e)?n.length>3&&(o=function(r,a,l){return n.call(this,r,a,l,e)}):o=function(r,a,l){return n.call(this,r,Nt(a),l,e)}),i[t](o,...s)}function ir(e,t,n){const s=lt(e);Ht(s,"iterate",Qs);const i=s[t](...n);return(i===-1||i===!1)&&Ta(n[0])?(n[0]=lt(n[0]),s[t](...n)):i}function xs(e,t,n=[]){Xe(),Ea();const s=lt(e)[t].apply(e,n);return Pa(),Ge(),s}const Pp=ya("__proto__,__v_isRef,__isVue"),_f=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(ye));function kp(e){ye(e)||(e=String(e));const t=lt(this);return Ht(t,"has",e),t.hasOwnProperty(e)}class yf{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const i=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!i;if(n==="__v_isReadonly")return i;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(i?o?Bp:Sf:o?wf:vf).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const r=q(t);if(!i){let l;if(r&&(l=Cp[n]))return l;if(n==="hasOwnProperty")return kp}const a=Reflect.get(t,n,kt(t)?t:s);return(ye(n)?_f.has(n):Pp(n))||(i||Ht(t,"get",n),o)?a:kt(a)?r&&wa(n)?a:a.value:gt(a)?i?Cf(a):di(a):a}}class xf extends yf{constructor(t=!1){super(!1,t)}set(t,n,s,i){let o=t[n];if(!this._isShallow){const l=pn(o);if(!he(s)&&!pn(s)&&(o=lt(o),s=lt(s)),!q(t)&&kt(o)&&!kt(s))return l?!1:(o.value=s,!0)}const r=q(t)&&wa(n)?Number(n)<t.length:ut(t,n),a=Reflect.set(t,n,s,kt(t)?t:i);return t===lt(i)&&(r?fn(s,o)&&We(t,"set",n,s):We(t,"add",n,s)),a}deleteProperty(t,n){const s=ut(t,n);t[n];const i=Reflect.deleteProperty(t,n);return i&&s&&We(t,"delete",n,void 0),i}has(t,n){const s=Reflect.has(t,n);return(!ye(n)||!_f.has(n))&&Ht(t,"has",n),s}ownKeys(t){return Ht(t,"iterate",q(t)?"length":Fn),Reflect.ownKeys(t)}}class Ap extends yf{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Op=new xf,Rp=new Ap,Tp=new xf(!0);const Dr=e=>e,xi=e=>Reflect.getPrototypeOf(e);function Dp(e,t,n){return function(...s){const i=this.__v_raw,o=lt(i),r=ts(o),a=e==="entries"||e===Symbol.iterator&&r,l=e==="keys"&&r,c=i[e](...s),u=n?Dr:t?ro:Nt;return!t&&Ht(o,"iterate",l?Tr:Fn),{next(){const{value:f,done:h}=c.next();return h?{value:f,done:h}:{value:a?[u(f[0]),u(f[1])]:u(f),done:h}},[Symbol.iterator](){return this}}}}function vi(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Lp(e,t){const n={get(i){const o=this.__v_raw,r=lt(o),a=lt(i);e||(fn(i,a)&&Ht(r,"get",i),Ht(r,"get",a));const{has:l}=xi(r),c=t?Dr:e?ro:Nt;if(l.call(r,i))return c(o.get(i));if(l.call(r,a))return c(o.get(a));o!==r&&o.get(i)},get size(){const i=this.__v_raw;return!e&&Ht(lt(i),"iterate",Fn),Reflect.get(i,"size",i)},has(i){const o=this.__v_raw,r=lt(o),a=lt(i);return e||(fn(i,a)&&Ht(r,"has",i),Ht(r,"has",a)),i===a?o.has(i):o.has(i)||o.has(a)},forEach(i,o){const r=this,a=r.__v_raw,l=lt(a),c=t?Dr:e?ro:Nt;return!e&&Ht(l,"iterate",Fn),a.forEach((u,f)=>i.call(o,c(u),c(f),r))}};return Lt(n,e?{add:vi("add"),set:vi("set"),delete:vi("delete"),clear:vi("clear")}:{add(i){!t&&!he(i)&&!pn(i)&&(i=lt(i));const o=lt(this);return xi(o).has.call(o,i)||(o.add(i),We(o,"add",i,i)),this},set(i,o){!t&&!he(o)&&!pn(o)&&(o=lt(o));const r=lt(this),{has:a,get:l}=xi(r);let c=a.call(r,i);c||(i=lt(i),c=a.call(r,i));const u=l.call(r,i);return r.set(i,o),c?fn(o,u)&&We(r,"set",i,o):We(r,"add",i,o),this},delete(i){const o=lt(this),{has:r,get:a}=xi(o);let l=r.call(o,i);l||(i=lt(i),l=r.call(o,i)),a&&a.call(o,i);const c=o.delete(i);return l&&We(o,"delete",i,void 0),c},clear(){const i=lt(this),o=i.size!==0,r=i.clear();return o&&We(i,"clear",void 0,void 0),r}}),["keys","values","entries",Symbol.iterator].forEach(i=>{n[i]=Dp(i,e,t)}),n}function Oa(e,t){const n=Lp(e,t);return(s,i,o)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?s:Reflect.get(ut(n,i)&&i in s?n:s,i,o)}const Fp={get:Oa(!1,!1)},Ip={get:Oa(!1,!0)},Np={get:Oa(!0,!1)};const vf=new WeakMap,wf=new WeakMap,Sf=new WeakMap,Bp=new WeakMap;function zp(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Hp(e){return e.__v_skip||!Object.isExtensible(e)?0:zp(cp(e))}function di(e){return pn(e)?e:Ra(e,!1,Op,Fp,vf)}function Mf(e){return Ra(e,!1,Tp,Ip,wf)}function Cf(e){return Ra(e,!0,Rp,Np,Sf)}function Ra(e,t,n,s,i){if(!gt(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=Hp(e);if(o===0)return e;const r=i.get(e);if(r)return r;const a=new Proxy(e,o===2?s:n);return i.set(e,a),a}function hn(e){return pn(e)?hn(e.__v_raw):!!(e&&e.__v_isReactive)}function pn(e){return!!(e&&e.__v_isReadonly)}function he(e){return!!(e&&e.__v_isShallow)}function Ta(e){return e?!!e.__v_raw:!1}function lt(e){const t=e&&e.__v_raw;return t?lt(t):e}function Da(e){return!ut(e,"__v_skip")&&Object.isExtensible(e)&&nf(e,"__v_skip",!0),e}const Nt=e=>gt(e)?di(e):e,ro=e=>gt(e)?Cf(e):e;function kt(e){return e?e.__v_isRef===!0:!1}function La(e){return Ef(e,!1)}function Vp(e){return Ef(e,!0)}function Ef(e,t){return kt(e)?e:new jp(e,t)}class jp{constructor(t,n){this.dep=new Aa,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:lt(t),this._value=n?t:Nt(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||he(t)||pn(t);t=s?t:lt(t),fn(t,n)&&(this._rawValue=t,this._value=s?t:Nt(t),this.dep.trigger())}}function ns(e){return kt(e)?e.value:e}const Wp={get:(e,t,n)=>t==="__v_raw"?e:ns(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const i=e[t];return kt(i)&&!kt(n)?(i.value=n,!0):Reflect.set(e,t,n,s)}};function Pf(e){return hn(e)?e:new Proxy(e,Wp)}function $p(e){const t=q(e)?new Array(e.length):{};for(const n in e)t[n]=Kp(e,n);return t}class Up{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Mp(lt(this._object),this._key)}}function Kp(e,t,n){const s=e[t];return kt(s)?s:new Up(e,t,n)}class qp{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Aa(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Js-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&_t!==this)return hf(this,!0),!0}get value(){const t=this.dep.track();return gf(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Yp(e,t,n=!1){let s,i;return tt(e)?s=e:(s=e.get,i=e.set),new qp(s,i,n)}const wi={},ao=new WeakMap;let kn;function Xp(e,t=!1,n=kn){if(n){let s=ao.get(n);s||ao.set(n,s=[]),s.push(e)}}function Gp(e,t,n=pt){const{immediate:s,deep:i,once:o,scheduler:r,augmentJob:a,call:l}=n,c=v=>i?v:he(v)||i===!1||i===0?$e(v,1):$e(v);let u,f,h,d,p=!1,g=!1;if(kt(e)?(f=()=>e.value,p=he(e)):hn(e)?(f=()=>c(e),p=!0):q(e)?(g=!0,p=e.some(v=>hn(v)||he(v)),f=()=>e.map(v=>{if(kt(v))return v.value;if(hn(v))return c(v);if(tt(v))return l?l(v,2):v()})):tt(e)?t?f=l?()=>l(e,2):e:f=()=>{if(h){Xe();try{h()}finally{Ge()}}const v=kn;kn=u;try{return l?l(e,3,[d]):e(d)}finally{kn=v}}:f=Le,t&&i){const v=f,S=i===!0?1/0:i;f=()=>$e(v(),S)}const m=cf(),_=()=>{u.stop(),m&&m.active&&va(m.effects,u)};if(o&&t){const v=t;t=(...S)=>{v(...S),_()}}let b=g?new Array(e.length).fill(wi):wi;const w=v=>{if(!(!(u.flags&1)||!u.dirty&&!v))if(t){const S=u.run();if(i||p||(g?S.some((A,k)=>fn(A,b[k])):fn(S,b))){h&&h();const A=kn;kn=u;try{const k=[S,b===wi?void 0:g&&b[0]===wi?[]:b,d];b=S,l?l(t,3,k):t(...k)}finally{kn=A}}}else u.run()};return a&&a(w),u=new uf(f),u.scheduler=r?()=>r(w,!1):w,d=v=>Xp(v,!1,u),h=u.onStop=()=>{const v=ao.get(u);if(v){if(l)l(v,4);else for(const S of v)S();ao.delete(u)}},t?s?w(!0):b=u.run():r?r(w.bind(null,!0),!0):u.run(),_.pause=u.pause.bind(u),_.resume=u.resume.bind(u),_.stop=_,_}function $e(e,t=1/0,n){if(t<=0||!gt(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,kt(e))$e(e.value,t,n);else if(q(e))for(let s=0;s<e.length;s++)$e(e[s],t,n);else if(gs(e)||ts(e))e.forEach(s=>{$e(s,t,n)});else if(ef(e)){for(const s in e)$e(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&$e(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function pi(e,t,n,s){try{return s?e(...s):e()}catch(i){To(i,t,n)}}function xe(e,t,n,s){if(tt(e)){const i=pi(e,t,n,s);return i&&Zu(i)&&i.catch(o=>{To(o,t,n)}),i}if(q(e)){const i=[];for(let o=0;o<e.length;o++)i.push(xe(e[o],t,n,s));return i}}function To(e,t,n,s=!0){const i=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:r}=t&&t.appContext.config||pt;if(t){let a=t.parent;const l=t.proxy,c=`https://vuejs.org/error-reference/#runtime-${n}`;for(;a;){const u=a.ec;if(u){for(let f=0;f<u.length;f++)if(u[f](e,l,c)===!1)return}a=a.parent}if(o){Xe(),pi(o,null,10,[e,l,c]),Ge();return}}Jp(e,n,i,s,r)}function Jp(e,t,n,s=!0,i=!1){if(i)throw e;console.error(e)}const Yt=[];let Re=-1;const ss=[];let sn=null,Jn=0;const kf=Promise.resolve();let lo=null;function Do(e){const t=lo||kf;return e?t.then(this?e.bind(this):e):t}function Qp(e){let t=Re+1,n=Yt.length;for(;t<n;){const s=t+n>>>1,i=Yt[s],o=Zs(i);o<e||o===e&&i.flags&2?t=s+1:n=s}return t}function Fa(e){if(!(e.flags&1)){const t=Zs(e),n=Yt[Yt.length-1];!n||!(e.flags&2)&&t>=Zs(n)?Yt.push(e):Yt.splice(Qp(t),0,e),e.flags|=1,Af()}}function Af(){lo||(lo=kf.then(Rf))}function Zp(e){q(e)?ss.push(...e):sn&&e.id===-1?sn.splice(Jn+1,0,e):e.flags&1||(ss.push(e),e.flags|=1),Af()}function bl(e,t,n=Re+1){for(;n<Yt.length;n++){const s=Yt[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;Yt.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function Of(e){if(ss.length){const t=[...new Set(ss)].sort((n,s)=>Zs(n)-Zs(s));if(ss.length=0,sn){sn.push(...t);return}for(sn=t,Jn=0;Jn<sn.length;Jn++){const n=sn[Jn];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}sn=null,Jn=0}}const Zs=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Rf(e){try{for(Re=0;Re<Yt.length;Re++){const t=Yt[Re];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),pi(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Re<Yt.length;Re++){const t=Yt[Re];t&&(t.flags&=-2)}Re=-1,Yt.length=0,Of(),lo=null,(Yt.length||ss.length)&&Rf()}}let Tt=null,Tf=null;function co(e){const t=Tt;return Tt=e,Tf=e&&e.type.__scopeId||null,t}function tg(e,t=Tt,n){if(!t||e._n)return e;const s=(...i)=>{s._d&&Al(-1);const o=co(t);let r;try{r=e(...i)}finally{co(o),s._d&&Al(1)}return r};return s._n=!0,s._c=!0,s._d=!0,s}function OS(e,t){if(Tt===null)return e;const n=No(Tt),s=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[o,r,a,l=pt]=t[i];o&&(tt(o)&&(o={mounted:o,updated:o}),o.deep&&$e(r),s.push({dir:o,instance:n,value:r,oldValue:void 0,arg:a,modifiers:l}))}return e}function vn(e,t,n,s){const i=e.dirs,o=t&&t.dirs;for(let r=0;r<i.length;r++){const a=i[r];o&&(a.oldValue=o[r].value);let l=a.dir[s];l&&(Xe(),xe(l,n,8,[e.el,a,e,t]),Ge())}}const Df=Symbol("_vte"),eg=e=>e.__isTeleport,Hs=e=>e&&(e.disabled||e.disabled===""),_l=e=>e&&(e.defer||e.defer===""),yl=e=>typeof SVGElement<"u"&&e instanceof SVGElement,xl=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,Lr=(e,t)=>{const n=e&&e.to;return Mt(n)?t?t(n):null:n},Lf={name:"Teleport",__isTeleport:!0,process(e,t,n,s,i,o,r,a,l,c){const{mc:u,pc:f,pbc:h,o:{insert:d,querySelector:p,createText:g,createComment:m}}=c,_=Hs(t.props);let{shapeFlag:b,children:w,dynamicChildren:v}=t;if(e==null){const S=t.el=g(""),A=t.anchor=g("");d(S,n,s),d(A,n,s);const k=(C,F)=>{b&16&&(i&&i.isCE&&(i.ce._teleportTarget=C),u(w,C,F,i,o,r,a,l))},E=()=>{const C=t.target=Lr(t.props,p),F=Ff(C,t,g,d);C&&(r!=="svg"&&yl(C)?r="svg":r!=="mathml"&&xl(C)&&(r="mathml"),_||(k(C,F),Vi(t,!1)))};_&&(k(n,A),Vi(t,!0)),_l(t.props)?(t.el.__isMounted=!1,Rt(()=>{E(),delete t.el.__isMounted},o)):E()}else{if(_l(t.props)&&e.el.__isMounted===!1){Rt(()=>{Lf.process(e,t,n,s,i,o,r,a,l,c)},o);return}t.el=e.el,t.targetStart=e.targetStart;const S=t.anchor=e.anchor,A=t.target=e.target,k=t.targetAnchor=e.targetAnchor,E=Hs(e.props),C=E?n:A,F=E?S:k;if(r==="svg"||yl(A)?r="svg":(r==="mathml"||xl(A))&&(r="mathml"),v?(h(e.dynamicChildren,v,C,i,o,r,a),Va(e,t,!0)):l||f(e,t,C,F,i,o,r,a,!1),_)E?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Si(t,n,S,c,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const B=t.target=Lr(t.props,p);B&&Si(t,B,null,c,0)}else E&&Si(t,A,k,c,1);Vi(t,_)}},remove(e,t,n,{um:s,o:{remove:i}},o){const{shapeFlag:r,children:a,anchor:l,targetStart:c,targetAnchor:u,target:f,props:h}=e;if(f&&(i(c),i(u)),o&&i(l),r&16){const d=o||!Hs(h);for(let p=0;p<a.length;p++){const g=a[p];s(g,t,n,d,!!g.dynamicChildren)}}},move:Si,hydrate:ng};function Si(e,t,n,{o:{insert:s},m:i},o=2){o===0&&s(e.targetAnchor,t,n);const{el:r,anchor:a,shapeFlag:l,children:c,props:u}=e,f=o===2;if(f&&s(r,t,n),(!f||Hs(u))&&l&16)for(let h=0;h<c.length;h++)i(c[h],t,n,2);f&&s(a,t,n)}function ng(e,t,n,s,i,o,{o:{nextSibling:r,parentNode:a,querySelector:l,insert:c,createText:u}},f){const h=t.target=Lr(t.props,l);if(h){const d=Hs(t.props),p=h._lpa||h.firstChild;if(t.shapeFlag&16)if(d)t.anchor=f(r(e),t,a(e),n,s,i,o),t.targetStart=p,t.targetAnchor=p&&r(p);else{t.anchor=r(e);let g=p;for(;g;){if(g&&g.nodeType===8){if(g.data==="teleport start anchor")t.targetStart=g;else if(g.data==="teleport anchor"){t.targetAnchor=g,h._lpa=t.targetAnchor&&r(t.targetAnchor);break}}g=r(g)}t.targetAnchor||Ff(h,t,u,c),f(p&&r(p),t,h,n,s,i,o)}Vi(t,d)}return t.anchor&&r(t.anchor)}const RS=Lf;function Vi(e,t){const n=e.ctx;if(n&&n.ut){let s,i;for(t?(s=e.el,i=e.anchor):(s=e.targetStart,i=e.targetAnchor);s&&s!==i;)s.nodeType===1&&s.setAttribute("data-v-owner",n.uid),s=s.nextSibling;n.ut()}}function Ff(e,t,n,s){const i=t.targetStart=n(""),o=t.targetAnchor=n("");return i[Df]=o,e&&(s(i,e),s(o,e)),o}const qn=Symbol("_leaveCb"),Mi=Symbol("_enterCb");function sg(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Ia(()=>{e.isMounted=!0}),Ba(()=>{e.isUnmounting=!0}),e}const le=[Function,Array],ig={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:le,onEnter:le,onAfterEnter:le,onEnterCancelled:le,onBeforeLeave:le,onLeave:le,onAfterLeave:le,onLeaveCancelled:le,onBeforeAppear:le,onAppear:le,onAfterAppear:le,onAppearCancelled:le};function og(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function Fr(e,t,n,s,i){const{appear:o,mode:r,persisted:a=!1,onBeforeEnter:l,onEnter:c,onAfterEnter:u,onEnterCancelled:f,onBeforeLeave:h,onLeave:d,onAfterLeave:p,onLeaveCancelled:g,onBeforeAppear:m,onAppear:_,onAfterAppear:b,onAppearCancelled:w}=t,v=String(e.key),S=og(n,e),A=(C,F)=>{C&&xe(C,s,9,F)},k=(C,F)=>{const B=F[1];A(C,F),q(C)?C.every(L=>L.length<=1)&&B():C.length<=1&&B()},E={mode:r,persisted:a,beforeEnter(C){let F=l;if(!n.isMounted)if(o)F=m||l;else return;C[qn]&&C[qn](!0);const B=S[v];B&&Tn(e,B)&&B.el[qn]&&B.el[qn](),A(F,[C])},enter(C){let F=c,B=u,L=f;if(!n.isMounted)if(o)F=_||c,B=b||u,L=w||f;else return;let X=!1;const rt=C[Mi]=Z=>{X||(X=!0,Z?A(L,[C]):A(B,[C]),E.delayedLeave&&E.delayedLeave(),C[Mi]=void 0)};F?k(F,[C,rt]):rt()},leave(C,F){const B=String(e.key);if(C[Mi]&&C[Mi](!0),n.isUnmounting)return F();A(h,[C]);let L=!1;const X=C[qn]=rt=>{L||(L=!0,F(),rt?A(g,[C]):A(p,[C]),C[qn]=void 0,S[B]===e&&delete S[B])};S[B]=e,d?k(d,[C,X]):X()},clone(C){return Fr(C,t,n,s)}};return E}function rs(e,t){e.shapeFlag&6&&e.component?(e.transition=t,rs(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function If(e,t=!1,n){let s=[],i=0;for(let o=0;o<e.length;o++){let r=e[o];const a=n==null?r.key:String(n)+String(r.key!=null?r.key:o);r.type===Qt?(r.patchFlag&128&&i++,s=s.concat(If(r.children,t,a))):(t||r.type!==ve)&&s.push(a!=null?gn(r,{key:a}):r)}if(i>1)for(let o=0;o<s.length;o++)s[o].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function Nf(e,t){return tt(e)?Lt({name:e.name},t,{setup:e}):e}function Bf(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function uo(e,t,n,s,i=!1){if(q(e)){e.forEach((p,g)=>uo(p,t&&(q(t)?t[g]:t),n,s,i));return}if(In(s)&&!i){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&uo(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?No(s.component):s.el,r=i?null:o,{i:a,r:l}=e,c=t&&t.r,u=a.refs===pt?a.refs={}:a.refs,f=a.setupState,h=lt(f),d=f===pt?()=>!1:p=>ut(h,p);if(c!=null&&c!==l&&(Mt(c)?(u[c]=null,d(c)&&(f[c]=null)):kt(c)&&(c.value=null)),tt(l))pi(l,a,12,[r,u]);else{const p=Mt(l),g=kt(l);if(p||g){const m=()=>{if(e.f){const _=p?d(l)?f[l]:u[l]:l.value;i?q(_)&&va(_,o):q(_)?_.includes(o)||_.push(o):p?(u[l]=[o],d(l)&&(f[l]=u[l])):(l.value=[o],e.k&&(u[e.k]=l.value))}else p?(u[l]=r,d(l)&&(f[l]=r)):g&&(l.value=r,e.k&&(u[e.k]=r))};r?(m.id=-1,Rt(m,n)):m()}}}Oo().requestIdleCallback;Oo().cancelIdleCallback;const In=e=>!!e.type.__asyncLoader,zf=e=>e.type.__isKeepAlive,rg={name:"KeepAlive",__isKeepAlive:!0,props:{include:[String,RegExp,Array],exclude:[String,RegExp,Array],max:[String,Number]},setup(e,{slots:t}){const n=uh(),s=n.ctx;if(!s.renderer)return()=>{const b=t.default&&t.default();return b&&b.length===1?b[0]:b};const i=new Map,o=new Set;let r=null;const a=n.suspense,{renderer:{p:l,m:c,um:u,o:{createElement:f}}}=s,h=f("div");s.activate=(b,w,v,S,A)=>{const k=b.component;c(b,w,v,0,a),l(k.vnode,b,w,v,k,a,S,b.slotScopeIds,A),Rt(()=>{k.isDeactivated=!1,k.a&&es(k.a);const E=b.props&&b.props.onVnodeMounted;E&&ue(E,k.parent,b)},a)},s.deactivate=b=>{const w=b.component;ho(w.m),ho(w.a),c(b,h,null,1,a),Rt(()=>{w.da&&es(w.da);const v=b.props&&b.props.onVnodeUnmounted;v&&ue(v,w.parent,b),w.isDeactivated=!0},a)};function d(b){or(b),u(b,n,a,!0)}function p(b){i.forEach((w,v)=>{const S=Wr(w.type);S&&!b(S)&&g(v)})}function g(b){const w=i.get(b);w&&(!r||!Tn(w,r))?d(w):r&&or(r),i.delete(b),o.delete(b)}is(()=>[e.include,e.exclude],([b,w])=>{b&&p(v=>As(b,v)),w&&p(v=>!As(w,v))},{flush:"post",deep:!0});let m=null;const _=()=>{m!=null&&(po(n.subTree.type)?Rt(()=>{i.set(m,Ci(n.subTree))},n.subTree.suspense):i.set(m,Ci(n.subTree)))};return Ia(_),Na(_),Ba(()=>{i.forEach(b=>{const{subTree:w,suspense:v}=n,S=Ci(w);if(b.type===S.type&&b.key===S.key){or(S);const A=S.component.da;A&&Rt(A,v);return}d(b)})}),()=>{if(m=null,!t.default)return r=null;const b=t.default(),w=b[0];if(b.length>1)return r=null,b;if(!as(w)||!(w.shapeFlag&4)&&!(w.shapeFlag&128))return r=null,w;let v=Ci(w);if(v.type===ve)return r=null,v;const S=v.type,A=Wr(In(v)?v.type.__asyncResolved||{}:S),{include:k,exclude:E,max:C}=e;if(k&&(!A||!As(k,A))||E&&A&&As(E,A))return v.shapeFlag&=-257,r=v,w;const F=v.key==null?S:v.key,B=i.get(F);return v.el&&(v=gn(v),w.shapeFlag&128&&(w.ssContent=v)),m=F,B?(v.el=B.el,v.component=B.component,v.transition&&rs(v,v.transition),v.shapeFlag|=512,o.delete(F),o.add(F)):(o.add(F),C&&o.size>parseInt(C,10)&&g(o.values().next().value)),v.shapeFlag|=256,r=v,po(w.type)?w:v}}},TS=rg;function As(e,t){return q(e)?e.some(n=>As(n,t)):Mt(e)?e.split(",").includes(t):lp(e)?(e.lastIndex=0,e.test(t)):!1}function ag(e,t){Hf(e,"a",t)}function lg(e,t){Hf(e,"da",t)}function Hf(e,t,n=Ft){const s=e.__wdc||(e.__wdc=()=>{let i=n;for(;i;){if(i.isDeactivated)return;i=i.parent}return e()});if(Lo(t,s,n),n){let i=n.parent;for(;i&&i.parent;)zf(i.parent.vnode)&&cg(s,t,n,i),i=i.parent}}function cg(e,t,n,s){const i=Lo(t,e,s,!0);Vf(()=>{va(s[t],i)},n)}function or(e){e.shapeFlag&=-257,e.shapeFlag&=-513}function Ci(e){return e.shapeFlag&128?e.ssContent:e}function Lo(e,t,n=Ft,s=!1){if(n){const i=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...r)=>{Xe();const a=gi(n),l=xe(t,n,e,r);return a(),Ge(),l});return s?i.unshift(o):i.push(o),o}}const Je=e=>(t,n=Ft)=>{(!ei||e==="sp")&&Lo(e,(...s)=>t(...s),n)},ug=Je("bm"),Ia=Je("m"),fg=Je("bu"),Na=Je("u"),Ba=Je("bum"),Vf=Je("um"),hg=Je("sp"),dg=Je("rtg"),pg=Je("rtc");function gg(e,t=Ft){Lo("ec",e,t)}const jf="components";function DS(e,t){return $f(jf,e,!0,t)||e}const Wf=Symbol.for("v-ndc");function LS(e){return Mt(e)?$f(jf,e,!1)||e:e||Wf}function $f(e,t,n=!0,s=!1){const i=Tt||Ft;if(i){const o=i.type;{const a=Wr(o,!1);if(a&&(a===t||a===ge(t)||a===Ao(ge(t))))return o}const r=vl(i[e]||o[e],t)||vl(i.appContext[e],t);return!r&&s?o:r}}function vl(e,t){return e&&(e[t]||e[ge(t)]||e[Ao(ge(t))])}function FS(e,t,n,s){let i;const o=n,r=q(e);if(r||Mt(e)){const a=r&&hn(e);let l=!1,c=!1;a&&(l=!he(e),c=pn(e),e=Ro(e)),i=new Array(e.length);for(let u=0,f=e.length;u<f;u++)i[u]=t(l?c?ro(Nt(e[u])):Nt(e[u]):e[u],u,void 0,o)}else if(typeof e=="number"){i=new Array(e);for(let a=0;a<e;a++)i[a]=t(a+1,a,void 0,o)}else if(gt(e))if(e[Symbol.iterator])i=Array.from(e,(a,l)=>t(a,l,void 0,o));else{const a=Object.keys(e);i=new Array(a.length);for(let l=0,c=a.length;l<c;l++){const u=a[l];i[l]=t(e[u],u,l,o)}}else i=[];return i}function IS(e,t,n={},s,i){if(Tt.ce||Tt.parent&&In(Tt.parent)&&Tt.parent.ce)return n.name=t,Hr(),Vr(Qt,null,[jt("slot",n,s)],64);let o=e[t];o&&o._c&&(o._d=!1),Hr();const r=o&&Uf(o(n)),a=n.key||r&&r.key,l=Vr(Qt,{key:(a&&!ye(a)?a:`_${t}`)+""},r||[],r&&e._===1?64:-2);return l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),o&&o._c&&(o._d=!0),l}function Uf(e){return e.some(t=>as(t)?!(t.type===ve||t.type===Qt&&!Uf(t.children)):!0)?e:null}const Ir=e=>e?fh(e)?No(e):Ir(e.parent):null,Vs=Lt(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ir(e.parent),$root:e=>Ir(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>qf(e),$forceUpdate:e=>e.f||(e.f=()=>{Fa(e.update)}),$nextTick:e=>e.n||(e.n=Do.bind(e.proxy)),$watch:e=>Ng.bind(e)}),rr=(e,t)=>e!==pt&&!e.__isScriptSetup&&ut(e,t),mg={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:i,props:o,accessCache:r,type:a,appContext:l}=e;let c;if(t[0]!=="$"){const d=r[t];if(d!==void 0)switch(d){case 1:return s[t];case 2:return i[t];case 4:return n[t];case 3:return o[t]}else{if(rr(s,t))return r[t]=1,s[t];if(i!==pt&&ut(i,t))return r[t]=2,i[t];if((c=e.propsOptions[0])&&ut(c,t))return r[t]=3,o[t];if(n!==pt&&ut(n,t))return r[t]=4,n[t];Nr&&(r[t]=0)}}const u=Vs[t];let f,h;if(u)return t==="$attrs"&&Ht(e.attrs,"get",""),u(e);if((f=a.__cssModules)&&(f=f[t]))return f;if(n!==pt&&ut(n,t))return r[t]=4,n[t];if(h=l.config.globalProperties,ut(h,t))return h[t]},set({_:e},t,n){const{data:s,setupState:i,ctx:o}=e;return rr(i,t)?(i[t]=n,!0):s!==pt&&ut(s,t)?(s[t]=n,!0):ut(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:i,propsOptions:o}},r){let a;return!!n[r]||e!==pt&&ut(e,r)||rr(t,r)||(a=o[0])&&ut(a,r)||ut(s,r)||ut(Vs,r)||ut(i.config.globalProperties,r)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ut(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function wl(e){return q(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Nr=!0;function bg(e){const t=qf(e),n=e.proxy,s=e.ctx;Nr=!1,t.beforeCreate&&Sl(t.beforeCreate,e,"bc");const{data:i,computed:o,methods:r,watch:a,provide:l,inject:c,created:u,beforeMount:f,mounted:h,beforeUpdate:d,updated:p,activated:g,deactivated:m,beforeDestroy:_,beforeUnmount:b,destroyed:w,unmounted:v,render:S,renderTracked:A,renderTriggered:k,errorCaptured:E,serverPrefetch:C,expose:F,inheritAttrs:B,components:L,directives:X,filters:rt}=t;if(c&&_g(c,s,null),r)for(const U in r){const et=r[U];tt(et)&&(s[U]=et.bind(n))}if(i){const U=i.call(n,n);gt(U)&&(e.data=di(U))}if(Nr=!0,o)for(const U in o){const et=o[U],mt=tt(et)?et.bind(n,n):tt(et.get)?et.get.bind(n,n):Le,Ut=!tt(et)&&tt(et.set)?et.set.bind(n):Le,Kt=fe({get:mt,set:Ut});Object.defineProperty(s,U,{enumerable:!0,configurable:!0,get:()=>Kt.value,set:Et=>Kt.value=Et})}if(a)for(const U in a)Kf(a[U],s,n,U);if(l){const U=tt(l)?l.call(n):l;Reflect.ownKeys(U).forEach(et=>{ji(et,U[et])})}u&&Sl(u,e,"c");function G(U,et){q(et)?et.forEach(mt=>U(mt.bind(n))):et&&U(et.bind(n))}if(G(ug,f),G(Ia,h),G(fg,d),G(Na,p),G(ag,g),G(lg,m),G(gg,E),G(pg,A),G(dg,k),G(Ba,b),G(Vf,v),G(hg,C),q(F))if(F.length){const U=e.exposed||(e.exposed={});F.forEach(et=>{Object.defineProperty(U,et,{get:()=>n[et],set:mt=>n[et]=mt})})}else e.exposed||(e.exposed={});S&&e.render===Le&&(e.render=S),B!=null&&(e.inheritAttrs=B),L&&(e.components=L),X&&(e.directives=X),C&&Bf(e)}function _g(e,t,n=Le){q(e)&&(e=Br(e));for(const s in e){const i=e[s];let o;gt(i)?"default"in i?o=de(i.from||s,i.default,!0):o=de(i.from||s):o=de(i),kt(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:r=>o.value=r}):t[s]=o}}function Sl(e,t,n){xe(q(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Kf(e,t,n,s){let i=s.includes(".")?oh(n,s):()=>n[s];if(Mt(e)){const o=t[e];tt(o)&&is(i,o)}else if(tt(e))is(i,e.bind(n));else if(gt(e))if(q(e))e.forEach(o=>Kf(o,t,n,s));else{const o=tt(e.handler)?e.handler.bind(n):t[e.handler];tt(o)&&is(i,o,e)}}function qf(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:i,optionsCache:o,config:{optionMergeStrategies:r}}=e.appContext,a=o.get(t);let l;return a?l=a:!i.length&&!n&&!s?l=t:(l={},i.length&&i.forEach(c=>fo(l,c,r,!0)),fo(l,t,r)),gt(t)&&o.set(t,l),l}function fo(e,t,n,s=!1){const{mixins:i,extends:o}=t;o&&fo(e,o,n,!0),i&&i.forEach(r=>fo(e,r,n,!0));for(const r in t)if(!(s&&r==="expose")){const a=yg[r]||n&&n[r];e[r]=a?a(e[r],t[r]):t[r]}return e}const yg={data:Ml,props:Cl,emits:Cl,methods:Os,computed:Os,beforeCreate:qt,created:qt,beforeMount:qt,mounted:qt,beforeUpdate:qt,updated:qt,beforeDestroy:qt,beforeUnmount:qt,destroyed:qt,unmounted:qt,activated:qt,deactivated:qt,errorCaptured:qt,serverPrefetch:qt,components:Os,directives:Os,watch:vg,provide:Ml,inject:xg};function Ml(e,t){return t?e?function(){return Lt(tt(e)?e.call(this,this):e,tt(t)?t.call(this,this):t)}:t:e}function xg(e,t){return Os(Br(e),Br(t))}function Br(e){if(q(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function qt(e,t){return e?[...new Set([].concat(e,t))]:t}function Os(e,t){return e?Lt(Object.create(null),e,t):t}function Cl(e,t){return e?q(e)&&q(t)?[...new Set([...e,...t])]:Lt(Object.create(null),wl(e),wl(t??{})):t}function vg(e,t){if(!e)return t;if(!t)return e;const n=Lt(Object.create(null),e);for(const s in t)n[s]=qt(e[s],t[s]);return n}function Yf(){return{app:null,config:{isNativeTag:rp,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let wg=0;function Sg(e,t){return function(s,i=null){tt(s)||(s=Lt({},s)),i!=null&&!gt(i)&&(i=null);const o=Yf(),r=new WeakSet,a=[];let l=!1;const c=o.app={_uid:wg++,_component:s,_props:i,_container:null,_context:o,_instance:null,version:im,get config(){return o.config},set config(u){},use(u,...f){return r.has(u)||(u&&tt(u.install)?(r.add(u),u.install(c,...f)):tt(u)&&(r.add(u),u(c,...f))),c},mixin(u){return o.mixins.includes(u)||o.mixins.push(u),c},component(u,f){return f?(o.components[u]=f,c):o.components[u]},directive(u,f){return f?(o.directives[u]=f,c):o.directives[u]},mount(u,f,h){if(!l){const d=c._ceVNode||jt(s,i);return d.appContext=o,h===!0?h="svg":h===!1&&(h=void 0),e(d,u,h),l=!0,c._container=u,u.__vue_app__=c,No(d.component)}},onUnmount(u){a.push(u)},unmount(){l&&(xe(a,c._instance,16),e(null,c._container),delete c._container.__vue_app__)},provide(u,f){return o.provides[u]=f,c},runWithContext(u){const f=Nn;Nn=c;try{return u()}finally{Nn=f}}};return c}}let Nn=null;function ji(e,t){if(Ft){let n=Ft.provides;const s=Ft.parent&&Ft.parent.provides;s===n&&(n=Ft.provides=Object.create(s)),n[e]=t}}function de(e,t,n=!1){const s=Ft||Tt;if(s||Nn){let i=Nn?Nn._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return n&&tt(t)?t.call(s&&s.proxy):t}}function Mg(){return!!(Ft||Tt||Nn)}const Xf={},Gf=()=>Object.create(Xf),Jf=e=>Object.getPrototypeOf(e)===Xf;function Cg(e,t,n,s=!1){const i={},o=Gf();e.propsDefaults=Object.create(null),Qf(e,t,i,o);for(const r in e.propsOptions[0])r in i||(i[r]=void 0);n?e.props=s?i:Mf(i):e.type.props?e.props=i:e.props=o,e.attrs=o}function Eg(e,t,n,s){const{props:i,attrs:o,vnode:{patchFlag:r}}=e,a=lt(i),[l]=e.propsOptions;let c=!1;if((s||r>0)&&!(r&16)){if(r&8){const u=e.vnode.dynamicProps;for(let f=0;f<u.length;f++){let h=u[f];if(Fo(e.emitsOptions,h))continue;const d=t[h];if(l)if(ut(o,h))d!==o[h]&&(o[h]=d,c=!0);else{const p=ge(h);i[p]=zr(l,a,p,d,e,!1)}else d!==o[h]&&(o[h]=d,c=!0)}}}else{Qf(e,t,i,o)&&(c=!0);let u;for(const f in a)(!t||!ut(t,f)&&((u=yn(f))===f||!ut(t,u)))&&(l?n&&(n[f]!==void 0||n[u]!==void 0)&&(i[f]=zr(l,a,f,void 0,e,!0)):delete i[f]);if(o!==a)for(const f in o)(!t||!ut(t,f))&&(delete o[f],c=!0)}c&&We(e.attrs,"set","")}function Qf(e,t,n,s){const[i,o]=e.propsOptions;let r=!1,a;if(t)for(let l in t){if(Ns(l))continue;const c=t[l];let u;i&&ut(i,u=ge(l))?!o||!o.includes(u)?n[u]=c:(a||(a={}))[u]=c:Fo(e.emitsOptions,l)||(!(l in s)||c!==s[l])&&(s[l]=c,r=!0)}if(o){const l=lt(n),c=a||pt;for(let u=0;u<o.length;u++){const f=o[u];n[f]=zr(i,l,f,c[f],e,!ut(c,f))}}return r}function zr(e,t,n,s,i,o){const r=e[n];if(r!=null){const a=ut(r,"default");if(a&&s===void 0){const l=r.default;if(r.type!==Function&&!r.skipFactory&&tt(l)){const{propsDefaults:c}=i;if(n in c)s=c[n];else{const u=gi(i);s=c[n]=l.call(null,t),u()}}else s=l;i.ce&&i.ce._setProp(n,s)}r[0]&&(o&&!a?s=!1:r[1]&&(s===""||s===yn(n))&&(s=!0))}return s}const Pg=new WeakMap;function Zf(e,t,n=!1){const s=n?Pg:t.propsCache,i=s.get(e);if(i)return i;const o=e.props,r={},a=[];let l=!1;if(!tt(e)){const u=f=>{l=!0;const[h,d]=Zf(f,t,!0);Lt(r,h),d&&a.push(...d)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!o&&!l)return gt(e)&&s.set(e,Zn),Zn;if(q(o))for(let u=0;u<o.length;u++){const f=ge(o[u]);El(f)&&(r[f]=pt)}else if(o)for(const u in o){const f=ge(u);if(El(f)){const h=o[u],d=r[f]=q(h)||tt(h)?{type:h}:Lt({},h),p=d.type;let g=!1,m=!0;if(q(p))for(let _=0;_<p.length;++_){const b=p[_],w=tt(b)&&b.name;if(w==="Boolean"){g=!0;break}else w==="String"&&(m=!1)}else g=tt(p)&&p.name==="Boolean";d[0]=g,d[1]=m,(g||ut(d,"default"))&&a.push(f)}}const c=[r,a];return gt(e)&&s.set(e,c),c}function El(e){return e[0]!=="$"&&!Ns(e)}const za=e=>e[0]==="_"||e==="$stable",Ha=e=>q(e)?e.map(De):[De(e)],kg=(e,t,n)=>{if(t._n)return t;const s=tg((...i)=>Ha(t(...i)),n);return s._c=!1,s},th=(e,t,n)=>{const s=e._ctx;for(const i in e){if(za(i))continue;const o=e[i];if(tt(o))t[i]=kg(i,o,s);else if(o!=null){const r=Ha(o);t[i]=()=>r}}},eh=(e,t)=>{const n=Ha(t);e.slots.default=()=>n},nh=(e,t,n)=>{for(const s in t)(n||!za(s))&&(e[s]=t[s])},Ag=(e,t,n)=>{const s=e.slots=Gf();if(e.vnode.shapeFlag&32){const i=t._;i?(nh(s,t,n),n&&nf(s,"_",i,!0)):th(t,s)}else t&&eh(e,t)},Og=(e,t,n)=>{const{vnode:s,slots:i}=e;let o=!0,r=pt;if(s.shapeFlag&32){const a=t._;a?n&&a===1?o=!1:nh(i,t,n):(o=!t.$stable,th(t,i)),r=t}else t&&(eh(e,t),r={default:1});if(o)for(const a in i)!za(a)&&r[a]==null&&delete i[a]},Rt=$g;function Rg(e){return Tg(e)}function Tg(e,t){const n=Oo();n.__VUE__=!0;const{insert:s,remove:i,patchProp:o,createElement:r,createText:a,createComment:l,setText:c,setElementText:u,parentNode:f,nextSibling:h,setScopeId:d=Le,insertStaticContent:p}=e,g=(y,x,M,O=null,D=null,T=null,V=void 0,H=null,z=!!x.dynamicChildren)=>{if(y===x)return;y&&!Tn(y,x)&&(O=R(y),Et(y,D,T,!0),y=null),x.patchFlag===-2&&(z=!1,x.dynamicChildren=null);const{type:I,ref:J,shapeFlag:W}=x;switch(I){case Io:m(y,x,M,O);break;case ve:_(y,x,M,O);break;case Wi:y==null&&b(x,M,O,V);break;case Qt:L(y,x,M,O,D,T,V,H,z);break;default:W&1?S(y,x,M,O,D,T,V,H,z):W&6?X(y,x,M,O,D,T,V,H,z):(W&64||W&128)&&I.process(y,x,M,O,D,T,V,H,z,K)}J!=null&&D&&uo(J,y&&y.ref,T,x||y,!x)},m=(y,x,M,O)=>{if(y==null)s(x.el=a(x.children),M,O);else{const D=x.el=y.el;x.children!==y.children&&c(D,x.children)}},_=(y,x,M,O)=>{y==null?s(x.el=l(x.children||""),M,O):x.el=y.el},b=(y,x,M,O)=>{[y.el,y.anchor]=p(y.children,x,M,O,y.el,y.anchor)},w=({el:y,anchor:x},M,O)=>{let D;for(;y&&y!==x;)D=h(y),s(y,M,O),y=D;s(x,M,O)},v=({el:y,anchor:x})=>{let M;for(;y&&y!==x;)M=h(y),i(y),y=M;i(x)},S=(y,x,M,O,D,T,V,H,z)=>{x.type==="svg"?V="svg":x.type==="math"&&(V="mathml"),y==null?A(x,M,O,D,T,V,H,z):C(y,x,D,T,V,H,z)},A=(y,x,M,O,D,T,V,H)=>{let z,I;const{props:J,shapeFlag:W,transition:Y,dirs:Q}=y;if(z=y.el=r(y.type,T,J&&J.is,J),W&8?u(z,y.children):W&16&&E(y.children,z,null,O,D,ar(y,T),V,H),Q&&vn(y,null,O,"created"),k(z,y,y.scopeId,V,O),J){for(const bt in J)bt!=="value"&&!Ns(bt)&&o(z,bt,null,J[bt],T,O);"value"in J&&o(z,"value",null,J.value,T),(I=J.onVnodeBeforeMount)&&ue(I,O,y)}Q&&vn(y,null,O,"beforeMount");const at=Dg(D,Y);at&&Y.beforeEnter(z),s(z,x,M),((I=J&&J.onVnodeMounted)||at||Q)&&Rt(()=>{I&&ue(I,O,y),at&&Y.enter(z),Q&&vn(y,null,O,"mounted")},D)},k=(y,x,M,O,D)=>{if(M&&d(y,M),O)for(let T=0;T<O.length;T++)d(y,O[T]);if(D){let T=D.subTree;if(x===T||po(T.type)&&(T.ssContent===x||T.ssFallback===x)){const V=D.vnode;k(y,V,V.scopeId,V.slotScopeIds,D.parent)}}},E=(y,x,M,O,D,T,V,H,z=0)=>{for(let I=z;I<y.length;I++){const J=y[I]=H?on(y[I]):De(y[I]);g(null,J,x,M,O,D,T,V,H)}},C=(y,x,M,O,D,T,V)=>{const H=x.el=y.el;let{patchFlag:z,dynamicChildren:I,dirs:J}=x;z|=y.patchFlag&16;const W=y.props||pt,Y=x.props||pt;let Q;if(M&&wn(M,!1),(Q=Y.onVnodeBeforeUpdate)&&ue(Q,M,x,y),J&&vn(x,y,M,"beforeUpdate"),M&&wn(M,!0),(W.innerHTML&&Y.innerHTML==null||W.textContent&&Y.textContent==null)&&u(H,""),I?F(y.dynamicChildren,I,H,M,O,ar(x,D),T):V||et(y,x,H,null,M,O,ar(x,D),T,!1),z>0){if(z&16)B(H,W,Y,M,D);else if(z&2&&W.class!==Y.class&&o(H,"class",null,Y.class,D),z&4&&o(H,"style",W.style,Y.style,D),z&8){const at=x.dynamicProps;for(let bt=0;bt<at.length;bt++){const ft=at[bt],ne=W[ft],Gt=Y[ft];(Gt!==ne||ft==="value")&&o(H,ft,ne,Gt,D,M)}}z&1&&y.children!==x.children&&u(H,x.children)}else!V&&I==null&&B(H,W,Y,M,D);((Q=Y.onVnodeUpdated)||J)&&Rt(()=>{Q&&ue(Q,M,x,y),J&&vn(x,y,M,"updated")},O)},F=(y,x,M,O,D,T,V)=>{for(let H=0;H<x.length;H++){const z=y[H],I=x[H],J=z.el&&(z.type===Qt||!Tn(z,I)||z.shapeFlag&198)?f(z.el):M;g(z,I,J,null,O,D,T,V,!0)}},B=(y,x,M,O,D)=>{if(x!==M){if(x!==pt)for(const T in x)!Ns(T)&&!(T in M)&&o(y,T,x[T],null,D,O);for(const T in M){if(Ns(T))continue;const V=M[T],H=x[T];V!==H&&T!=="value"&&o(y,T,H,V,D,O)}"value"in M&&o(y,"value",x.value,M.value,D)}},L=(y,x,M,O,D,T,V,H,z)=>{const I=x.el=y?y.el:a(""),J=x.anchor=y?y.anchor:a("");let{patchFlag:W,dynamicChildren:Y,slotScopeIds:Q}=x;Q&&(H=H?H.concat(Q):Q),y==null?(s(I,M,O),s(J,M,O),E(x.children||[],M,J,D,T,V,H,z)):W>0&&W&64&&Y&&y.dynamicChildren?(F(y.dynamicChildren,Y,M,D,T,V,H),(x.key!=null||D&&x===D.subTree)&&Va(y,x,!0)):et(y,x,M,J,D,T,V,H,z)},X=(y,x,M,O,D,T,V,H,z)=>{x.slotScopeIds=H,y==null?x.shapeFlag&512?D.ctx.activate(x,M,O,V,z):rt(x,M,O,D,T,V,z):Z(y,x,z)},rt=(y,x,M,O,D,T,V)=>{const H=y.component=Qg(y,O,D);if(zf(y)&&(H.ctx.renderer=K),Zg(H,!1,V),H.asyncDep){if(D&&D.registerDep(H,G,V),!y.el){const z=H.subTree=jt(ve);_(null,z,x,M)}}else G(H,y,x,M,D,T,V)},Z=(y,x,M)=>{const O=x.component=y.component;if(jg(y,x,M))if(O.asyncDep&&!O.asyncResolved){U(O,x,M);return}else O.next=x,O.update();else x.el=y.el,O.vnode=x},G=(y,x,M,O,D,T,V)=>{const H=()=>{if(y.isMounted){let{next:W,bu:Y,u:Q,parent:at,vnode:bt}=y;{const Ee=sh(y);if(Ee){W&&(W.el=bt.el,U(y,W,V)),Ee.asyncDep.then(()=>{y.isUnmounted||H()});return}}let ft=W,ne;wn(y,!1),W?(W.el=bt.el,U(y,W,V)):W=bt,Y&&es(Y),(ne=W.props&&W.props.onVnodeBeforeUpdate)&&ue(ne,at,W,bt),wn(y,!0);const Gt=Pl(y),Ce=y.subTree;y.subTree=Gt,g(Ce,Gt,f(Ce.el),R(Ce),y,D,T),W.el=Gt.el,ft===null&&Wg(y,Gt.el),Q&&Rt(Q,D),(ne=W.props&&W.props.onVnodeUpdated)&&Rt(()=>ue(ne,at,W,bt),D)}else{let W;const{el:Y,props:Q}=x,{bm:at,m:bt,parent:ft,root:ne,type:Gt}=y,Ce=In(x);wn(y,!1),at&&es(at),!Ce&&(W=Q&&Q.onVnodeBeforeMount)&&ue(W,ft,x),wn(y,!0);{ne.ce&&ne.ce._injectChildStyle(Gt);const Ee=y.subTree=Pl(y);g(null,Ee,M,O,y,D,T),x.el=Ee.el}if(bt&&Rt(bt,D),!Ce&&(W=Q&&Q.onVnodeMounted)){const Ee=x;Rt(()=>ue(W,ft,Ee),D)}(x.shapeFlag&256||ft&&In(ft.vnode)&&ft.vnode.shapeFlag&256)&&y.a&&Rt(y.a,D),y.isMounted=!0,x=M=O=null}};y.scope.on();const z=y.effect=new uf(H);y.scope.off();const I=y.update=z.run.bind(z),J=y.job=z.runIfDirty.bind(z);J.i=y,J.id=y.uid,z.scheduler=()=>Fa(J),wn(y,!0),I()},U=(y,x,M)=>{x.component=y;const O=y.vnode.props;y.vnode=x,y.next=null,Eg(y,x.props,O,M),Og(y,x.children,M),Xe(),bl(y),Ge()},et=(y,x,M,O,D,T,V,H,z=!1)=>{const I=y&&y.children,J=y?y.shapeFlag:0,W=x.children,{patchFlag:Y,shapeFlag:Q}=x;if(Y>0){if(Y&128){Ut(I,W,M,O,D,T,V,H,z);return}else if(Y&256){mt(I,W,M,O,D,T,V,H,z);return}}Q&8?(J&16&&At(I,D,T),W!==I&&u(M,W)):J&16?Q&16?Ut(I,W,M,O,D,T,V,H,z):At(I,D,T,!0):(J&8&&u(M,""),Q&16&&E(W,M,O,D,T,V,H,z))},mt=(y,x,M,O,D,T,V,H,z)=>{y=y||Zn,x=x||Zn;const I=y.length,J=x.length,W=Math.min(I,J);let Y;for(Y=0;Y<W;Y++){const Q=x[Y]=z?on(x[Y]):De(x[Y]);g(y[Y],Q,M,null,D,T,V,H,z)}I>J?At(y,D,T,!0,!1,W):E(x,M,O,D,T,V,H,z,W)},Ut=(y,x,M,O,D,T,V,H,z)=>{let I=0;const J=x.length;let W=y.length-1,Y=J-1;for(;I<=W&&I<=Y;){const Q=y[I],at=x[I]=z?on(x[I]):De(x[I]);if(Tn(Q,at))g(Q,at,M,null,D,T,V,H,z);else break;I++}for(;I<=W&&I<=Y;){const Q=y[W],at=x[Y]=z?on(x[Y]):De(x[Y]);if(Tn(Q,at))g(Q,at,M,null,D,T,V,H,z);else break;W--,Y--}if(I>W){if(I<=Y){const Q=Y+1,at=Q<J?x[Q].el:O;for(;I<=Y;)g(null,x[I]=z?on(x[I]):De(x[I]),M,at,D,T,V,H,z),I++}}else if(I>Y)for(;I<=W;)Et(y[I],D,T,!0),I++;else{const Q=I,at=I,bt=new Map;for(I=at;I<=Y;I++){const se=x[I]=z?on(x[I]):De(x[I]);se.key!=null&&bt.set(se.key,I)}let ft,ne=0;const Gt=Y-at+1;let Ce=!1,Ee=0;const ys=new Array(Gt);for(I=0;I<Gt;I++)ys[I]=0;for(I=Q;I<=W;I++){const se=y[I];if(ne>=Gt){Et(se,D,T,!0);continue}let Pe;if(se.key!=null)Pe=bt.get(se.key);else for(ft=at;ft<=Y;ft++)if(ys[ft-at]===0&&Tn(se,x[ft])){Pe=ft;break}Pe===void 0?Et(se,D,T,!0):(ys[Pe-at]=I+1,Pe>=Ee?Ee=Pe:Ce=!0,g(se,x[Pe],M,null,D,T,V,H,z),ne++)}const fl=Ce?Lg(ys):Zn;for(ft=fl.length-1,I=Gt-1;I>=0;I--){const se=at+I,Pe=x[se],hl=se+1<J?x[se+1].el:O;ys[I]===0?g(null,Pe,M,hl,D,T,V,H,z):Ce&&(ft<0||I!==fl[ft]?Kt(Pe,M,hl,2):ft--)}}},Kt=(y,x,M,O,D=null)=>{const{el:T,type:V,transition:H,children:z,shapeFlag:I}=y;if(I&6){Kt(y.component.subTree,x,M,O);return}if(I&128){y.suspense.move(x,M,O);return}if(I&64){V.move(y,x,M,K);return}if(V===Qt){s(T,x,M);for(let W=0;W<z.length;W++)Kt(z[W],x,M,O);s(y.anchor,x,M);return}if(V===Wi){w(y,x,M);return}if(O!==2&&I&1&&H)if(O===0)H.beforeEnter(T),s(T,x,M),Rt(()=>H.enter(T),D);else{const{leave:W,delayLeave:Y,afterLeave:Q}=H,at=()=>{y.ctx.isUnmounted?i(T):s(T,x,M)},bt=()=>{W(T,()=>{at(),Q&&Q()})};Y?Y(T,at,bt):bt()}else s(T,x,M)},Et=(y,x,M,O=!1,D=!1)=>{const{type:T,props:V,ref:H,children:z,dynamicChildren:I,shapeFlag:J,patchFlag:W,dirs:Y,cacheIndex:Q}=y;if(W===-2&&(D=!1),H!=null&&(Xe(),uo(H,null,M,y,!0),Ge()),Q!=null&&(x.renderCache[Q]=void 0),J&256){x.ctx.deactivate(y);return}const at=J&1&&Y,bt=!In(y);let ft;if(bt&&(ft=V&&V.onVnodeBeforeUnmount)&&ue(ft,x,y),J&6)Me(y.component,M,O);else{if(J&128){y.suspense.unmount(M,O);return}at&&vn(y,null,x,"beforeUnmount"),J&64?y.type.remove(y,x,M,K,O):I&&!I.hasOnce&&(T!==Qt||W>0&&W&64)?At(I,x,M,!1,!0):(T===Qt&&W&384||!D&&J&16)&&At(z,x,M),O&&ae(y)}(bt&&(ft=V&&V.onVnodeUnmounted)||at)&&Rt(()=>{ft&&ue(ft,x,y),at&&vn(y,null,x,"unmounted")},M)},ae=y=>{const{type:x,el:M,anchor:O,transition:D}=y;if(x===Qt){Xt(M,O);return}if(x===Wi){v(y);return}const T=()=>{i(M),D&&!D.persisted&&D.afterLeave&&D.afterLeave()};if(y.shapeFlag&1&&D&&!D.persisted){const{leave:V,delayLeave:H}=D,z=()=>V(M,T);H?H(y.el,T,z):z()}else T()},Xt=(y,x)=>{let M;for(;y!==x;)M=h(y),i(y),y=M;i(x)},Me=(y,x,M)=>{const{bum:O,scope:D,job:T,subTree:V,um:H,m:z,a:I,parent:J,slots:{__:W}}=y;ho(z),ho(I),O&&es(O),J&&q(W)&&W.forEach(Y=>{J.renderCache[Y]=void 0}),D.stop(),T&&(T.flags|=8,Et(V,y,x,M)),H&&Rt(H,x),Rt(()=>{y.isUnmounted=!0},x),x&&x.pendingBranch&&!x.isUnmounted&&y.asyncDep&&!y.asyncResolved&&y.suspenseId===x.pendingId&&(x.deps--,x.deps===0&&x.resolve())},At=(y,x,M,O=!1,D=!1,T=0)=>{for(let V=T;V<y.length;V++)Et(y[V],x,M,O,D)},R=y=>{if(y.shapeFlag&6)return R(y.component.subTree);if(y.shapeFlag&128)return y.suspense.next();const x=h(y.anchor||y.el),M=x&&x[Df];return M?h(M):x};let $=!1;const j=(y,x,M)=>{y==null?x._vnode&&Et(x._vnode,null,null,!0):g(x._vnode||null,y,x,null,null,null,M),x._vnode=y,$||($=!0,bl(),Of(),$=!1)},K={p:g,um:Et,m:Kt,r:ae,mt:rt,mc:E,pc:et,pbc:F,n:R,o:e};return{render:j,hydrate:void 0,createApp:Sg(j)}}function ar({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function wn({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Dg(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Va(e,t,n=!1){const s=e.children,i=t.children;if(q(s)&&q(i))for(let o=0;o<s.length;o++){const r=s[o];let a=i[o];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=i[o]=on(i[o]),a.el=r.el),!n&&a.patchFlag!==-2&&Va(r,a)),a.type===Io&&(a.el=r.el),a.type===ve&&!a.el&&(a.el=r.el)}}function Lg(e){const t=e.slice(),n=[0];let s,i,o,r,a;const l=e.length;for(s=0;s<l;s++){const c=e[s];if(c!==0){if(i=n[n.length-1],e[i]<c){t[s]=i,n.push(s);continue}for(o=0,r=n.length-1;o<r;)a=o+r>>1,e[n[a]]<c?o=a+1:r=a;c<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,r=n[o-1];o-- >0;)n[o]=r,r=t[r];return n}function sh(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:sh(t)}function ho(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Fg=Symbol.for("v-scx"),Ig=()=>de(Fg);function is(e,t,n){return ih(e,t,n)}function ih(e,t,n=pt){const{immediate:s,deep:i,flush:o,once:r}=n,a=Lt({},n),l=t&&s||!t&&o!=="post";let c;if(ei){if(o==="sync"){const d=Ig();c=d.__watcherHandles||(d.__watcherHandles=[])}else if(!l){const d=()=>{};return d.stop=Le,d.resume=Le,d.pause=Le,d}}const u=Ft;a.call=(d,p,g)=>xe(d,u,p,g);let f=!1;o==="post"?a.scheduler=d=>{Rt(d,u&&u.suspense)}:o!=="sync"&&(f=!0,a.scheduler=(d,p)=>{p?d():Fa(d)}),a.augmentJob=d=>{t&&(d.flags|=4),f&&(d.flags|=2,u&&(d.id=u.uid,d.i=u))};const h=Gp(e,t,a);return ei&&(c?c.push(h):l&&h()),h}function Ng(e,t,n){const s=this.proxy,i=Mt(e)?e.includes(".")?oh(s,e):()=>s[e]:e.bind(s,s);let o;tt(t)?o=t:(o=t.handler,n=t);const r=gi(this),a=ih(i,o.bind(s),n);return r(),a}function oh(e,t){const n=t.split(".");return()=>{let s=e;for(let i=0;i<n.length&&s;i++)s=s[n[i]];return s}}const Bg=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${ge(t)}Modifiers`]||e[`${yn(t)}Modifiers`];function zg(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||pt;let i=n;const o=t.startsWith("update:"),r=o&&Bg(s,t.slice(7));r&&(r.trim&&(i=n.map(u=>Mt(u)?u.trim():u)),r.number&&(i=n.map(io)));let a,l=s[a=tr(t)]||s[a=tr(ge(t))];!l&&o&&(l=s[a=tr(yn(t))]),l&&xe(l,e,6,i);const c=s[a+"Once"];if(c){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,xe(c,e,6,i)}}function rh(e,t,n=!1){const s=t.emitsCache,i=s.get(e);if(i!==void 0)return i;const o=e.emits;let r={},a=!1;if(!tt(e)){const l=c=>{const u=rh(c,t,!0);u&&(a=!0,Lt(r,u))};!n&&t.mixins.length&&t.mixins.forEach(l),e.extends&&l(e.extends),e.mixins&&e.mixins.forEach(l)}return!o&&!a?(gt(e)&&s.set(e,null),null):(q(o)?o.forEach(l=>r[l]=null):Lt(r,o),gt(e)&&s.set(e,r),r)}function Fo(e,t){return!e||!Po(t)?!1:(t=t.slice(2).replace(/Once$/,""),ut(e,t[0].toLowerCase()+t.slice(1))||ut(e,yn(t))||ut(e,t))}function Pl(e){const{type:t,vnode:n,proxy:s,withProxy:i,propsOptions:[o],slots:r,attrs:a,emit:l,render:c,renderCache:u,props:f,data:h,setupState:d,ctx:p,inheritAttrs:g}=e,m=co(e);let _,b;try{if(n.shapeFlag&4){const v=i||s,S=v;_=De(c.call(S,v,u,f,d,h,p)),b=a}else{const v=t;_=De(v.length>1?v(f,{attrs:a,slots:r,emit:l}):v(f,null)),b=t.props?a:Hg(a)}}catch(v){js.length=0,To(v,e,1),_=jt(ve)}let w=_;if(b&&g!==!1){const v=Object.keys(b),{shapeFlag:S}=w;v.length&&S&7&&(o&&v.some(xa)&&(b=Vg(b,o)),w=gn(w,b,!1,!0))}return n.dirs&&(w=gn(w,null,!1,!0),w.dirs=w.dirs?w.dirs.concat(n.dirs):n.dirs),n.transition&&rs(w,n.transition),_=w,co(m),_}const Hg=e=>{let t;for(const n in e)(n==="class"||n==="style"||Po(n))&&((t||(t={}))[n]=e[n]);return t},Vg=(e,t)=>{const n={};for(const s in e)(!xa(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function jg(e,t,n){const{props:s,children:i,component:o}=e,{props:r,children:a,patchFlag:l}=t,c=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&l>=0){if(l&1024)return!0;if(l&16)return s?kl(s,r,c):!!r;if(l&8){const u=t.dynamicProps;for(let f=0;f<u.length;f++){const h=u[f];if(r[h]!==s[h]&&!Fo(c,h))return!0}}}else return(i||a)&&(!a||!a.$stable)?!0:s===r?!1:s?r?kl(s,r,c):!0:!!r;return!1}function kl(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let i=0;i<s.length;i++){const o=s[i];if(t[o]!==e[o]&&!Fo(n,o))return!0}return!1}function Wg({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const po=e=>e.__isSuspense;function $g(e,t){t&&t.pendingBranch?q(e)?t.effects.push(...e):t.effects.push(e):Zp(e)}const Qt=Symbol.for("v-fgt"),Io=Symbol.for("v-txt"),ve=Symbol.for("v-cmt"),Wi=Symbol.for("v-stc"),js=[];let re=null;function Hr(e=!1){js.push(re=e?null:[])}function Ug(){js.pop(),re=js[js.length-1]||null}let ti=1;function Al(e,t=!1){ti+=e,e<0&&re&&t&&(re.hasOnce=!0)}function ah(e){return e.dynamicChildren=ti>0?re||Zn:null,Ug(),ti>0&&re&&re.push(e),e}function NS(e,t,n,s,i,o){return ah(ch(e,t,n,s,i,o,!0))}function Vr(e,t,n,s,i){return ah(jt(e,t,n,s,i,!0))}function as(e){return e?e.__v_isVNode===!0:!1}function Tn(e,t){return e.type===t.type&&e.key===t.key}const lh=({key:e})=>e??null,$i=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?Mt(e)||kt(e)||tt(e)?{i:Tt,r:e,k:t,f:!!n}:e:null);function ch(e,t=null,n=null,s=0,i=null,o=e===Qt?0:1,r=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&lh(t),ref:t&&$i(t),scopeId:Tf,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:Tt};return a?(ja(l,n),o&128&&e.normalize(l)):n&&(l.shapeFlag|=Mt(n)?8:16),ti>0&&!r&&re&&(l.patchFlag>0||o&6)&&l.patchFlag!==32&&re.push(l),l}const jt=Kg;function Kg(e,t=null,n=null,s=0,i=null,o=!1){if((!e||e===Wf)&&(e=ve),as(e)){const a=gn(e,t,!0);return n&&ja(a,n),ti>0&&!o&&re&&(a.shapeFlag&6?re[re.indexOf(e)]=a:re.push(a)),a.patchFlag=-2,a}if(sm(e)&&(e=e.__vccOpts),t){t=qg(t);let{class:a,style:l}=t;a&&!Mt(a)&&(t.class=Ma(a)),gt(l)&&(Ta(l)&&!q(l)&&(l=Lt({},l)),t.style=Sa(l))}const r=Mt(e)?1:po(e)?128:eg(e)?64:gt(e)?4:tt(e)?2:0;return ch(e,t,n,s,i,r,o,!0)}function qg(e){return e?Ta(e)||Jf(e)?Lt({},e):e:null}function gn(e,t,n=!1,s=!1){const{props:i,ref:o,patchFlag:r,children:a,transition:l}=e,c=t?Xg(i||{},t):i,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:c,key:c&&lh(c),ref:t&&t.ref?n&&o?q(o)?o.concat($i(t)):[o,$i(t)]:$i(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Qt?r===-1?16:r|16:r,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:l,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&gn(e.ssContent),ssFallback:e.ssFallback&&gn(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return l&&s&&rs(u,l.clone(u)),u}function Yg(e=" ",t=0){return jt(Io,null,e,t)}function BS(e,t){const n=jt(Wi,null,e);return n.staticCount=t,n}function zS(e="",t=!1){return t?(Hr(),Vr(ve,null,e)):jt(ve,null,e)}function De(e){return e==null||typeof e=="boolean"?jt(ve):q(e)?jt(Qt,null,e.slice()):as(e)?on(e):jt(Io,null,String(e))}function on(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:gn(e)}function ja(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(q(t))n=16;else if(typeof t=="object")if(s&65){const i=t.default;i&&(i._c&&(i._d=!1),ja(e,i()),i._c&&(i._d=!0));return}else{n=32;const i=t._;!i&&!Jf(t)?t._ctx=Tt:i===3&&Tt&&(Tt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else tt(t)?(t={default:t,_ctx:Tt},n=32):(t=String(t),s&64?(n=16,t=[Yg(t)]):n=8);e.children=t,e.shapeFlag|=n}function Xg(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const i in s)if(i==="class")t.class!==s.class&&(t.class=Ma([t.class,s.class]));else if(i==="style")t.style=Sa([t.style,s.style]);else if(Po(i)){const o=t[i],r=s[i];r&&o!==r&&!(q(o)&&o.includes(r))&&(t[i]=o?[].concat(o,r):r)}else i!==""&&(t[i]=s[i])}return t}function ue(e,t,n,s=null){xe(e,t,7,[n,s])}const Gg=Yf();let Jg=0;function Qg(e,t,n){const s=e.type,i=(t?t.appContext:e.appContext)||Gg,o={uid:Jg++,vnode:e,type:s,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new af(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Zf(s,i),emitsOptions:rh(s,i),emit:null,emitted:null,propsDefaults:pt,inheritAttrs:s.inheritAttrs,ctx:pt,data:pt,props:pt,attrs:pt,slots:pt,refs:pt,setupState:pt,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=zg.bind(null,o),e.ce&&e.ce(o),o}let Ft=null;const uh=()=>Ft||Tt;let go,jr;{const e=Oo(),t=(n,s)=>{let i;return(i=e[n])||(i=e[n]=[]),i.push(s),o=>{i.length>1?i.forEach(r=>r(o)):i[0](o)}};go=t("__VUE_INSTANCE_SETTERS__",n=>Ft=n),jr=t("__VUE_SSR_SETTERS__",n=>ei=n)}const gi=e=>{const t=Ft;return go(e),e.scope.on(),()=>{e.scope.off(),go(t)}},Ol=()=>{Ft&&Ft.scope.off(),go(null)};function fh(e){return e.vnode.shapeFlag&4}let ei=!1;function Zg(e,t=!1,n=!1){t&&jr(t);const{props:s,children:i}=e.vnode,o=fh(e);Cg(e,s,o,t),Ag(e,i,n||t);const r=o?tm(e,t):void 0;return t&&jr(!1),r}function tm(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,mg);const{setup:s}=n;if(s){Xe();const i=e.setupContext=s.length>1?nm(e):null,o=gi(e),r=pi(s,e,0,[e.props,i]),a=Zu(r);if(Ge(),o(),(a||e.sp)&&!In(e)&&Bf(e),a){if(r.then(Ol,Ol),t)return r.then(l=>{Rl(e,l)}).catch(l=>{To(l,e,0)});e.asyncDep=r}else Rl(e,r)}else hh(e)}function Rl(e,t,n){tt(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:gt(t)&&(e.setupState=Pf(t)),hh(e)}function hh(e,t,n){const s=e.type;e.render||(e.render=s.render||Le);{const i=gi(e);Xe();try{bg(e)}finally{Ge(),i()}}}const em={get(e,t){return Ht(e,"get",""),e[t]}};function nm(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,em),slots:e.slots,emit:e.emit,expose:t}}function No(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Pf(Da(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Vs)return Vs[n](e)},has(t,n){return n in t||n in Vs}})):e.proxy}function Wr(e,t=!0){return tt(e)?e.displayName||e.name:e.name||t&&e.__name}function sm(e){return tt(e)&&"__vccOpts"in e}const fe=(e,t)=>Yp(e,t,ei);function dh(e,t,n){const s=arguments.length;return s===2?gt(t)&&!q(t)?as(t)?jt(e,null,[t]):jt(e,t):jt(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&as(n)&&(n=[n]),jt(e,t,n))}const im="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let $r;const Tl=typeof window<"u"&&window.trustedTypes;if(Tl)try{$r=Tl.createPolicy("vue",{createHTML:e=>e})}catch{}const ph=$r?e=>$r.createHTML(e):e=>e,om="http://www.w3.org/2000/svg",rm="http://www.w3.org/1998/Math/MathML",Ve=typeof document<"u"?document:null,Dl=Ve&&Ve.createElement("template"),am={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const i=t==="svg"?Ve.createElementNS(om,e):t==="mathml"?Ve.createElementNS(rm,e):n?Ve.createElement(e,{is:n}):Ve.createElement(e);return e==="select"&&s&&s.multiple!=null&&i.setAttribute("multiple",s.multiple),i},createText:e=>Ve.createTextNode(e),createComment:e=>Ve.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ve.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,i,o){const r=n?n.previousSibling:t.lastChild;if(i&&(i===o||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),n),!(i===o||!(i=i.nextSibling)););else{Dl.innerHTML=ph(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const a=Dl.content;if(s==="svg"||s==="mathml"){const l=a.firstChild;for(;l.firstChild;)a.appendChild(l.firstChild);a.removeChild(l)}t.insertBefore(a,n)}return[r?r.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Ze="transition",vs="animation",ls=Symbol("_vtc"),gh={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},lm=Lt({},ig,gh),Sn=(e,t=[])=>{q(e)?e.forEach(n=>n(...t)):e&&e(...t)},Ll=e=>e?q(e)?e.some(t=>t.length>1):e.length>1:!1;function cm(e){const t={};for(const L in e)L in gh||(t[L]=e[L]);if(e.css===!1)return t;const{name:n="v",type:s,duration:i,enterFromClass:o=`${n}-enter-from`,enterActiveClass:r=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:l=o,appearActiveClass:c=r,appearToClass:u=a,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:h=`${n}-leave-active`,leaveToClass:d=`${n}-leave-to`}=e,p=um(i),g=p&&p[0],m=p&&p[1],{onBeforeEnter:_,onEnter:b,onEnterCancelled:w,onLeave:v,onLeaveCancelled:S,onBeforeAppear:A=_,onAppear:k=b,onAppearCancelled:E=w}=t,C=(L,X,rt,Z)=>{L._enterCancelled=Z,en(L,X?u:a),en(L,X?c:r),rt&&rt()},F=(L,X)=>{L._isLeaving=!1,en(L,f),en(L,d),en(L,h),X&&X()},B=L=>(X,rt)=>{const Z=L?k:b,G=()=>C(X,L,rt);Sn(Z,[X,G]),Fl(()=>{en(X,L?l:o),Ae(X,L?u:a),Ll(Z)||Il(X,s,g,G)})};return Lt(t,{onBeforeEnter(L){Sn(_,[L]),Ae(L,o),Ae(L,r)},onBeforeAppear(L){Sn(A,[L]),Ae(L,l),Ae(L,c)},onEnter:B(!1),onAppear:B(!0),onLeave(L,X){L._isLeaving=!0;const rt=()=>F(L,X);Ae(L,f),L._enterCancelled?(Ae(L,h),Ur()):(Ur(),Ae(L,h)),Fl(()=>{L._isLeaving&&(en(L,f),Ae(L,d),Ll(v)||Il(L,s,m,rt))}),Sn(v,[L,rt])},onEnterCancelled(L){C(L,!1,void 0,!0),Sn(w,[L])},onAppearCancelled(L){C(L,!0,void 0,!0),Sn(E,[L])},onLeaveCancelled(L){F(L),Sn(S,[L])}})}function um(e){if(e==null)return null;if(gt(e))return[lr(e.enter),lr(e.leave)];{const t=lr(e);return[t,t]}}function lr(e){return hp(e)}function Ae(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[ls]||(e[ls]=new Set)).add(t)}function en(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const n=e[ls];n&&(n.delete(t),n.size||(e[ls]=void 0))}function Fl(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let fm=0;function Il(e,t,n,s){const i=e._endId=++fm,o=()=>{i===e._endId&&s()};if(n!=null)return setTimeout(o,n);const{type:r,timeout:a,propCount:l}=mh(e,t);if(!r)return s();const c=r+"end";let u=0;const f=()=>{e.removeEventListener(c,h),o()},h=d=>{d.target===e&&++u>=l&&f()};setTimeout(()=>{u<l&&f()},a+1),e.addEventListener(c,h)}function mh(e,t){const n=window.getComputedStyle(e),s=p=>(n[p]||"").split(", "),i=s(`${Ze}Delay`),o=s(`${Ze}Duration`),r=Nl(i,o),a=s(`${vs}Delay`),l=s(`${vs}Duration`),c=Nl(a,l);let u=null,f=0,h=0;t===Ze?r>0&&(u=Ze,f=r,h=o.length):t===vs?c>0&&(u=vs,f=c,h=l.length):(f=Math.max(r,c),u=f>0?r>c?Ze:vs:null,h=u?u===Ze?o.length:l.length:0);const d=u===Ze&&/\b(transform|all)(,|$)/.test(s(`${Ze}Property`).toString());return{type:u,timeout:f,propCount:h,hasTransform:d}}function Nl(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>Bl(n)+Bl(e[s])))}function Bl(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Ur(){return document.body.offsetHeight}function hm(e,t,n){const s=e[ls];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const zl=Symbol("_vod"),dm=Symbol("_vsh"),pm=Symbol(""),gm=/(^|;)\s*display\s*:/;function mm(e,t,n){const s=e.style,i=Mt(n);let o=!1;if(n&&!i){if(t)if(Mt(t))for(const r of t.split(";")){const a=r.slice(0,r.indexOf(":")).trim();n[a]==null&&Ui(s,a,"")}else for(const r in t)n[r]==null&&Ui(s,r,"");for(const r in n)r==="display"&&(o=!0),Ui(s,r,n[r])}else if(i){if(t!==n){const r=s[pm];r&&(n+=";"+r),s.cssText=n,o=gm.test(n)}}else t&&e.removeAttribute("style");zl in e&&(e[zl]=o?s.display:"",e[dm]&&(s.display="none"))}const Hl=/\s*!important$/;function Ui(e,t,n){if(q(n))n.forEach(s=>Ui(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=bm(e,t);Hl.test(n)?e.setProperty(yn(s),n.replace(Hl,""),"important"):e[s]=n}}const Vl=["Webkit","Moz","ms"],cr={};function bm(e,t){const n=cr[t];if(n)return n;let s=ge(t);if(s!=="filter"&&s in e)return cr[t]=s;s=Ao(s);for(let i=0;i<Vl.length;i++){const o=Vl[i]+s;if(o in e)return cr[t]=o}return t}const jl="http://www.w3.org/1999/xlink";function Wl(e,t,n,s,i,o=_p(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(jl,t.slice(6,t.length)):e.setAttributeNS(jl,t,n):n==null||o&&!sf(n)?e.removeAttribute(t):e.setAttribute(t,o?"":ye(n)?String(n):n)}function $l(e,t,n,s,i){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?ph(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const a=o==="OPTION"?e.getAttribute("value")||"":e.value,l=n==null?e.type==="checkbox"?"on":"":String(n);(a!==l||!("_value"in e))&&(e.value=l),n==null&&e.removeAttribute(t),e._value=n;return}let r=!1;if(n===""||n==null){const a=typeof e[t];a==="boolean"?n=sf(n):n==null&&a==="string"?(n="",r=!0):a==="number"&&(n=0,r=!0)}try{e[t]=n}catch{}r&&e.removeAttribute(i||t)}function Ue(e,t,n,s){e.addEventListener(t,n,s)}function _m(e,t,n,s){e.removeEventListener(t,n,s)}const Ul=Symbol("_vei");function ym(e,t,n,s,i=null){const o=e[Ul]||(e[Ul]={}),r=o[t];if(s&&r)r.value=s;else{const[a,l]=xm(t);if(s){const c=o[t]=Sm(s,i);Ue(e,a,c,l)}else r&&(_m(e,a,r,l),o[t]=void 0)}}const Kl=/(?:Once|Passive|Capture)$/;function xm(e){let t;if(Kl.test(e)){t={};let s;for(;s=e.match(Kl);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):yn(e.slice(2)),t]}let ur=0;const vm=Promise.resolve(),wm=()=>ur||(vm.then(()=>ur=0),ur=Date.now());function Sm(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;xe(Mm(s,n.value),t,5,[s])};return n.value=e,n.attached=wm(),n}function Mm(e,t){if(q(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>i=>!i._stopped&&s&&s(i))}else return t}const ql=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Cm=(e,t,n,s,i,o)=>{const r=i==="svg";t==="class"?hm(e,s,r):t==="style"?mm(e,n,s):Po(t)?xa(t)||ym(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Em(e,t,s,r))?($l(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Wl(e,t,s,r,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Mt(s))?$l(e,ge(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),Wl(e,t,s,r))};function Em(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&ql(t)&&tt(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const i=e.tagName;if(i==="IMG"||i==="VIDEO"||i==="CANVAS"||i==="SOURCE")return!1}return ql(t)&&Mt(n)?!1:t in e}const bh=new WeakMap,_h=new WeakMap,mo=Symbol("_moveCb"),Yl=Symbol("_enterCb"),Pm=e=>(delete e.props.mode,e),km=Pm({name:"TransitionGroup",props:Lt({},lm,{tag:String,moveClass:String}),setup(e,{slots:t}){const n=uh(),s=sg();let i,o;return Na(()=>{if(!i.length)return;const r=e.moveClass||`${e.name||"v"}-move`;if(!Tm(i[0].el,n.vnode.el,r)){i=[];return}i.forEach(Am),i.forEach(Om);const a=i.filter(Rm);Ur(),a.forEach(l=>{const c=l.el,u=c.style;Ae(c,r),u.transform=u.webkitTransform=u.transitionDuration="";const f=c[mo]=h=>{h&&h.target!==c||(!h||/transform$/.test(h.propertyName))&&(c.removeEventListener("transitionend",f),c[mo]=null,en(c,r))};c.addEventListener("transitionend",f)}),i=[]}),()=>{const r=lt(e),a=cm(r);let l=r.tag||Qt;if(i=[],o)for(let c=0;c<o.length;c++){const u=o[c];u.el&&u.el instanceof Element&&(i.push(u),rs(u,Fr(u,a,s,n)),bh.set(u,u.el.getBoundingClientRect()))}o=t.default?If(t.default()):[];for(let c=0;c<o.length;c++){const u=o[c];u.key!=null&&rs(u,Fr(u,a,s,n))}return jt(l,null,o)}}}),HS=km;function Am(e){const t=e.el;t[mo]&&t[mo](),t[Yl]&&t[Yl]()}function Om(e){_h.set(e,e.el.getBoundingClientRect())}function Rm(e){const t=bh.get(e),n=_h.get(e),s=t.left-n.left,i=t.top-n.top;if(s||i){const o=e.el.style;return o.transform=o.webkitTransform=`translate(${s}px,${i}px)`,o.transitionDuration="0s",e}}function Tm(e,t,n){const s=e.cloneNode(),i=e[ls];i&&i.forEach(a=>{a.split(/\s+/).forEach(l=>l&&s.classList.remove(l))}),n.split(/\s+/).forEach(a=>a&&s.classList.add(a)),s.style.display="none";const o=t.nodeType===1?t:t.parentNode;o.appendChild(s);const{hasTransform:r}=mh(s);return o.removeChild(s),r}const mn=e=>{const t=e.props["onUpdate:modelValue"]||!1;return q(t)?n=>es(t,n):t};function Dm(e){e.target.composing=!0}function Xl(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const pe=Symbol("_assign"),VS={created(e,{modifiers:{lazy:t,trim:n,number:s}},i){e[pe]=mn(i);const o=s||i.props&&i.props.type==="number";Ue(e,t?"change":"input",r=>{if(r.target.composing)return;let a=e.value;n&&(a=a.trim()),o&&(a=io(a)),e[pe](a)}),n&&Ue(e,"change",()=>{e.value=e.value.trim()}),t||(Ue(e,"compositionstart",Dm),Ue(e,"compositionend",Xl),Ue(e,"change",Xl))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:i,number:o}},r){if(e[pe]=mn(r),e.composing)return;const a=(o||e.type==="number")&&!/^0\d/.test(e.value)?io(e.value):e.value,l=t??"";a!==l&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||i&&e.value.trim()===l)||(e.value=l))}},jS={deep:!0,created(e,t,n){e[pe]=mn(n),Ue(e,"change",()=>{const s=e._modelValue,i=cs(e),o=e.checked,r=e[pe];if(q(s)){const a=Ca(s,i),l=a!==-1;if(o&&!l)r(s.concat(i));else if(!o&&l){const c=[...s];c.splice(a,1),r(c)}}else if(gs(s)){const a=new Set(s);o?a.add(i):a.delete(i),r(a)}else r(yh(e,o))})},mounted:Gl,beforeUpdate(e,t,n){e[pe]=mn(n),Gl(e,t,n)}};function Gl(e,{value:t,oldValue:n},s){e._modelValue=t;let i;if(q(t))i=Ca(t,s.props.value)>-1;else if(gs(t))i=t.has(s.props.value);else{if(t===n)return;i=Vn(t,yh(e,!0))}e.checked!==i&&(e.checked=i)}const WS={created(e,{value:t},n){e.checked=Vn(t,n.props.value),e[pe]=mn(n),Ue(e,"change",()=>{e[pe](cs(e))})},beforeUpdate(e,{value:t,oldValue:n},s){e[pe]=mn(s),t!==n&&(e.checked=Vn(t,s.props.value))}},$S={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const i=gs(t);Ue(e,"change",()=>{const o=Array.prototype.filter.call(e.options,r=>r.selected).map(r=>n?io(cs(r)):cs(r));e[pe](e.multiple?i?new Set(o):o:o[0]),e._assigning=!0,Do(()=>{e._assigning=!1})}),e[pe]=mn(s)},mounted(e,{value:t}){Jl(e,t)},beforeUpdate(e,t,n){e[pe]=mn(n)},updated(e,{value:t}){e._assigning||Jl(e,t)}};function Jl(e,t){const n=e.multiple,s=q(t);if(!(n&&!s&&!gs(t))){for(let i=0,o=e.options.length;i<o;i++){const r=e.options[i],a=cs(r);if(n)if(s){const l=typeof a;l==="string"||l==="number"?r.selected=t.some(c=>String(c)===String(a)):r.selected=Ca(t,a)>-1}else r.selected=t.has(a);else if(Vn(cs(r),t)){e.selectedIndex!==i&&(e.selectedIndex=i);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function cs(e){return"_value"in e?e._value:e.value}function yh(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const Lm=["ctrl","shift","alt","meta"],Fm={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Lm.some(n=>e[`${n}Key`]&&!t.includes(n))},US=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(i,...o)=>{for(let r=0;r<t.length;r++){const a=Fm[t[r]];if(a&&a(i,t))return}return e(i,...o)})},Im={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},KS=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=i=>{if(!("key"in i))return;const o=yn(i.key);if(t.some(r=>r===o||Im[r]===o))return e(i)})},Nm=Lt({patchProp:Cm},am);let Ql;function Bm(){return Ql||(Ql=Rg(Nm))}const qS=(...e)=>{const t=Bm().createApp(...e),{mount:n}=t;return t.mount=s=>{const i=Hm(s);if(!i)return;const o=t._component;!tt(o)&&!o.render&&!o.template&&(o.template=i.innerHTML),i.nodeType===1&&(i.textContent="");const r=n(i,!1,zm(i));return i instanceof Element&&(i.removeAttribute("v-cloak"),i.setAttribute("data-v-app","")),r},t};function zm(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Hm(e){return Mt(e)?document.querySelector(e):e}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let xh;const Bo=e=>xh=e,vh=Symbol();function Kr(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var Ws;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Ws||(Ws={}));function YS(){const e=lf(!0),t=e.run(()=>La({}));let n=[],s=[];const i=Da({install(o){Bo(i),i._a=o,o.provide(vh,i),o.config.globalProperties.$pinia=i,s.forEach(r=>n.push(r)),s=[]},use(o){return this._a?n.push(o):s.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return i}const wh=()=>{};function Zl(e,t,n,s=wh){e.push(t);const i=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),s())};return!n&&cf()&&vp(i),i}function Yn(e,...t){e.slice().forEach(n=>{n(...t)})}const Vm=e=>e(),tc=Symbol(),fr=Symbol();function qr(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,s)=>e.set(s,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const s=t[n],i=e[n];Kr(i)&&Kr(s)&&e.hasOwnProperty(n)&&!kt(s)&&!hn(s)?e[n]=qr(i,s):e[n]=s}return e}const jm=Symbol();function Wm(e){return!Kr(e)||!e.hasOwnProperty(jm)}const{assign:nn}=Object;function $m(e){return!!(kt(e)&&e.effect)}function Um(e,t,n,s){const{state:i,actions:o,getters:r}=t,a=n.state.value[e];let l;function c(){a||(n.state.value[e]=i?i():{});const u=$p(n.state.value[e]);return nn(u,o,Object.keys(r||{}).reduce((f,h)=>(f[h]=Da(fe(()=>{Bo(n);const d=n._s.get(e);return r[h].call(d,d)})),f),{}))}return l=Sh(e,c,t,n,s,!0),l}function Sh(e,t,n={},s,i,o){let r;const a=nn({actions:{}},n),l={deep:!0};let c,u,f=[],h=[],d;const p=s.state.value[e];!o&&!p&&(s.state.value[e]={}),La({});let g;function m(E){let C;c=u=!1,typeof E=="function"?(E(s.state.value[e]),C={type:Ws.patchFunction,storeId:e,events:d}):(qr(s.state.value[e],E),C={type:Ws.patchObject,payload:E,storeId:e,events:d});const F=g=Symbol();Do().then(()=>{g===F&&(c=!0)}),u=!0,Yn(f,C,s.state.value[e])}const _=o?function(){const{state:C}=n,F=C?C():{};this.$patch(B=>{nn(B,F)})}:wh;function b(){r.stop(),f=[],h=[],s._s.delete(e)}const w=(E,C="")=>{if(tc in E)return E[fr]=C,E;const F=function(){Bo(s);const B=Array.from(arguments),L=[],X=[];function rt(U){L.push(U)}function Z(U){X.push(U)}Yn(h,{args:B,name:F[fr],store:S,after:rt,onError:Z});let G;try{G=E.apply(this&&this.$id===e?this:S,B)}catch(U){throw Yn(X,U),U}return G instanceof Promise?G.then(U=>(Yn(L,U),U)).catch(U=>(Yn(X,U),Promise.reject(U))):(Yn(L,G),G)};return F[tc]=!0,F[fr]=C,F},v={_p:s,$id:e,$onAction:Zl.bind(null,h),$patch:m,$reset:_,$subscribe(E,C={}){const F=Zl(f,E,C.detached,()=>B()),B=r.run(()=>is(()=>s.state.value[e],L=>{(C.flush==="sync"?u:c)&&E({storeId:e,type:Ws.direct,events:d},L)},nn({},l,C)));return F},$dispose:b},S=di(v);s._s.set(e,S);const k=(s._a&&s._a.runWithContext||Vm)(()=>s._e.run(()=>(r=lf()).run(()=>t({action:w}))));for(const E in k){const C=k[E];if(kt(C)&&!$m(C)||hn(C))o||(p&&Wm(C)&&(kt(C)?C.value=p[E]:qr(C,p[E])),s.state.value[e][E]=C);else if(typeof C=="function"){const F=w(C,E);k[E]=F,a.actions[E]=C}}return nn(S,k),nn(lt(S),k),Object.defineProperty(S,"$state",{get:()=>s.state.value[e],set:E=>{m(C=>{nn(C,E)})}}),s._p.forEach(E=>{nn(S,r.run(()=>E({store:S,app:s._a,pinia:s,options:a})))}),p&&o&&n.hydrate&&n.hydrate(S.$state,p),c=!0,u=!0,S}/*! #__NO_SIDE_EFFECTS__ */function XS(e,t,n){let s,i;const o=typeof t=="function";typeof e=="string"?(s=e,i=o?n:t):(i=e,s=e.id);function r(a,l){const c=Mg();return a=a||(c?de(vh,null):null),a&&Bo(a),a=xh,a._s.has(s)||(o?Sh(s,t,i,a):Um(s,i,a)),a._s.get(s)}return r.$id=s,r}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Qn=typeof document<"u";function Mh(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Km(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Mh(e.default)}const ct=Object.assign;function hr(e,t){const n={};for(const s in t){const i=t[s];n[s]=we(i)?i.map(e):e(i)}return n}const $s=()=>{},we=Array.isArray,Ch=/#/g,qm=/&/g,Ym=/\//g,Xm=/=/g,Gm=/\?/g,Eh=/\+/g,Jm=/%5B/g,Qm=/%5D/g,Ph=/%5E/g,Zm=/%60/g,kh=/%7B/g,tb=/%7C/g,Ah=/%7D/g,eb=/%20/g;function Wa(e){return encodeURI(""+e).replace(tb,"|").replace(Jm,"[").replace(Qm,"]")}function nb(e){return Wa(e).replace(kh,"{").replace(Ah,"}").replace(Ph,"^")}function Yr(e){return Wa(e).replace(Eh,"%2B").replace(eb,"+").replace(Ch,"%23").replace(qm,"%26").replace(Zm,"`").replace(kh,"{").replace(Ah,"}").replace(Ph,"^")}function sb(e){return Yr(e).replace(Xm,"%3D")}function ib(e){return Wa(e).replace(Ch,"%23").replace(Gm,"%3F")}function ob(e){return e==null?"":ib(e).replace(Ym,"%2F")}function ni(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const rb=/\/$/,ab=e=>e.replace(rb,"");function dr(e,t,n="/"){let s,i={},o="",r="";const a=t.indexOf("#");let l=t.indexOf("?");return a<l&&a>=0&&(l=-1),l>-1&&(s=t.slice(0,l),o=t.slice(l+1,a>-1?a:t.length),i=e(o)),a>-1&&(s=s||t.slice(0,a),r=t.slice(a,t.length)),s=fb(s??t,n),{fullPath:s+(o&&"?")+o+r,path:s,query:i,hash:ni(r)}}function lb(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function ec(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function cb(e,t,n){const s=t.matched.length-1,i=n.matched.length-1;return s>-1&&s===i&&us(t.matched[s],n.matched[i])&&Oh(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function us(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Oh(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!ub(e[n],t[n]))return!1;return!0}function ub(e,t){return we(e)?nc(e,t):we(t)?nc(t,e):e===t}function nc(e,t){return we(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function fb(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),i=s[s.length-1];(i===".."||i===".")&&s.push("");let o=n.length-1,r,a;for(r=0;r<s.length;r++)if(a=s[r],a!==".")if(a==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(r).join("/")}const tn={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var si;(function(e){e.pop="pop",e.push="push"})(si||(si={}));var Us;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Us||(Us={}));function hb(e){if(!e)if(Qn){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),ab(e)}const db=/^[^#]+#/;function pb(e,t){return e.replace(db,"#")+t}function gb(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const zo=()=>({left:window.scrollX,top:window.scrollY});function mb(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),i=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i)return;t=gb(i,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function sc(e,t){return(history.state?history.state.position-t:-1)+e}const Xr=new Map;function bb(e,t){Xr.set(e,t)}function _b(e){const t=Xr.get(e);return Xr.delete(e),t}let yb=()=>location.protocol+"//"+location.host;function Rh(e,t){const{pathname:n,search:s,hash:i}=t,o=e.indexOf("#");if(o>-1){let a=i.includes(e.slice(o))?e.slice(o).length:1,l=i.slice(a);return l[0]!=="/"&&(l="/"+l),ec(l,"")}return ec(n,e)+s+i}function xb(e,t,n,s){let i=[],o=[],r=null;const a=({state:h})=>{const d=Rh(e,location),p=n.value,g=t.value;let m=0;if(h){if(n.value=d,t.value=h,r&&r===p){r=null;return}m=g?h.position-g.position:0}else s(d);i.forEach(_=>{_(n.value,p,{delta:m,type:si.pop,direction:m?m>0?Us.forward:Us.back:Us.unknown})})};function l(){r=n.value}function c(h){i.push(h);const d=()=>{const p=i.indexOf(h);p>-1&&i.splice(p,1)};return o.push(d),d}function u(){const{history:h}=window;h.state&&h.replaceState(ct({},h.state,{scroll:zo()}),"")}function f(){for(const h of o)h();o=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:l,listen:c,destroy:f}}function ic(e,t,n,s=!1,i=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:i?zo():null}}function vb(e){const{history:t,location:n}=window,s={value:Rh(e,n)},i={value:t.state};i.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(l,c,u){const f=e.indexOf("#"),h=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+l:yb()+e+l;try{t[u?"replaceState":"pushState"](c,"",h),i.value=c}catch(d){console.error(d),n[u?"replace":"assign"](h)}}function r(l,c){const u=ct({},t.state,ic(i.value.back,l,i.value.forward,!0),c,{position:i.value.position});o(l,u,!0),s.value=l}function a(l,c){const u=ct({},i.value,t.state,{forward:l,scroll:zo()});o(u.current,u,!0);const f=ct({},ic(s.value,l,null),{position:u.position+1},c);o(l,f,!1),s.value=l}return{location:s,state:i,push:a,replace:r}}function GS(e){e=hb(e);const t=vb(e),n=xb(e,t.state,t.location,t.replace);function s(o,r=!0){r||n.pauseListeners(),history.go(o)}const i=ct({location:"",base:e,go:s,createHref:pb.bind(null,e)},t,n);return Object.defineProperty(i,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(i,"state",{enumerable:!0,get:()=>t.state.value}),i}function wb(e){return typeof e=="string"||e&&typeof e=="object"}function Th(e){return typeof e=="string"||typeof e=="symbol"}const Dh=Symbol("");var oc;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(oc||(oc={}));function fs(e,t){return ct(new Error,{type:e,[Dh]:!0},t)}function Ne(e,t){return e instanceof Error&&Dh in e&&(t==null||!!(e.type&t))}const rc="[^/]+?",Sb={sensitive:!1,strict:!1,start:!0,end:!0},Mb=/[.+*?^${}()[\]/\\]/g;function Cb(e,t){const n=ct({},Sb,t),s=[];let i=n.start?"^":"";const o=[];for(const c of e){const u=c.length?[]:[90];n.strict&&!c.length&&(i+="/");for(let f=0;f<c.length;f++){const h=c[f];let d=40+(n.sensitive?.25:0);if(h.type===0)f||(i+="/"),i+=h.value.replace(Mb,"\\$&"),d+=40;else if(h.type===1){const{value:p,repeatable:g,optional:m,regexp:_}=h;o.push({name:p,repeatable:g,optional:m});const b=_||rc;if(b!==rc){d+=10;try{new RegExp(`(${b})`)}catch(v){throw new Error(`Invalid custom RegExp for param "${p}" (${b}): `+v.message)}}let w=g?`((?:${b})(?:/(?:${b}))*)`:`(${b})`;f||(w=m&&c.length<2?`(?:/${w})`:"/"+w),m&&(w+="?"),i+=w,d+=20,m&&(d+=-8),g&&(d+=-20),b===".*"&&(d+=-50)}u.push(d)}s.push(u)}if(n.strict&&n.end){const c=s.length-1;s[c][s[c].length-1]+=.7000000000000001}n.strict||(i+="/?"),n.end?i+="$":n.strict&&!i.endsWith("/")&&(i+="(?:/|$)");const r=new RegExp(i,n.sensitive?"":"i");function a(c){const u=c.match(r),f={};if(!u)return null;for(let h=1;h<u.length;h++){const d=u[h]||"",p=o[h-1];f[p.name]=d&&p.repeatable?d.split("/"):d}return f}function l(c){let u="",f=!1;for(const h of e){(!f||!u.endsWith("/"))&&(u+="/"),f=!1;for(const d of h)if(d.type===0)u+=d.value;else if(d.type===1){const{value:p,repeatable:g,optional:m}=d,_=p in c?c[p]:"";if(we(_)&&!g)throw new Error(`Provided param "${p}" is an array but it is not repeatable (* or + modifiers)`);const b=we(_)?_.join("/"):_;if(!b)if(m)h.length<2&&(u.endsWith("/")?u=u.slice(0,-1):f=!0);else throw new Error(`Missing required param "${p}"`);u+=b}}return u||"/"}return{re:r,score:s,keys:o,parse:a,stringify:l}}function Eb(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Lh(e,t){let n=0;const s=e.score,i=t.score;for(;n<s.length&&n<i.length;){const o=Eb(s[n],i[n]);if(o)return o;n++}if(Math.abs(i.length-s.length)===1){if(ac(s))return 1;if(ac(i))return-1}return i.length-s.length}function ac(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Pb={type:0,value:""},kb=/[a-zA-Z0-9_]/;function Ab(e){if(!e)return[[]];if(e==="/")return[[Pb]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(d){throw new Error(`ERR (${n})/"${c}": ${d}`)}let n=0,s=n;const i=[];let o;function r(){o&&i.push(o),o=[]}let a=0,l,c="",u="";function f(){c&&(n===0?o.push({type:0,value:c}):n===1||n===2||n===3?(o.length>1&&(l==="*"||l==="+")&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:c,regexp:u,repeatable:l==="*"||l==="+",optional:l==="*"||l==="?"})):t("Invalid state to consume buffer"),c="")}function h(){c+=l}for(;a<e.length;){if(l=e[a++],l==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:l==="/"?(c&&f(),r()):l===":"?(f(),n=1):h();break;case 4:h(),n=s;break;case 1:l==="("?n=2:kb.test(l)?h():(f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--);break;case 2:l===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+l:n=3:u+=l;break;case 3:f(),n=0,l!=="*"&&l!=="?"&&l!=="+"&&a--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${c}"`),f(),r(),i}function Ob(e,t,n){const s=Cb(Ab(e.path),n),i=ct(s,{record:e,parent:t,children:[],alias:[]});return t&&!i.record.aliasOf==!t.record.aliasOf&&t.children.push(i),i}function Rb(e,t){const n=[],s=new Map;t=fc({strict:!1,end:!0,sensitive:!1},t);function i(f){return s.get(f)}function o(f,h,d){const p=!d,g=cc(f);g.aliasOf=d&&d.record;const m=fc(t,f),_=[g];if("alias"in f){const v=typeof f.alias=="string"?[f.alias]:f.alias;for(const S of v)_.push(cc(ct({},g,{components:d?d.record.components:g.components,path:S,aliasOf:d?d.record:g})))}let b,w;for(const v of _){const{path:S}=v;if(h&&S[0]!=="/"){const A=h.record.path,k=A[A.length-1]==="/"?"":"/";v.path=h.record.path+(S&&k+S)}if(b=Ob(v,h,m),d?d.alias.push(b):(w=w||b,w!==b&&w.alias.push(b),p&&f.name&&!uc(b)&&r(f.name)),Fh(b)&&l(b),g.children){const A=g.children;for(let k=0;k<A.length;k++)o(A[k],b,d&&d.children[k])}d=d||b}return w?()=>{r(w)}:$s}function r(f){if(Th(f)){const h=s.get(f);h&&(s.delete(f),n.splice(n.indexOf(h),1),h.children.forEach(r),h.alias.forEach(r))}else{const h=n.indexOf(f);h>-1&&(n.splice(h,1),f.record.name&&s.delete(f.record.name),f.children.forEach(r),f.alias.forEach(r))}}function a(){return n}function l(f){const h=Lb(f,n);n.splice(h,0,f),f.record.name&&!uc(f)&&s.set(f.record.name,f)}function c(f,h){let d,p={},g,m;if("name"in f&&f.name){if(d=s.get(f.name),!d)throw fs(1,{location:f});m=d.record.name,p=ct(lc(h.params,d.keys.filter(w=>!w.optional).concat(d.parent?d.parent.keys.filter(w=>w.optional):[]).map(w=>w.name)),f.params&&lc(f.params,d.keys.map(w=>w.name))),g=d.stringify(p)}else if(f.path!=null)g=f.path,d=n.find(w=>w.re.test(g)),d&&(p=d.parse(g),m=d.record.name);else{if(d=h.name?s.get(h.name):n.find(w=>w.re.test(h.path)),!d)throw fs(1,{location:f,currentLocation:h});m=d.record.name,p=ct({},h.params,f.params),g=d.stringify(p)}const _=[];let b=d;for(;b;)_.unshift(b.record),b=b.parent;return{name:m,path:g,params:p,matched:_,meta:Db(_)}}e.forEach(f=>o(f));function u(){n.length=0,s.clear()}return{addRoute:o,resolve:c,removeRoute:r,clearRoutes:u,getRoutes:a,getRecordMatcher:i}}function lc(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function cc(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Tb(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Tb(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function uc(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Db(e){return e.reduce((t,n)=>ct(t,n.meta),{})}function fc(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function Lb(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;Lh(e,t[o])<0?s=o:n=o+1}const i=Fb(e);return i&&(s=t.lastIndexOf(i,s-1)),s}function Fb(e){let t=e;for(;t=t.parent;)if(Fh(t)&&Lh(e,t)===0)return t}function Fh({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Ib(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let i=0;i<s.length;++i){const o=s[i].replace(Eh," "),r=o.indexOf("="),a=ni(r<0?o:o.slice(0,r)),l=r<0?null:ni(o.slice(r+1));if(a in t){let c=t[a];we(c)||(c=t[a]=[c]),c.push(l)}else t[a]=l}return t}function hc(e){let t="";for(let n in e){const s=e[n];if(n=sb(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(we(s)?s.map(o=>o&&Yr(o)):[s&&Yr(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function Nb(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=we(s)?s.map(i=>i==null?null:""+i):s==null?s:""+s)}return t}const Bb=Symbol(""),dc=Symbol(""),Ho=Symbol(""),$a=Symbol(""),Gr=Symbol("");function ws(){let e=[];function t(s){return e.push(s),()=>{const i=e.indexOf(s);i>-1&&e.splice(i,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function rn(e,t,n,s,i,o=r=>r()){const r=s&&(s.enterCallbacks[i]=s.enterCallbacks[i]||[]);return()=>new Promise((a,l)=>{const c=h=>{h===!1?l(fs(4,{from:n,to:t})):h instanceof Error?l(h):wb(h)?l(fs(2,{from:t,to:h})):(r&&s.enterCallbacks[i]===r&&typeof h=="function"&&r.push(h),a())},u=o(()=>e.call(s&&s.instances[i],t,n,c));let f=Promise.resolve(u);e.length<3&&(f=f.then(c)),f.catch(h=>l(h))})}function pr(e,t,n,s,i=o=>o()){const o=[];for(const r of e)for(const a in r.components){let l=r.components[a];if(!(t!=="beforeRouteEnter"&&!r.instances[a]))if(Mh(l)){const u=(l.__vccOpts||l)[t];u&&o.push(rn(u,n,s,r,a,i))}else{let c=l();o.push(()=>c.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${a}" at "${r.path}"`);const f=Km(u)?u.default:u;r.mods[a]=u,r.components[a]=f;const d=(f.__vccOpts||f)[t];return d&&rn(d,n,s,r,a,i)()}))}}return o}function pc(e){const t=de(Ho),n=de($a),s=fe(()=>{const l=ns(e.to);return t.resolve(l)}),i=fe(()=>{const{matched:l}=s.value,{length:c}=l,u=l[c-1],f=n.matched;if(!u||!f.length)return-1;const h=f.findIndex(us.bind(null,u));if(h>-1)return h;const d=gc(l[c-2]);return c>1&&gc(u)===d&&f[f.length-1].path!==d?f.findIndex(us.bind(null,l[c-2])):h}),o=fe(()=>i.value>-1&&Wb(n.params,s.value.params)),r=fe(()=>i.value>-1&&i.value===n.matched.length-1&&Oh(n.params,s.value.params));function a(l={}){if(jb(l)){const c=t[ns(e.replace)?"replace":"push"](ns(e.to)).catch($s);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>c),c}return Promise.resolve()}return{route:s,href:fe(()=>s.value.href),isActive:o,isExactActive:r,navigate:a}}function zb(e){return e.length===1?e[0]:e}const Hb=Nf({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:pc,setup(e,{slots:t}){const n=di(pc(e)),{options:s}=de(Ho),i=fe(()=>({[mc(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[mc(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&zb(t.default(n));return e.custom?o:dh("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},o)}}}),Vb=Hb;function jb(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Wb(e,t){for(const n in t){const s=t[n],i=e[n];if(typeof s=="string"){if(s!==i)return!1}else if(!we(i)||i.length!==s.length||s.some((o,r)=>o!==i[r]))return!1}return!0}function gc(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const mc=(e,t,n)=>e??t??n,$b=Nf({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=de(Gr),i=fe(()=>e.route||s.value),o=de(dc,0),r=fe(()=>{let c=ns(o);const{matched:u}=i.value;let f;for(;(f=u[c])&&!f.components;)c++;return c}),a=fe(()=>i.value.matched[r.value]);ji(dc,fe(()=>r.value+1)),ji(Bb,a),ji(Gr,i);const l=La();return is(()=>[l.value,a.value,e.name],([c,u,f],[h,d,p])=>{u&&(u.instances[f]=c,d&&d!==u&&c&&c===h&&(u.leaveGuards.size||(u.leaveGuards=d.leaveGuards),u.updateGuards.size||(u.updateGuards=d.updateGuards))),c&&u&&(!d||!us(u,d)||!h)&&(u.enterCallbacks[f]||[]).forEach(g=>g(c))},{flush:"post"}),()=>{const c=i.value,u=e.name,f=a.value,h=f&&f.components[u];if(!h)return bc(n.default,{Component:h,route:c});const d=f.props[u],p=d?d===!0?c.params:typeof d=="function"?d(c):d:null,m=dh(h,ct({},p,t,{onVnodeUnmounted:_=>{_.component.isUnmounted&&(f.instances[u]=null)},ref:l}));return bc(n.default,{Component:m,route:c})||m}}});function bc(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Ub=$b;function JS(e){const t=Rb(e.routes,e),n=e.parseQuery||Ib,s=e.stringifyQuery||hc,i=e.history,o=ws(),r=ws(),a=ws(),l=Vp(tn);let c=tn;Qn&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=hr.bind(null,R=>""+R),f=hr.bind(null,ob),h=hr.bind(null,ni);function d(R,$){let j,K;return Th(R)?(j=t.getRecordMatcher(R),K=$):K=R,t.addRoute(K,j)}function p(R){const $=t.getRecordMatcher(R);$&&t.removeRoute($)}function g(){return t.getRoutes().map(R=>R.record)}function m(R){return!!t.getRecordMatcher(R)}function _(R,$){if($=ct({},$||l.value),typeof R=="string"){const M=dr(n,R,$.path),O=t.resolve({path:M.path},$),D=i.createHref(M.fullPath);return ct(M,O,{params:h(O.params),hash:ni(M.hash),redirectedFrom:void 0,href:D})}let j;if(R.path!=null)j=ct({},R,{path:dr(n,R.path,$.path).path});else{const M=ct({},R.params);for(const O in M)M[O]==null&&delete M[O];j=ct({},R,{params:f(M)}),$.params=f($.params)}const K=t.resolve(j,$),dt=R.hash||"";K.params=u(h(K.params));const y=lb(s,ct({},R,{hash:nb(dt),path:K.path})),x=i.createHref(y);return ct({fullPath:y,hash:dt,query:s===hc?Nb(R.query):R.query||{}},K,{redirectedFrom:void 0,href:x})}function b(R){return typeof R=="string"?dr(n,R,l.value.path):ct({},R)}function w(R,$){if(c!==R)return fs(8,{from:$,to:R})}function v(R){return k(R)}function S(R){return v(ct(b(R),{replace:!0}))}function A(R){const $=R.matched[R.matched.length-1];if($&&$.redirect){const{redirect:j}=$;let K=typeof j=="function"?j(R):j;return typeof K=="string"&&(K=K.includes("?")||K.includes("#")?K=b(K):{path:K},K.params={}),ct({query:R.query,hash:R.hash,params:K.path!=null?{}:R.params},K)}}function k(R,$){const j=c=_(R),K=l.value,dt=R.state,y=R.force,x=R.replace===!0,M=A(j);if(M)return k(ct(b(M),{state:typeof M=="object"?ct({},dt,M.state):dt,force:y,replace:x}),$||j);const O=j;O.redirectedFrom=$;let D;return!y&&cb(s,K,j)&&(D=fs(16,{to:O,from:K}),Kt(K,K,!0,!1)),(D?Promise.resolve(D):F(O,K)).catch(T=>Ne(T)?Ne(T,2)?T:Ut(T):et(T,O,K)).then(T=>{if(T){if(Ne(T,2))return k(ct({replace:x},b(T.to),{state:typeof T.to=="object"?ct({},dt,T.to.state):dt,force:y}),$||O)}else T=L(O,K,!0,x,dt);return B(O,K,T),T})}function E(R,$){const j=w(R,$);return j?Promise.reject(j):Promise.resolve()}function C(R){const $=Xt.values().next().value;return $&&typeof $.runWithContext=="function"?$.runWithContext(R):R()}function F(R,$){let j;const[K,dt,y]=Kb(R,$);j=pr(K.reverse(),"beforeRouteLeave",R,$);for(const M of K)M.leaveGuards.forEach(O=>{j.push(rn(O,R,$))});const x=E.bind(null,R,$);return j.push(x),At(j).then(()=>{j=[];for(const M of o.list())j.push(rn(M,R,$));return j.push(x),At(j)}).then(()=>{j=pr(dt,"beforeRouteUpdate",R,$);for(const M of dt)M.updateGuards.forEach(O=>{j.push(rn(O,R,$))});return j.push(x),At(j)}).then(()=>{j=[];for(const M of y)if(M.beforeEnter)if(we(M.beforeEnter))for(const O of M.beforeEnter)j.push(rn(O,R,$));else j.push(rn(M.beforeEnter,R,$));return j.push(x),At(j)}).then(()=>(R.matched.forEach(M=>M.enterCallbacks={}),j=pr(y,"beforeRouteEnter",R,$,C),j.push(x),At(j))).then(()=>{j=[];for(const M of r.list())j.push(rn(M,R,$));return j.push(x),At(j)}).catch(M=>Ne(M,8)?M:Promise.reject(M))}function B(R,$,j){a.list().forEach(K=>C(()=>K(R,$,j)))}function L(R,$,j,K,dt){const y=w(R,$);if(y)return y;const x=$===tn,M=Qn?history.state:{};j&&(K||x?i.replace(R.fullPath,ct({scroll:x&&M&&M.scroll},dt)):i.push(R.fullPath,dt)),l.value=R,Kt(R,$,j,x),Ut()}let X;function rt(){X||(X=i.listen((R,$,j)=>{if(!Me.listening)return;const K=_(R),dt=A(K);if(dt){k(ct(dt,{replace:!0,force:!0}),K).catch($s);return}c=K;const y=l.value;Qn&&bb(sc(y.fullPath,j.delta),zo()),F(K,y).catch(x=>Ne(x,12)?x:Ne(x,2)?(k(ct(b(x.to),{force:!0}),K).then(M=>{Ne(M,20)&&!j.delta&&j.type===si.pop&&i.go(-1,!1)}).catch($s),Promise.reject()):(j.delta&&i.go(-j.delta,!1),et(x,K,y))).then(x=>{x=x||L(K,y,!1),x&&(j.delta&&!Ne(x,8)?i.go(-j.delta,!1):j.type===si.pop&&Ne(x,20)&&i.go(-1,!1)),B(K,y,x)}).catch($s)}))}let Z=ws(),G=ws(),U;function et(R,$,j){Ut(R);const K=G.list();return K.length?K.forEach(dt=>dt(R,$,j)):console.error(R),Promise.reject(R)}function mt(){return U&&l.value!==tn?Promise.resolve():new Promise((R,$)=>{Z.add([R,$])})}function Ut(R){return U||(U=!R,rt(),Z.list().forEach(([$,j])=>R?j(R):$()),Z.reset()),R}function Kt(R,$,j,K){const{scrollBehavior:dt}=e;if(!Qn||!dt)return Promise.resolve();const y=!j&&_b(sc(R.fullPath,0))||(K||!j)&&history.state&&history.state.scroll||null;return Do().then(()=>dt(R,$,y)).then(x=>x&&mb(x)).catch(x=>et(x,R,$))}const Et=R=>i.go(R);let ae;const Xt=new Set,Me={currentRoute:l,listening:!0,addRoute:d,removeRoute:p,clearRoutes:t.clearRoutes,hasRoute:m,getRoutes:g,resolve:_,options:e,push:v,replace:S,go:Et,back:()=>Et(-1),forward:()=>Et(1),beforeEach:o.add,beforeResolve:r.add,afterEach:a.add,onError:G.add,isReady:mt,install(R){const $=this;R.component("RouterLink",Vb),R.component("RouterView",Ub),R.config.globalProperties.$router=$,Object.defineProperty(R.config.globalProperties,"$route",{enumerable:!0,get:()=>ns(l)}),Qn&&!ae&&l.value===tn&&(ae=!0,v(i.location).catch(dt=>{}));const j={};for(const dt in tn)Object.defineProperty(j,dt,{get:()=>l.value[dt],enumerable:!0});R.provide(Ho,$),R.provide($a,Mf(j)),R.provide(Gr,l);const K=R.unmount;Xt.add(R),R.unmount=function(){Xt.delete(R),Xt.size<1&&(c=tn,X&&X(),X=null,l.value=tn,ae=!1,U=!1),K()}}};function At(R){return R.reduce(($,j)=>$.then(()=>C(j)),Promise.resolve())}return Me}function Kb(e,t){const n=[],s=[],i=[],o=Math.max(t.matched.length,e.matched.length);for(let r=0;r<o;r++){const a=t.matched[r];a&&(e.matched.find(c=>us(c,a))?s.push(a):n.push(a));const l=e.matched[r];l&&(t.matched.find(c=>us(c,l))||i.push(l))}return[n,s,i]}function QS(){return de(Ho)}function ZS(e){return de($a)}function Ih(e,t){return function(){return e.apply(t,arguments)}}const{toString:qb}=Object.prototype,{getPrototypeOf:Ua}=Object,{iterator:Vo,toStringTag:Nh}=Symbol,jo=(e=>t=>{const n=qb.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),Se=e=>(e=e.toLowerCase(),t=>jo(t)===e),Wo=e=>t=>typeof t===e,{isArray:bs}=Array,ii=Wo("undefined");function Yb(e){return e!==null&&!ii(e)&&e.constructor!==null&&!ii(e.constructor)&&te(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Bh=Se("ArrayBuffer");function Xb(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Bh(e.buffer),t}const Gb=Wo("string"),te=Wo("function"),zh=Wo("number"),$o=e=>e!==null&&typeof e=="object",Jb=e=>e===!0||e===!1,Ki=e=>{if(jo(e)!=="object")return!1;const t=Ua(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Nh in e)&&!(Vo in e)},Qb=Se("Date"),Zb=Se("File"),t_=Se("Blob"),e_=Se("FileList"),n_=e=>$o(e)&&te(e.pipe),s_=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||te(e.append)&&((t=jo(e))==="formdata"||t==="object"&&te(e.toString)&&e.toString()==="[object FormData]"))},i_=Se("URLSearchParams"),[o_,r_,a_,l_]=["ReadableStream","Request","Response","Headers"].map(Se),c_=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function mi(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,i;if(typeof e!="object"&&(e=[e]),bs(e))for(s=0,i=e.length;s<i;s++)t.call(null,e[s],s,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),r=o.length;let a;for(s=0;s<r;s++)a=o[s],t.call(null,e[a],a,e)}}function Hh(e,t){t=t.toLowerCase();const n=Object.keys(e);let s=n.length,i;for(;s-- >0;)if(i=n[s],t===i.toLowerCase())return i;return null}const Dn=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Vh=e=>!ii(e)&&e!==Dn;function Jr(){const{caseless:e}=Vh(this)&&this||{},t={},n=(s,i)=>{const o=e&&Hh(t,i)||i;Ki(t[o])&&Ki(s)?t[o]=Jr(t[o],s):Ki(s)?t[o]=Jr({},s):bs(s)?t[o]=s.slice():t[o]=s};for(let s=0,i=arguments.length;s<i;s++)arguments[s]&&mi(arguments[s],n);return t}const u_=(e,t,n,{allOwnKeys:s}={})=>(mi(t,(i,o)=>{n&&te(i)?e[o]=Ih(i,n):e[o]=i},{allOwnKeys:s}),e),f_=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),h_=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},d_=(e,t,n,s)=>{let i,o,r;const a={};if(t=t||{},e==null)return t;do{for(i=Object.getOwnPropertyNames(e),o=i.length;o-- >0;)r=i[o],(!s||s(r,e,t))&&!a[r]&&(t[r]=e[r],a[r]=!0);e=n!==!1&&Ua(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},p_=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},g_=e=>{if(!e)return null;if(bs(e))return e;let t=e.length;if(!zh(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},m_=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Ua(Uint8Array)),b_=(e,t)=>{const s=(e&&e[Vo]).call(e);let i;for(;(i=s.next())&&!i.done;){const o=i.value;t.call(e,o[0],o[1])}},__=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},y_=Se("HTMLFormElement"),x_=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,i){return s.toUpperCase()+i}),_c=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),v_=Se("RegExp"),jh=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};mi(n,(i,o)=>{let r;(r=t(i,o,e))!==!1&&(s[o]=r||i)}),Object.defineProperties(e,s)},w_=e=>{jh(e,(t,n)=>{if(te(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(te(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},S_=(e,t)=>{const n={},s=i=>{i.forEach(o=>{n[o]=!0})};return bs(e)?s(e):s(String(e).split(t)),n},M_=()=>{},C_=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function E_(e){return!!(e&&te(e.append)&&e[Nh]==="FormData"&&e[Vo])}const P_=e=>{const t=new Array(10),n=(s,i)=>{if($o(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[i]=s;const o=bs(s)?[]:{};return mi(s,(r,a)=>{const l=n(r,i+1);!ii(l)&&(o[a]=l)}),t[i]=void 0,o}}return s};return n(e,0)},k_=Se("AsyncFunction"),A_=e=>e&&($o(e)||te(e))&&te(e.then)&&te(e.catch),Wh=((e,t)=>e?setImmediate:t?((n,s)=>(Dn.addEventListener("message",({source:i,data:o})=>{i===Dn&&o===n&&s.length&&s.shift()()},!1),i=>{s.push(i),Dn.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",te(Dn.postMessage)),O_=typeof queueMicrotask<"u"?queueMicrotask.bind(Dn):typeof process<"u"&&process.nextTick||Wh,R_=e=>e!=null&&te(e[Vo]),P={isArray:bs,isArrayBuffer:Bh,isBuffer:Yb,isFormData:s_,isArrayBufferView:Xb,isString:Gb,isNumber:zh,isBoolean:Jb,isObject:$o,isPlainObject:Ki,isReadableStream:o_,isRequest:r_,isResponse:a_,isHeaders:l_,isUndefined:ii,isDate:Qb,isFile:Zb,isBlob:t_,isRegExp:v_,isFunction:te,isStream:n_,isURLSearchParams:i_,isTypedArray:m_,isFileList:e_,forEach:mi,merge:Jr,extend:u_,trim:c_,stripBOM:f_,inherits:h_,toFlatObject:d_,kindOf:jo,kindOfTest:Se,endsWith:p_,toArray:g_,forEachEntry:b_,matchAll:__,isHTMLForm:y_,hasOwnProperty:_c,hasOwnProp:_c,reduceDescriptors:jh,freezeMethods:w_,toObjectSet:S_,toCamelCase:x_,noop:M_,toFiniteNumber:C_,findKey:Hh,global:Dn,isContextDefined:Vh,isSpecCompliantForm:E_,toJSONObject:P_,isAsyncFn:k_,isThenable:A_,setImmediate:Wh,asap:O_,isIterable:R_};function nt(e,t,n,s,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),i&&(this.response=i,this.status=i.status?i.status:null)}P.inherits(nt,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:P.toJSONObject(this.config),code:this.code,status:this.status}}});const $h=nt.prototype,Uh={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Uh[e]={value:e}});Object.defineProperties(nt,Uh);Object.defineProperty($h,"isAxiosError",{value:!0});nt.from=(e,t,n,s,i,o)=>{const r=Object.create($h);return P.toFlatObject(e,r,function(l){return l!==Error.prototype},a=>a!=="isAxiosError"),nt.call(r,e.message,t,n,s,i),r.cause=e,r.name=e.name,o&&Object.assign(r,o),r};const T_=null;function Qr(e){return P.isPlainObject(e)||P.isArray(e)}function Kh(e){return P.endsWith(e,"[]")?e.slice(0,-2):e}function yc(e,t,n){return e?e.concat(t).map(function(i,o){return i=Kh(i),!n&&o?"["+i+"]":i}).join(n?".":""):t}function D_(e){return P.isArray(e)&&!e.some(Qr)}const L_=P.toFlatObject(P,{},null,function(t){return/^is[A-Z]/.test(t)});function Uo(e,t,n){if(!P.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=P.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(g,m){return!P.isUndefined(m[g])});const s=n.metaTokens,i=n.visitor||u,o=n.dots,r=n.indexes,l=(n.Blob||typeof Blob<"u"&&Blob)&&P.isSpecCompliantForm(t);if(!P.isFunction(i))throw new TypeError("visitor must be a function");function c(p){if(p===null)return"";if(P.isDate(p))return p.toISOString();if(!l&&P.isBlob(p))throw new nt("Blob is not supported. Use a Buffer instead.");return P.isArrayBuffer(p)||P.isTypedArray(p)?l&&typeof Blob=="function"?new Blob([p]):Buffer.from(p):p}function u(p,g,m){let _=p;if(p&&!m&&typeof p=="object"){if(P.endsWith(g,"{}"))g=s?g:g.slice(0,-2),p=JSON.stringify(p);else if(P.isArray(p)&&D_(p)||(P.isFileList(p)||P.endsWith(g,"[]"))&&(_=P.toArray(p)))return g=Kh(g),_.forEach(function(w,v){!(P.isUndefined(w)||w===null)&&t.append(r===!0?yc([g],v,o):r===null?g:g+"[]",c(w))}),!1}return Qr(p)?!0:(t.append(yc(m,g,o),c(p)),!1)}const f=[],h=Object.assign(L_,{defaultVisitor:u,convertValue:c,isVisitable:Qr});function d(p,g){if(!P.isUndefined(p)){if(f.indexOf(p)!==-1)throw Error("Circular reference detected in "+g.join("."));f.push(p),P.forEach(p,function(_,b){(!(P.isUndefined(_)||_===null)&&i.call(t,_,P.isString(b)?b.trim():b,g,h))===!0&&d(_,g?g.concat(b):[b])}),f.pop()}}if(!P.isObject(e))throw new TypeError("data must be an object");return d(e),t}function xc(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function Ka(e,t){this._pairs=[],e&&Uo(e,this,t)}const qh=Ka.prototype;qh.append=function(t,n){this._pairs.push([t,n])};qh.toString=function(t){const n=t?function(s){return t.call(this,s,xc)}:xc;return this._pairs.map(function(i){return n(i[0])+"="+n(i[1])},"").join("&")};function F_(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Yh(e,t,n){if(!t)return e;const s=n&&n.encode||F_;P.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let o;if(i?o=i(t,n):o=P.isURLSearchParams(t)?t.toString():new Ka(t,n).toString(s),o){const r=e.indexOf("#");r!==-1&&(e=e.slice(0,r)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class vc{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){P.forEach(this.handlers,function(s){s!==null&&t(s)})}}const Xh={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},I_=typeof URLSearchParams<"u"?URLSearchParams:Ka,N_=typeof FormData<"u"?FormData:null,B_=typeof Blob<"u"?Blob:null,z_={isBrowser:!0,classes:{URLSearchParams:I_,FormData:N_,Blob:B_},protocols:["http","https","file","blob","url","data"]},qa=typeof window<"u"&&typeof document<"u",Zr=typeof navigator=="object"&&navigator||void 0,H_=qa&&(!Zr||["ReactNative","NativeScript","NS"].indexOf(Zr.product)<0),V_=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",j_=qa&&window.location.href||"http://localhost",W_=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:qa,hasStandardBrowserEnv:H_,hasStandardBrowserWebWorkerEnv:V_,navigator:Zr,origin:j_},Symbol.toStringTag,{value:"Module"})),Vt={...W_,...z_};function $_(e,t){return Uo(e,new Vt.classes.URLSearchParams,Object.assign({visitor:function(n,s,i,o){return Vt.isNode&&P.isBuffer(n)?(this.append(s,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function U_(e){return P.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function K_(e){const t={},n=Object.keys(e);let s;const i=n.length;let o;for(s=0;s<i;s++)o=n[s],t[o]=e[o];return t}function Gh(e){function t(n,s,i,o){let r=n[o++];if(r==="__proto__")return!0;const a=Number.isFinite(+r),l=o>=n.length;return r=!r&&P.isArray(i)?i.length:r,l?(P.hasOwnProp(i,r)?i[r]=[i[r],s]:i[r]=s,!a):((!i[r]||!P.isObject(i[r]))&&(i[r]=[]),t(n,s,i[r],o)&&P.isArray(i[r])&&(i[r]=K_(i[r])),!a)}if(P.isFormData(e)&&P.isFunction(e.entries)){const n={};return P.forEachEntry(e,(s,i)=>{t(U_(s),i,n,0)}),n}return null}function q_(e,t,n){if(P.isString(e))try{return(t||JSON.parse)(e),P.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const bi={transitional:Xh,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",i=s.indexOf("application/json")>-1,o=P.isObject(t);if(o&&P.isHTMLForm(t)&&(t=new FormData(t)),P.isFormData(t))return i?JSON.stringify(Gh(t)):t;if(P.isArrayBuffer(t)||P.isBuffer(t)||P.isStream(t)||P.isFile(t)||P.isBlob(t)||P.isReadableStream(t))return t;if(P.isArrayBufferView(t))return t.buffer;if(P.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(o){if(s.indexOf("application/x-www-form-urlencoded")>-1)return $_(t,this.formSerializer).toString();if((a=P.isFileList(t))||s.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return Uo(a?{"files[]":t}:t,l&&new l,this.formSerializer)}}return o||i?(n.setContentType("application/json",!1),q_(t)):t}],transformResponse:[function(t){const n=this.transitional||bi.transitional,s=n&&n.forcedJSONParsing,i=this.responseType==="json";if(P.isResponse(t)||P.isReadableStream(t))return t;if(t&&P.isString(t)&&(s&&!this.responseType||i)){const r=!(n&&n.silentJSONParsing)&&i;try{return JSON.parse(t)}catch(a){if(r)throw a.name==="SyntaxError"?nt.from(a,nt.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Vt.classes.FormData,Blob:Vt.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};P.forEach(["delete","get","head","post","put","patch"],e=>{bi.headers[e]={}});const Y_=P.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),X_=e=>{const t={};let n,s,i;return e&&e.split(`
`).forEach(function(r){i=r.indexOf(":"),n=r.substring(0,i).trim().toLowerCase(),s=r.substring(i+1).trim(),!(!n||t[n]&&Y_[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},wc=Symbol("internals");function Ss(e){return e&&String(e).trim().toLowerCase()}function qi(e){return e===!1||e==null?e:P.isArray(e)?e.map(qi):String(e)}function G_(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const J_=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function gr(e,t,n,s,i){if(P.isFunction(s))return s.call(this,t,n);if(i&&(t=n),!!P.isString(t)){if(P.isString(s))return t.indexOf(s)!==-1;if(P.isRegExp(s))return s.test(t)}}function Q_(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function Z_(e,t){const n=P.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(i,o,r){return this[s].call(this,t,i,o,r)},configurable:!0})})}let ee=class{constructor(t){t&&this.set(t)}set(t,n,s){const i=this;function o(a,l,c){const u=Ss(l);if(!u)throw new Error("header name must be a non-empty string");const f=P.findKey(i,u);(!f||i[f]===void 0||c===!0||c===void 0&&i[f]!==!1)&&(i[f||l]=qi(a))}const r=(a,l)=>P.forEach(a,(c,u)=>o(c,u,l));if(P.isPlainObject(t)||t instanceof this.constructor)r(t,n);else if(P.isString(t)&&(t=t.trim())&&!J_(t))r(X_(t),n);else if(P.isObject(t)&&P.isIterable(t)){let a={},l,c;for(const u of t){if(!P.isArray(u))throw TypeError("Object iterator must return a key-value pair");a[c=u[0]]=(l=a[c])?P.isArray(l)?[...l,u[1]]:[l,u[1]]:u[1]}r(a,n)}else t!=null&&o(n,t,s);return this}get(t,n){if(t=Ss(t),t){const s=P.findKey(this,t);if(s){const i=this[s];if(!n)return i;if(n===!0)return G_(i);if(P.isFunction(n))return n.call(this,i,s);if(P.isRegExp(n))return n.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Ss(t),t){const s=P.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||gr(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let i=!1;function o(r){if(r=Ss(r),r){const a=P.findKey(s,r);a&&(!n||gr(s,s[a],a,n))&&(delete s[a],i=!0)}}return P.isArray(t)?t.forEach(o):o(t),i}clear(t){const n=Object.keys(this);let s=n.length,i=!1;for(;s--;){const o=n[s];(!t||gr(this,this[o],o,t,!0))&&(delete this[o],i=!0)}return i}normalize(t){const n=this,s={};return P.forEach(this,(i,o)=>{const r=P.findKey(s,o);if(r){n[r]=qi(i),delete n[o];return}const a=t?Q_(o):String(o).trim();a!==o&&delete n[o],n[a]=qi(i),s[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return P.forEach(this,(s,i)=>{s!=null&&s!==!1&&(n[i]=t&&P.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(i=>s.set(i)),s}static accessor(t){const s=(this[wc]=this[wc]={accessors:{}}).accessors,i=this.prototype;function o(r){const a=Ss(r);s[a]||(Z_(i,r),s[a]=!0)}return P.isArray(t)?t.forEach(o):o(t),this}};ee.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);P.reduceDescriptors(ee.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});P.freezeMethods(ee);function mr(e,t){const n=this||bi,s=t||n,i=ee.from(s.headers);let o=s.data;return P.forEach(e,function(a){o=a.call(n,o,i.normalize(),t?t.status:void 0)}),i.normalize(),o}function Jh(e){return!!(e&&e.__CANCEL__)}function _s(e,t,n){nt.call(this,e??"canceled",nt.ERR_CANCELED,t,n),this.name="CanceledError"}P.inherits(_s,nt,{__CANCEL__:!0});function Qh(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new nt("Request failed with status code "+n.status,[nt.ERR_BAD_REQUEST,nt.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function ty(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function ey(e,t){e=e||10;const n=new Array(e),s=new Array(e);let i=0,o=0,r;return t=t!==void 0?t:1e3,function(l){const c=Date.now(),u=s[o];r||(r=c),n[i]=l,s[i]=c;let f=o,h=0;for(;f!==i;)h+=n[f++],f=f%e;if(i=(i+1)%e,i===o&&(o=(o+1)%e),c-r<t)return;const d=u&&c-u;return d?Math.round(h*1e3/d):void 0}}function ny(e,t){let n=0,s=1e3/t,i,o;const r=(c,u=Date.now())=>{n=u,i=null,o&&(clearTimeout(o),o=null),e.apply(null,c)};return[(...c)=>{const u=Date.now(),f=u-n;f>=s?r(c,u):(i=c,o||(o=setTimeout(()=>{o=null,r(i)},s-f)))},()=>i&&r(i)]}const bo=(e,t,n=3)=>{let s=0;const i=ey(50,250);return ny(o=>{const r=o.loaded,a=o.lengthComputable?o.total:void 0,l=r-s,c=i(l),u=r<=a;s=r;const f={loaded:r,total:a,progress:a?r/a:void 0,bytes:l,rate:c||void 0,estimated:c&&a&&u?(a-r)/c:void 0,event:o,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(f)},n)},Sc=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},Mc=e=>(...t)=>P.asap(()=>e(...t)),sy=Vt.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Vt.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Vt.origin),Vt.navigator&&/(msie|trident)/i.test(Vt.navigator.userAgent)):()=>!0,iy=Vt.hasStandardBrowserEnv?{write(e,t,n,s,i,o){const r=[e+"="+encodeURIComponent(t)];P.isNumber(n)&&r.push("expires="+new Date(n).toGMTString()),P.isString(s)&&r.push("path="+s),P.isString(i)&&r.push("domain="+i),o===!0&&r.push("secure"),document.cookie=r.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function oy(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function ry(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Zh(e,t,n){let s=!oy(t);return e&&(s||n==!1)?ry(e,t):t}const Cc=e=>e instanceof ee?{...e}:e;function jn(e,t){t=t||{};const n={};function s(c,u,f,h){return P.isPlainObject(c)&&P.isPlainObject(u)?P.merge.call({caseless:h},c,u):P.isPlainObject(u)?P.merge({},u):P.isArray(u)?u.slice():u}function i(c,u,f,h){if(P.isUndefined(u)){if(!P.isUndefined(c))return s(void 0,c,f,h)}else return s(c,u,f,h)}function o(c,u){if(!P.isUndefined(u))return s(void 0,u)}function r(c,u){if(P.isUndefined(u)){if(!P.isUndefined(c))return s(void 0,c)}else return s(void 0,u)}function a(c,u,f){if(f in t)return s(c,u);if(f in e)return s(void 0,c)}const l={url:o,method:o,data:o,baseURL:r,transformRequest:r,transformResponse:r,paramsSerializer:r,timeout:r,timeoutMessage:r,withCredentials:r,withXSRFToken:r,adapter:r,responseType:r,xsrfCookieName:r,xsrfHeaderName:r,onUploadProgress:r,onDownloadProgress:r,decompress:r,maxContentLength:r,maxBodyLength:r,beforeRedirect:r,transport:r,httpAgent:r,httpsAgent:r,cancelToken:r,socketPath:r,responseEncoding:r,validateStatus:a,headers:(c,u,f)=>i(Cc(c),Cc(u),f,!0)};return P.forEach(Object.keys(Object.assign({},e,t)),function(u){const f=l[u]||i,h=f(e[u],t[u],u);P.isUndefined(h)&&f!==a||(n[u]=h)}),n}const td=e=>{const t=jn({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:i,xsrfCookieName:o,headers:r,auth:a}=t;t.headers=r=ee.from(r),t.url=Yh(Zh(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&r.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let l;if(P.isFormData(n)){if(Vt.hasStandardBrowserEnv||Vt.hasStandardBrowserWebWorkerEnv)r.setContentType(void 0);else if((l=r.getContentType())!==!1){const[c,...u]=l?l.split(";").map(f=>f.trim()).filter(Boolean):[];r.setContentType([c||"multipart/form-data",...u].join("; "))}}if(Vt.hasStandardBrowserEnv&&(s&&P.isFunction(s)&&(s=s(t)),s||s!==!1&&sy(t.url))){const c=i&&o&&iy.read(o);c&&r.set(i,c)}return t},ay=typeof XMLHttpRequest<"u",ly=ay&&function(e){return new Promise(function(n,s){const i=td(e);let o=i.data;const r=ee.from(i.headers).normalize();let{responseType:a,onUploadProgress:l,onDownloadProgress:c}=i,u,f,h,d,p;function g(){d&&d(),p&&p(),i.cancelToken&&i.cancelToken.unsubscribe(u),i.signal&&i.signal.removeEventListener("abort",u)}let m=new XMLHttpRequest;m.open(i.method.toUpperCase(),i.url,!0),m.timeout=i.timeout;function _(){if(!m)return;const w=ee.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders()),S={data:!a||a==="text"||a==="json"?m.responseText:m.response,status:m.status,statusText:m.statusText,headers:w,config:e,request:m};Qh(function(k){n(k),g()},function(k){s(k),g()},S),m=null}"onloadend"in m?m.onloadend=_:m.onreadystatechange=function(){!m||m.readyState!==4||m.status===0&&!(m.responseURL&&m.responseURL.indexOf("file:")===0)||setTimeout(_)},m.onabort=function(){m&&(s(new nt("Request aborted",nt.ECONNABORTED,e,m)),m=null)},m.onerror=function(){s(new nt("Network Error",nt.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let v=i.timeout?"timeout of "+i.timeout+"ms exceeded":"timeout exceeded";const S=i.transitional||Xh;i.timeoutErrorMessage&&(v=i.timeoutErrorMessage),s(new nt(v,S.clarifyTimeoutError?nt.ETIMEDOUT:nt.ECONNABORTED,e,m)),m=null},o===void 0&&r.setContentType(null),"setRequestHeader"in m&&P.forEach(r.toJSON(),function(v,S){m.setRequestHeader(S,v)}),P.isUndefined(i.withCredentials)||(m.withCredentials=!!i.withCredentials),a&&a!=="json"&&(m.responseType=i.responseType),c&&([h,p]=bo(c,!0),m.addEventListener("progress",h)),l&&m.upload&&([f,d]=bo(l),m.upload.addEventListener("progress",f),m.upload.addEventListener("loadend",d)),(i.cancelToken||i.signal)&&(u=w=>{m&&(s(!w||w.type?new _s(null,e,m):w),m.abort(),m=null)},i.cancelToken&&i.cancelToken.subscribe(u),i.signal&&(i.signal.aborted?u():i.signal.addEventListener("abort",u)));const b=ty(i.url);if(b&&Vt.protocols.indexOf(b)===-1){s(new nt("Unsupported protocol "+b+":",nt.ERR_BAD_REQUEST,e));return}m.send(o||null)})},cy=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,i;const o=function(c){if(!i){i=!0,a();const u=c instanceof Error?c:this.reason;s.abort(u instanceof nt?u:new _s(u instanceof Error?u.message:u))}};let r=t&&setTimeout(()=>{r=null,o(new nt(`timeout ${t} of ms exceeded`,nt.ETIMEDOUT))},t);const a=()=>{e&&(r&&clearTimeout(r),r=null,e.forEach(c=>{c.unsubscribe?c.unsubscribe(o):c.removeEventListener("abort",o)}),e=null)};e.forEach(c=>c.addEventListener("abort",o));const{signal:l}=s;return l.unsubscribe=()=>P.asap(a),l}},uy=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let s=0,i;for(;s<n;)i=s+t,yield e.slice(s,i),s=i},fy=async function*(e,t){for await(const n of hy(e))yield*uy(n,t)},hy=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},Ec=(e,t,n,s)=>{const i=fy(e,t);let o=0,r,a=l=>{r||(r=!0,s&&s(l))};return new ReadableStream({async pull(l){try{const{done:c,value:u}=await i.next();if(c){a(),l.close();return}let f=u.byteLength;if(n){let h=o+=f;n(h)}l.enqueue(new Uint8Array(u))}catch(c){throw a(c),c}},cancel(l){return a(l),i.return()}},{highWaterMark:2})},Ko=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",ed=Ko&&typeof ReadableStream=="function",dy=Ko&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),nd=(e,...t)=>{try{return!!e(...t)}catch{return!1}},py=ed&&nd(()=>{let e=!1;const t=new Request(Vt.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Pc=64*1024,ta=ed&&nd(()=>P.isReadableStream(new Response("").body)),_o={stream:ta&&(e=>e.body)};Ko&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!_o[t]&&(_o[t]=P.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new nt(`Response type '${t}' is not supported`,nt.ERR_NOT_SUPPORT,s)})})})(new Response);const gy=async e=>{if(e==null)return 0;if(P.isBlob(e))return e.size;if(P.isSpecCompliantForm(e))return(await new Request(Vt.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(P.isArrayBufferView(e)||P.isArrayBuffer(e))return e.byteLength;if(P.isURLSearchParams(e)&&(e=e+""),P.isString(e))return(await dy(e)).byteLength},my=async(e,t)=>{const n=P.toFiniteNumber(e.getContentLength());return n??gy(t)},by=Ko&&(async e=>{let{url:t,method:n,data:s,signal:i,cancelToken:o,timeout:r,onDownloadProgress:a,onUploadProgress:l,responseType:c,headers:u,withCredentials:f="same-origin",fetchOptions:h}=td(e);c=c?(c+"").toLowerCase():"text";let d=cy([i,o&&o.toAbortSignal()],r),p;const g=d&&d.unsubscribe&&(()=>{d.unsubscribe()});let m;try{if(l&&py&&n!=="get"&&n!=="head"&&(m=await my(u,s))!==0){let S=new Request(t,{method:"POST",body:s,duplex:"half"}),A;if(P.isFormData(s)&&(A=S.headers.get("content-type"))&&u.setContentType(A),S.body){const[k,E]=Sc(m,bo(Mc(l)));s=Ec(S.body,Pc,k,E)}}P.isString(f)||(f=f?"include":"omit");const _="credentials"in Request.prototype;p=new Request(t,{...h,signal:d,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:s,duplex:"half",credentials:_?f:void 0});let b=await fetch(p);const w=ta&&(c==="stream"||c==="response");if(ta&&(a||w&&g)){const S={};["status","statusText","headers"].forEach(C=>{S[C]=b[C]});const A=P.toFiniteNumber(b.headers.get("content-length")),[k,E]=a&&Sc(A,bo(Mc(a),!0))||[];b=new Response(Ec(b.body,Pc,k,()=>{E&&E(),g&&g()}),S)}c=c||"text";let v=await _o[P.findKey(_o,c)||"text"](b,e);return!w&&g&&g(),await new Promise((S,A)=>{Qh(S,A,{data:v,headers:ee.from(b.headers),status:b.status,statusText:b.statusText,config:e,request:p})})}catch(_){throw g&&g(),_&&_.name==="TypeError"&&/Load failed|fetch/i.test(_.message)?Object.assign(new nt("Network Error",nt.ERR_NETWORK,e,p),{cause:_.cause||_}):nt.from(_,_&&_.code,e,p)}}),ea={http:T_,xhr:ly,fetch:by};P.forEach(ea,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const kc=e=>`- ${e}`,_y=e=>P.isFunction(e)||e===null||e===!1,sd={getAdapter:e=>{e=P.isArray(e)?e:[e];const{length:t}=e;let n,s;const i={};for(let o=0;o<t;o++){n=e[o];let r;if(s=n,!_y(n)&&(s=ea[(r=String(n)).toLowerCase()],s===void 0))throw new nt(`Unknown adapter '${r}'`);if(s)break;i[r||"#"+o]=s}if(!s){const o=Object.entries(i).map(([a,l])=>`adapter ${a} `+(l===!1?"is not supported by the environment":"is not available in the build"));let r=t?o.length>1?`since :
`+o.map(kc).join(`
`):" "+kc(o[0]):"as no adapter specified";throw new nt("There is no suitable adapter to dispatch the request "+r,"ERR_NOT_SUPPORT")}return s},adapters:ea};function br(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new _s(null,e)}function Ac(e){return br(e),e.headers=ee.from(e.headers),e.data=mr.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),sd.getAdapter(e.adapter||bi.adapter)(e).then(function(s){return br(e),s.data=mr.call(e,e.transformResponse,s),s.headers=ee.from(s.headers),s},function(s){return Jh(s)||(br(e),s&&s.response&&(s.response.data=mr.call(e,e.transformResponse,s.response),s.response.headers=ee.from(s.response.headers))),Promise.reject(s)})}const id="1.9.0",qo={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{qo[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const Oc={};qo.transitional=function(t,n,s){function i(o,r){return"[Axios v"+id+"] Transitional option '"+o+"'"+r+(s?". "+s:"")}return(o,r,a)=>{if(t===!1)throw new nt(i(r," has been removed"+(n?" in "+n:"")),nt.ERR_DEPRECATED);return n&&!Oc[r]&&(Oc[r]=!0,console.warn(i(r," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,r,a):!0}};qo.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function yy(e,t,n){if(typeof e!="object")throw new nt("options must be an object",nt.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let i=s.length;for(;i-- >0;){const o=s[i],r=t[o];if(r){const a=e[o],l=a===void 0||r(a,o,e);if(l!==!0)throw new nt("option "+o+" must be "+l,nt.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new nt("Unknown option "+o,nt.ERR_BAD_OPTION)}}const Yi={assertOptions:yy,validators:qo},ke=Yi.validators;let Bn=class{constructor(t){this.defaults=t||{},this.interceptors={request:new vc,response:new vc}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let i={};Error.captureStackTrace?Error.captureStackTrace(i):i=new Error;const o=i.stack?i.stack.replace(/^.+\n/,""):"";try{s.stack?o&&!String(s.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+o):s.stack=o}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=jn(this.defaults,n);const{transitional:s,paramsSerializer:i,headers:o}=n;s!==void 0&&Yi.assertOptions(s,{silentJSONParsing:ke.transitional(ke.boolean),forcedJSONParsing:ke.transitional(ke.boolean),clarifyTimeoutError:ke.transitional(ke.boolean)},!1),i!=null&&(P.isFunction(i)?n.paramsSerializer={serialize:i}:Yi.assertOptions(i,{encode:ke.function,serialize:ke.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Yi.assertOptions(n,{baseUrl:ke.spelling("baseURL"),withXsrfToken:ke.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let r=o&&P.merge(o.common,o[n.method]);o&&P.forEach(["delete","get","head","post","put","patch","common"],p=>{delete o[p]}),n.headers=ee.concat(r,o);const a=[];let l=!0;this.interceptors.request.forEach(function(g){typeof g.runWhen=="function"&&g.runWhen(n)===!1||(l=l&&g.synchronous,a.unshift(g.fulfilled,g.rejected))});const c=[];this.interceptors.response.forEach(function(g){c.push(g.fulfilled,g.rejected)});let u,f=0,h;if(!l){const p=[Ac.bind(this),void 0];for(p.unshift.apply(p,a),p.push.apply(p,c),h=p.length,u=Promise.resolve(n);f<h;)u=u.then(p[f++],p[f++]);return u}h=a.length;let d=n;for(f=0;f<h;){const p=a[f++],g=a[f++];try{d=p(d)}catch(m){g.call(this,m);break}}try{u=Ac.call(this,d)}catch(p){return Promise.reject(p)}for(f=0,h=c.length;f<h;)u=u.then(c[f++],c[f++]);return u}getUri(t){t=jn(this.defaults,t);const n=Zh(t.baseURL,t.url,t.allowAbsoluteUrls);return Yh(n,t.params,t.paramsSerializer)}};P.forEach(["delete","get","head","options"],function(t){Bn.prototype[t]=function(n,s){return this.request(jn(s||{},{method:t,url:n,data:(s||{}).data}))}});P.forEach(["post","put","patch"],function(t){function n(s){return function(o,r,a){return this.request(jn(a||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:o,data:r}))}}Bn.prototype[t]=n(),Bn.prototype[t+"Form"]=n(!0)});let xy=class od{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const s=this;this.promise.then(i=>{if(!s._listeners)return;let o=s._listeners.length;for(;o-- >0;)s._listeners[o](i);s._listeners=null}),this.promise.then=i=>{let o;const r=new Promise(a=>{s.subscribe(a),o=a}).then(i);return r.cancel=function(){s.unsubscribe(o)},r},t(function(o,r,a){s.reason||(s.reason=new _s(o,r,a),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new od(function(i){t=i}),cancel:t}}};function vy(e){return function(n){return e.apply(null,n)}}function wy(e){return P.isObject(e)&&e.isAxiosError===!0}const na={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(na).forEach(([e,t])=>{na[t]=e});function rd(e){const t=new Bn(e),n=Ih(Bn.prototype.request,t);return P.extend(n,Bn.prototype,t,{allOwnKeys:!0}),P.extend(n,t,null,{allOwnKeys:!0}),n.create=function(i){return rd(jn(e,i))},n}const Ot=rd(bi);Ot.Axios=Bn;Ot.CanceledError=_s;Ot.CancelToken=xy;Ot.isCancel=Jh;Ot.VERSION=id;Ot.toFormData=Uo;Ot.AxiosError=nt;Ot.Cancel=Ot.CanceledError;Ot.all=function(t){return Promise.all(t)};Ot.spread=vy;Ot.isAxiosError=wy;Ot.mergeConfig=jn;Ot.AxiosHeaders=ee;Ot.formToJSON=e=>Gh(P.isHTMLForm(e)?new FormData(e):e);Ot.getAdapter=sd.getAdapter;Ot.HttpStatusCode=na;Ot.default=Ot;const{Axios:nM,AxiosError:sM,CanceledError:iM,isCancel:oM,CancelToken:rM,VERSION:aM,all:lM,Cancel:cM,isAxiosError:uM,spread:fM,toFormData:hM,AxiosHeaders:dM,HttpStatusCode:pM,formToJSON:gM,getAdapter:mM,mergeConfig:bM}=Ot;/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function _i(e){return e+.5|0}const an=(e,t,n)=>Math.max(Math.min(e,n),t);function Rs(e){return an(_i(e*2.55),0,255)}function dn(e){return an(_i(e*255),0,255)}function je(e){return an(_i(e/2.55)/100,0,1)}function Rc(e){return an(_i(e*100),0,100)}const ce={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},sa=[..."0123456789ABCDEF"],Sy=e=>sa[e&15],My=e=>sa[(e&240)>>4]+sa[e&15],Ei=e=>(e&240)>>4===(e&15),Cy=e=>Ei(e.r)&&Ei(e.g)&&Ei(e.b)&&Ei(e.a);function Ey(e){var t=e.length,n;return e[0]==="#"&&(t===4||t===5?n={r:255&ce[e[1]]*17,g:255&ce[e[2]]*17,b:255&ce[e[3]]*17,a:t===5?ce[e[4]]*17:255}:(t===7||t===9)&&(n={r:ce[e[1]]<<4|ce[e[2]],g:ce[e[3]]<<4|ce[e[4]],b:ce[e[5]]<<4|ce[e[6]],a:t===9?ce[e[7]]<<4|ce[e[8]]:255})),n}const Py=(e,t)=>e<255?t(e):"";function ky(e){var t=Cy(e)?Sy:My;return e?"#"+t(e.r)+t(e.g)+t(e.b)+Py(e.a,t):void 0}const Ay=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function ad(e,t,n){const s=t*Math.min(n,1-n),i=(o,r=(o+e/30)%12)=>n-s*Math.max(Math.min(r-3,9-r,1),-1);return[i(0),i(8),i(4)]}function Oy(e,t,n){const s=(i,o=(i+e/60)%6)=>n-n*t*Math.max(Math.min(o,4-o,1),0);return[s(5),s(3),s(1)]}function Ry(e,t,n){const s=ad(e,1,.5);let i;for(t+n>1&&(i=1/(t+n),t*=i,n*=i),i=0;i<3;i++)s[i]*=1-t-n,s[i]+=t;return s}function Ty(e,t,n,s,i){return e===i?(t-n)/s+(t<n?6:0):t===i?(n-e)/s+2:(e-t)/s+4}function Ya(e){const n=e.r/255,s=e.g/255,i=e.b/255,o=Math.max(n,s,i),r=Math.min(n,s,i),a=(o+r)/2;let l,c,u;return o!==r&&(u=o-r,c=a>.5?u/(2-o-r):u/(o+r),l=Ty(n,s,i,u,o),l=l*60+.5),[l|0,c||0,a]}function Xa(e,t,n,s){return(Array.isArray(t)?e(t[0],t[1],t[2]):e(t,n,s)).map(dn)}function Ga(e,t,n){return Xa(ad,e,t,n)}function Dy(e,t,n){return Xa(Ry,e,t,n)}function Ly(e,t,n){return Xa(Oy,e,t,n)}function ld(e){return(e%360+360)%360}function Fy(e){const t=Ay.exec(e);let n=255,s;if(!t)return;t[5]!==s&&(n=t[6]?Rs(+t[5]):dn(+t[5]));const i=ld(+t[2]),o=+t[3]/100,r=+t[4]/100;return t[1]==="hwb"?s=Dy(i,o,r):t[1]==="hsv"?s=Ly(i,o,r):s=Ga(i,o,r),{r:s[0],g:s[1],b:s[2],a:n}}function Iy(e,t){var n=Ya(e);n[0]=ld(n[0]+t),n=Ga(n),e.r=n[0],e.g=n[1],e.b=n[2]}function Ny(e){if(!e)return;const t=Ya(e),n=t[0],s=Rc(t[1]),i=Rc(t[2]);return e.a<255?`hsla(${n}, ${s}%, ${i}%, ${je(e.a)})`:`hsl(${n}, ${s}%, ${i}%)`}const Tc={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},Dc={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function By(){const e={},t=Object.keys(Dc),n=Object.keys(Tc);let s,i,o,r,a;for(s=0;s<t.length;s++){for(r=a=t[s],i=0;i<n.length;i++)o=n[i],a=a.replace(o,Tc[o]);o=parseInt(Dc[r],16),e[a]=[o>>16&255,o>>8&255,o&255]}return e}let Pi;function zy(e){Pi||(Pi=By(),Pi.transparent=[0,0,0,0]);const t=Pi[e.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const Hy=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function Vy(e){const t=Hy.exec(e);let n=255,s,i,o;if(t){if(t[7]!==s){const r=+t[7];n=t[8]?Rs(r):an(r*255,0,255)}return s=+t[1],i=+t[3],o=+t[5],s=255&(t[2]?Rs(s):an(s,0,255)),i=255&(t[4]?Rs(i):an(i,0,255)),o=255&(t[6]?Rs(o):an(o,0,255)),{r:s,g:i,b:o,a:n}}}function jy(e){return e&&(e.a<255?`rgba(${e.r}, ${e.g}, ${e.b}, ${je(e.a)})`:`rgb(${e.r}, ${e.g}, ${e.b})`)}const _r=e=>e<=.0031308?e*12.92:Math.pow(e,1/2.4)*1.055-.055,Xn=e=>e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4);function Wy(e,t,n){const s=Xn(je(e.r)),i=Xn(je(e.g)),o=Xn(je(e.b));return{r:dn(_r(s+n*(Xn(je(t.r))-s))),g:dn(_r(i+n*(Xn(je(t.g))-i))),b:dn(_r(o+n*(Xn(je(t.b))-o))),a:e.a+n*(t.a-e.a)}}function ki(e,t,n){if(e){let s=Ya(e);s[t]=Math.max(0,Math.min(s[t]+s[t]*n,t===0?360:1)),s=Ga(s),e.r=s[0],e.g=s[1],e.b=s[2]}}function cd(e,t){return e&&Object.assign(t||{},e)}function Lc(e){var t={r:0,g:0,b:0,a:255};return Array.isArray(e)?e.length>=3&&(t={r:e[0],g:e[1],b:e[2],a:255},e.length>3&&(t.a=dn(e[3]))):(t=cd(e,{r:0,g:0,b:0,a:1}),t.a=dn(t.a)),t}function $y(e){return e.charAt(0)==="r"?Vy(e):Fy(e)}class oi{constructor(t){if(t instanceof oi)return t;const n=typeof t;let s;n==="object"?s=Lc(t):n==="string"&&(s=Ey(t)||zy(t)||$y(t)),this._rgb=s,this._valid=!!s}get valid(){return this._valid}get rgb(){var t=cd(this._rgb);return t&&(t.a=je(t.a)),t}set rgb(t){this._rgb=Lc(t)}rgbString(){return this._valid?jy(this._rgb):void 0}hexString(){return this._valid?ky(this._rgb):void 0}hslString(){return this._valid?Ny(this._rgb):void 0}mix(t,n){if(t){const s=this.rgb,i=t.rgb;let o;const r=n===o?.5:n,a=2*r-1,l=s.a-i.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-c,s.r=255&c*s.r+o*i.r+.5,s.g=255&c*s.g+o*i.g+.5,s.b=255&c*s.b+o*i.b+.5,s.a=r*s.a+(1-r)*i.a,this.rgb=s}return this}interpolate(t,n){return t&&(this._rgb=Wy(this._rgb,t._rgb,n)),this}clone(){return new oi(this.rgb)}alpha(t){return this._rgb.a=dn(t),this}clearer(t){const n=this._rgb;return n.a*=1-t,this}greyscale(){const t=this._rgb,n=_i(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=n,this}opaquer(t){const n=this._rgb;return n.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return ki(this._rgb,2,t),this}darken(t){return ki(this._rgb,2,-t),this}saturate(t){return ki(this._rgb,1,t),this}desaturate(t){return ki(this._rgb,1,-t),this}rotate(t){return Iy(this._rgb,t),this}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function Be(){}const Uy=(()=>{let e=0;return()=>e++})();function it(e){return e==null}function wt(e){if(Array.isArray&&Array.isArray(e))return!0;const t=Object.prototype.toString.call(e);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function ot(e){return e!==null&&Object.prototype.toString.call(e)==="[object Object]"}function Ct(e){return(typeof e=="number"||e instanceof Number)&&isFinite(+e)}function ie(e,t){return Ct(e)?e:t}function st(e,t){return typeof e>"u"?t:e}const Ky=(e,t)=>typeof e=="string"&&e.endsWith("%")?parseFloat(e)/100:+e/t,ud=(e,t)=>typeof e=="string"&&e.endsWith("%")?parseFloat(e)/100*t:+e;function yt(e,t,n){if(e&&typeof e.call=="function")return e.apply(n,t)}function ht(e,t,n,s){let i,o,r;if(wt(e))for(o=e.length,i=0;i<o;i++)t.call(n,e[i],i);else if(ot(e))for(r=Object.keys(e),o=r.length,i=0;i<o;i++)t.call(n,e[r[i]],r[i])}function yo(e,t){let n,s,i,o;if(!e||!t||e.length!==t.length)return!1;for(n=0,s=e.length;n<s;++n)if(i=e[n],o=t[n],i.datasetIndex!==o.datasetIndex||i.index!==o.index)return!1;return!0}function xo(e){if(wt(e))return e.map(xo);if(ot(e)){const t=Object.create(null),n=Object.keys(e),s=n.length;let i=0;for(;i<s;++i)t[n[i]]=xo(e[n[i]]);return t}return e}function fd(e){return["__proto__","prototype","constructor"].indexOf(e)===-1}function qy(e,t,n,s){if(!fd(e))return;const i=t[e],o=n[e];ot(i)&&ot(o)?ri(i,o,s):t[e]=xo(o)}function ri(e,t,n){const s=wt(t)?t:[t],i=s.length;if(!ot(e))return e;n=n||{};const o=n.merger||qy;let r;for(let a=0;a<i;++a){if(r=s[a],!ot(r))continue;const l=Object.keys(r);for(let c=0,u=l.length;c<u;++c)o(l[c],e,r,n)}return e}function Ks(e,t){return ri(e,t,{merger:Yy})}function Yy(e,t,n){if(!fd(e))return;const s=t[e],i=n[e];ot(s)&&ot(i)?Ks(s,i):Object.prototype.hasOwnProperty.call(t,e)||(t[e]=xo(i))}const Fc={"":e=>e,x:e=>e.x,y:e=>e.y};function Xy(e){const t=e.split("."),n=[];let s="";for(const i of t)s+=i,s.endsWith("\\")?s=s.slice(0,-1)+".":(n.push(s),s="");return n}function Gy(e){const t=Xy(e);return n=>{for(const s of t){if(s==="")break;n=n&&n[s]}return n}}function bn(e,t){return(Fc[t]||(Fc[t]=Gy(t)))(e)}function Ja(e){return e.charAt(0).toUpperCase()+e.slice(1)}const ai=e=>typeof e<"u",_n=e=>typeof e=="function",Ic=(e,t)=>{if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0};function Jy(e){return e.type==="mouseup"||e.type==="click"||e.type==="contextmenu"}const vt=Math.PI,xt=2*vt,Qy=xt+vt,vo=Number.POSITIVE_INFINITY,Zy=vt/180,Pt=vt/2,Mn=vt/4,Nc=vt*2/3,ln=Math.log10,Fe=Math.sign;function qs(e,t,n){return Math.abs(e-t)<n}function Bc(e){const t=Math.round(e);e=qs(e,t,e/1e3)?t:e;const n=Math.pow(10,Math.floor(ln(e))),s=e/n;return(s<=1?1:s<=2?2:s<=5?5:10)*n}function tx(e){const t=[],n=Math.sqrt(e);let s;for(s=1;s<n;s++)e%s===0&&(t.push(s),t.push(e/s));return n===(n|0)&&t.push(n),t.sort((i,o)=>i-o).pop(),t}function ex(e){return typeof e=="symbol"||typeof e=="object"&&e!==null&&!(Symbol.toPrimitive in e||"toString"in e||"valueOf"in e)}function hs(e){return!ex(e)&&!isNaN(parseFloat(e))&&isFinite(e)}function nx(e,t){const n=Math.round(e);return n-t<=e&&n+t>=e}function hd(e,t,n){let s,i,o;for(s=0,i=e.length;s<i;s++)o=e[s][n],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function me(e){return e*(vt/180)}function Qa(e){return e*(180/vt)}function zc(e){if(!Ct(e))return;let t=1,n=0;for(;Math.round(e*t)/t!==e;)t*=10,n++;return n}function dd(e,t){const n=t.x-e.x,s=t.y-e.y,i=Math.sqrt(n*n+s*s);let o=Math.atan2(s,n);return o<-.5*vt&&(o+=xt),{angle:o,distance:i}}function ia(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}function sx(e,t){return(e-t+Qy)%xt-vt}function oe(e){return(e%xt+xt)%xt}function li(e,t,n,s){const i=oe(e),o=oe(t),r=oe(n),a=oe(o-i),l=oe(r-i),c=oe(i-o),u=oe(i-r);return i===o||i===r||s&&o===r||a>l&&c<u}function It(e,t,n){return Math.max(t,Math.min(n,e))}function ix(e){return It(e,-32768,32767)}function Ke(e,t,n,s=1e-6){return e>=Math.min(t,n)-s&&e<=Math.max(t,n)+s}function Za(e,t,n){n=n||(r=>e[r]<t);let s=e.length-1,i=0,o;for(;s-i>1;)o=i+s>>1,n(o)?i=o:s=o;return{lo:i,hi:s}}const qe=(e,t,n,s)=>Za(e,n,s?i=>{const o=e[i][t];return o<n||o===n&&e[i+1][t]===n}:i=>e[i][t]<n),ox=(e,t,n)=>Za(e,n,s=>e[s][t]>=n);function rx(e,t,n){let s=0,i=e.length;for(;s<i&&e[s]<t;)s++;for(;i>s&&e[i-1]>n;)i--;return s>0||i<e.length?e.slice(s,i):e}const pd=["push","pop","shift","splice","unshift"];function ax(e,t){if(e._chartjs){e._chartjs.listeners.push(t);return}Object.defineProperty(e,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),pd.forEach(n=>{const s="_onData"+Ja(n),i=e[n];Object.defineProperty(e,n,{configurable:!0,enumerable:!1,value(...o){const r=i.apply(this,o);return e._chartjs.listeners.forEach(a=>{typeof a[s]=="function"&&a[s](...o)}),r}})})}function Hc(e,t){const n=e._chartjs;if(!n)return;const s=n.listeners,i=s.indexOf(t);i!==-1&&s.splice(i,1),!(s.length>0)&&(pd.forEach(o=>{delete e[o]}),delete e._chartjs)}function gd(e){const t=new Set(e);return t.size===e.length?e:Array.from(t)}const md=function(){return typeof window>"u"?function(e){return e()}:window.requestAnimationFrame}();function bd(e,t){let n=[],s=!1;return function(...i){n=i,s||(s=!0,md.call(window,()=>{s=!1,e.apply(t,n)}))}}function lx(e,t){let n;return function(...s){return t?(clearTimeout(n),n=setTimeout(e,t,s)):e.apply(this,s),t}}const tl=e=>e==="start"?"left":e==="end"?"right":"center",Bt=(e,t,n)=>e==="start"?t:e==="end"?n:(t+n)/2,cx=(e,t,n,s)=>e===(s?"left":"right")?n:e==="center"?(t+n)/2:t;function _d(e,t,n){const s=t.length;let i=0,o=s;if(e._sorted){const{iScale:r,vScale:a,_parsed:l}=e,c=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null,u=r.axis,{min:f,max:h,minDefined:d,maxDefined:p}=r.getUserBounds();if(d){if(i=Math.min(qe(l,u,f).lo,n?s:qe(t,u,r.getPixelForValue(f)).lo),c){const g=l.slice(0,i+1).reverse().findIndex(m=>!it(m[a.axis]));i-=Math.max(0,g)}i=It(i,0,s-1)}if(p){let g=Math.max(qe(l,r.axis,h,!0).hi+1,n?0:qe(t,u,r.getPixelForValue(h),!0).hi+1);if(c){const m=l.slice(g-1).findIndex(_=>!it(_[a.axis]));g+=Math.max(0,m)}o=It(g,i,s)-i}else o=s-i}return{start:i,count:o}}function yd(e){const{xScale:t,yScale:n,_scaleRanges:s}=e,i={xmin:t.min,xmax:t.max,ymin:n.min,ymax:n.max};if(!s)return e._scaleRanges=i,!0;const o=s.xmin!==t.min||s.xmax!==t.max||s.ymin!==n.min||s.ymax!==n.max;return Object.assign(s,i),o}const Ai=e=>e===0||e===1,Vc=(e,t,n)=>-(Math.pow(2,10*(e-=1))*Math.sin((e-t)*xt/n)),jc=(e,t,n)=>Math.pow(2,-10*e)*Math.sin((e-t)*xt/n)+1,Ys={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>-e*(e-2),easeInOutQuad:e=>(e/=.5)<1?.5*e*e:-.5*(--e*(e-2)-1),easeInCubic:e=>e*e*e,easeOutCubic:e=>(e-=1)*e*e+1,easeInOutCubic:e=>(e/=.5)<1?.5*e*e*e:.5*((e-=2)*e*e+2),easeInQuart:e=>e*e*e*e,easeOutQuart:e=>-((e-=1)*e*e*e-1),easeInOutQuart:e=>(e/=.5)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2),easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>(e-=1)*e*e*e*e+1,easeInOutQuint:e=>(e/=.5)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2),easeInSine:e=>-Math.cos(e*Pt)+1,easeOutSine:e=>Math.sin(e*Pt),easeInOutSine:e=>-.5*(Math.cos(vt*e)-1),easeInExpo:e=>e===0?0:Math.pow(2,10*(e-1)),easeOutExpo:e=>e===1?1:-Math.pow(2,-10*e)+1,easeInOutExpo:e=>Ai(e)?e:e<.5?.5*Math.pow(2,10*(e*2-1)):.5*(-Math.pow(2,-10*(e*2-1))+2),easeInCirc:e=>e>=1?e:-(Math.sqrt(1-e*e)-1),easeOutCirc:e=>Math.sqrt(1-(e-=1)*e),easeInOutCirc:e=>(e/=.5)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1),easeInElastic:e=>Ai(e)?e:Vc(e,.075,.3),easeOutElastic:e=>Ai(e)?e:jc(e,.075,.3),easeInOutElastic(e){return Ai(e)?e:e<.5?.5*Vc(e*2,.1125,.45):.5+.5*jc(e*2-1,.1125,.45)},easeInBack(e){return e*e*((1.70158+1)*e-1.70158)},easeOutBack(e){return(e-=1)*e*((1.70158+1)*e********)+1},easeInOutBack(e){let t=1.70158;return(e/=.5)<1?.5*(e*e*(((t*=1.525)+1)*e-t)):.5*((e-=2)*e*(((t*=1.525)+1)*e+t)+2)},easeInBounce:e=>1-Ys.easeOutBounce(1-e),easeOutBounce(e){return e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375},easeInOutBounce:e=>e<.5?Ys.easeInBounce(e*2)*.5:Ys.easeOutBounce(e*2-1)*.5+.5};function el(e){if(e&&typeof e=="object"){const t=e.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function Wc(e){return el(e)?e:new oi(e)}function yr(e){return el(e)?e:new oi(e).saturate(.5).darken(.1).hexString()}const ux=["x","y","borderWidth","radius","tension"],fx=["color","borderColor","backgroundColor"];function hx(e){e.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),e.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),e.set("animations",{colors:{type:"color",properties:fx},numbers:{type:"number",properties:ux}}),e.describe("animations",{_fallback:"animation"}),e.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function dx(e){e.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const $c=new Map;function px(e,t){t=t||{};const n=e+JSON.stringify(t);let s=$c.get(n);return s||(s=new Intl.NumberFormat(e,t),$c.set(n,s)),s}function yi(e,t,n){return px(t,n).format(e)}const xd={values(e){return wt(e)?e:""+e},numeric(e,t,n){if(e===0)return"0";const s=this.chart.options.locale;let i,o=e;if(n.length>1){const c=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(c<1e-4||c>1e15)&&(i="scientific"),o=gx(e,n)}const r=ln(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:i,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),yi(e,s,l)},logarithmic(e,t,n){if(e===0)return"0";const s=n[t].significand||e/Math.pow(10,Math.floor(ln(e)));return[1,2,3,5,10,15].includes(s)||t>.8*n.length?xd.numeric.call(this,e,t,n):""}};function gx(e,t){let n=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(n)>=1&&e!==Math.floor(e)&&(n=e-Math.floor(e)),n}var Yo={formatters:xd};function mx(e){e.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,n)=>n.lineWidth,tickColor:(t,n)=>n.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Yo.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),e.route("scale.ticks","color","","color"),e.route("scale.grid","color","","borderColor"),e.route("scale.border","color","","borderColor"),e.route("scale.title","color","","color"),e.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),e.describe("scales",{_fallback:"scale"}),e.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const Wn=Object.create(null),oa=Object.create(null);function Xs(e,t){if(!t)return e;const n=t.split(".");for(let s=0,i=n.length;s<i;++s){const o=n[s];e=e[o]||(e[o]=Object.create(null))}return e}function xr(e,t,n){return typeof t=="string"?ri(Xs(e,t),n):ri(Xs(e,""),t)}class bx{constructor(t,n){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=s=>s.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(s,i)=>yr(i.backgroundColor),this.hoverBorderColor=(s,i)=>yr(i.borderColor),this.hoverColor=(s,i)=>yr(i.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(n)}set(t,n){return xr(this,t,n)}get(t){return Xs(this,t)}describe(t,n){return xr(oa,t,n)}override(t,n){return xr(Wn,t,n)}route(t,n,s,i){const o=Xs(this,t),r=Xs(this,s),a="_"+n;Object.defineProperties(o,{[a]:{value:o[n],writable:!0},[n]:{enumerable:!0,get(){const l=this[a],c=r[i];return ot(l)?Object.assign({},c,l):st(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(n=>n(this))}}var St=new bx({_scriptable:e=>!e.startsWith("on"),_indexable:e=>e!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[hx,dx,mx]);function _x(e){return!e||it(e.size)||it(e.family)?null:(e.style?e.style+" ":"")+(e.weight?e.weight+" ":"")+e.size+"px "+e.family}function wo(e,t,n,s,i){let o=t[i];return o||(o=t[i]=e.measureText(i).width,n.push(i)),o>s&&(s=o),s}function yx(e,t,n,s){s=s||{};let i=s.data=s.data||{},o=s.garbageCollect=s.garbageCollect||[];s.font!==t&&(i=s.data={},o=s.garbageCollect=[],s.font=t),e.save(),e.font=t;let r=0;const a=n.length;let l,c,u,f,h;for(l=0;l<a;l++)if(f=n[l],f!=null&&!wt(f))r=wo(e,i,o,r,f);else if(wt(f))for(c=0,u=f.length;c<u;c++)h=f[c],h!=null&&!wt(h)&&(r=wo(e,i,o,r,h));e.restore();const d=o.length/2;if(d>n.length){for(l=0;l<d;l++)delete i[o[l]];o.splice(0,d)}return r}function Cn(e,t,n){const s=e.currentDevicePixelRatio,i=n!==0?Math.max(n/2,.5):0;return Math.round((t-i)*s)/s+i}function Uc(e,t){!t&&!e||(t=t||e.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,e.width,e.height),t.restore())}function ra(e,t,n,s){vd(e,t,n,s,null)}function vd(e,t,n,s,i){let o,r,a,l,c,u,f,h;const d=t.pointStyle,p=t.rotation,g=t.radius;let m=(p||0)*Zy;if(d&&typeof d=="object"&&(o=d.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){e.save(),e.translate(n,s),e.rotate(m),e.drawImage(d,-d.width/2,-d.height/2,d.width,d.height),e.restore();return}if(!(isNaN(g)||g<=0)){switch(e.beginPath(),d){default:i?e.ellipse(n,s,i/2,g,0,0,xt):e.arc(n,s,g,0,xt),e.closePath();break;case"triangle":u=i?i/2:g,e.moveTo(n+Math.sin(m)*u,s-Math.cos(m)*g),m+=Nc,e.lineTo(n+Math.sin(m)*u,s-Math.cos(m)*g),m+=Nc,e.lineTo(n+Math.sin(m)*u,s-Math.cos(m)*g),e.closePath();break;case"rectRounded":c=g*.516,l=g-c,r=Math.cos(m+Mn)*l,f=Math.cos(m+Mn)*(i?i/2-c:l),a=Math.sin(m+Mn)*l,h=Math.sin(m+Mn)*(i?i/2-c:l),e.arc(n-f,s-a,c,m-vt,m-Pt),e.arc(n+h,s-r,c,m-Pt,m),e.arc(n+f,s+a,c,m,m+Pt),e.arc(n-h,s+r,c,m+Pt,m+vt),e.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*g,u=i?i/2:l,e.rect(n-u,s-l,2*u,2*l);break}m+=Mn;case"rectRot":f=Math.cos(m)*(i?i/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,h=Math.sin(m)*(i?i/2:g),e.moveTo(n-f,s-a),e.lineTo(n+h,s-r),e.lineTo(n+f,s+a),e.lineTo(n-h,s+r),e.closePath();break;case"crossRot":m+=Mn;case"cross":f=Math.cos(m)*(i?i/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,h=Math.sin(m)*(i?i/2:g),e.moveTo(n-f,s-a),e.lineTo(n+f,s+a),e.moveTo(n+h,s-r),e.lineTo(n-h,s+r);break;case"star":f=Math.cos(m)*(i?i/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,h=Math.sin(m)*(i?i/2:g),e.moveTo(n-f,s-a),e.lineTo(n+f,s+a),e.moveTo(n+h,s-r),e.lineTo(n-h,s+r),m+=Mn,f=Math.cos(m)*(i?i/2:g),r=Math.cos(m)*g,a=Math.sin(m)*g,h=Math.sin(m)*(i?i/2:g),e.moveTo(n-f,s-a),e.lineTo(n+f,s+a),e.moveTo(n+h,s-r),e.lineTo(n-h,s+r);break;case"line":r=i?i/2:Math.cos(m)*g,a=Math.sin(m)*g,e.moveTo(n-r,s-a),e.lineTo(n+r,s+a);break;case"dash":e.moveTo(n,s),e.lineTo(n+Math.cos(m)*(i?i/2:g),s+Math.sin(m)*g);break;case!1:e.closePath();break}e.fill(),t.borderWidth>0&&e.stroke()}}function Ye(e,t,n){return n=n||.5,!t||e&&e.x>t.left-n&&e.x<t.right+n&&e.y>t.top-n&&e.y<t.bottom+n}function Xo(e,t){e.save(),e.beginPath(),e.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),e.clip()}function Go(e){e.restore()}function xx(e,t,n,s,i){if(!t)return e.lineTo(n.x,n.y);if(i==="middle"){const o=(t.x+n.x)/2;e.lineTo(o,t.y),e.lineTo(o,n.y)}else i==="after"!=!!s?e.lineTo(t.x,n.y):e.lineTo(n.x,t.y);e.lineTo(n.x,n.y)}function vx(e,t,n,s){if(!t)return e.lineTo(n.x,n.y);e.bezierCurveTo(s?t.cp1x:t.cp2x,s?t.cp1y:t.cp2y,s?n.cp2x:n.cp1x,s?n.cp2y:n.cp1y,n.x,n.y)}function wx(e,t){t.translation&&e.translate(t.translation[0],t.translation[1]),it(t.rotation)||e.rotate(t.rotation),t.color&&(e.fillStyle=t.color),t.textAlign&&(e.textAlign=t.textAlign),t.textBaseline&&(e.textBaseline=t.textBaseline)}function Sx(e,t,n,s,i){if(i.strikethrough||i.underline){const o=e.measureText(s),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=n-o.actualBoundingBoxAscent,c=n+o.actualBoundingBoxDescent,u=i.strikethrough?(l+c)/2:c;e.strokeStyle=e.fillStyle,e.beginPath(),e.lineWidth=i.decorationWidth||2,e.moveTo(r,u),e.lineTo(a,u),e.stroke()}}function Mx(e,t){const n=e.fillStyle;e.fillStyle=t.color,e.fillRect(t.left,t.top,t.width,t.height),e.fillStyle=n}function $n(e,t,n,s,i,o={}){const r=wt(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(e.save(),e.font=i.string,wx(e,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&Mx(e,o.backdrop),a&&(o.strokeColor&&(e.strokeStyle=o.strokeColor),it(o.strokeWidth)||(e.lineWidth=o.strokeWidth),e.strokeText(c,n,s,o.maxWidth)),e.fillText(c,n,s,o.maxWidth),Sx(e,n,s,c,o),s+=Number(i.lineHeight);e.restore()}function ci(e,t){const{x:n,y:s,w:i,h:o,radius:r}=t;e.arc(n+r.topLeft,s+r.topLeft,r.topLeft,1.5*vt,vt,!0),e.lineTo(n,s+o-r.bottomLeft),e.arc(n+r.bottomLeft,s+o-r.bottomLeft,r.bottomLeft,vt,Pt,!0),e.lineTo(n+i-r.bottomRight,s+o),e.arc(n+i-r.bottomRight,s+o-r.bottomRight,r.bottomRight,Pt,0,!0),e.lineTo(n+i,s+r.topRight),e.arc(n+i-r.topRight,s+r.topRight,r.topRight,0,-Pt,!0),e.lineTo(n+r.topLeft,s)}const Cx=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Ex=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function Px(e,t){const n=(""+e).match(Cx);if(!n||n[1]==="normal")return t*1.2;switch(e=+n[2],n[3]){case"px":return e;case"%":e/=100;break}return t*e}const kx=e=>+e||0;function nl(e,t){const n={},s=ot(t),i=s?Object.keys(t):t,o=ot(e)?s?r=>st(e[r],e[t[r]]):r=>e[r]:()=>e;for(const r of i)n[r]=kx(o(r));return n}function wd(e){return nl(e,{top:"y",right:"x",bottom:"y",left:"x"})}function zn(e){return nl(e,["topLeft","topRight","bottomLeft","bottomRight"])}function $t(e){const t=wd(e);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function Dt(e,t){e=e||{},t=t||St.font;let n=st(e.size,t.size);typeof n=="string"&&(n=parseInt(n,10));let s=st(e.style,t.style);s&&!(""+s).match(Ex)&&(console.warn('Invalid font style specified: "'+s+'"'),s=void 0);const i={family:st(e.family,t.family),lineHeight:Px(st(e.lineHeight,t.lineHeight),n),size:n,style:s,weight:st(e.weight,t.weight),string:""};return i.string=_x(i),i}function Ts(e,t,n,s){let i,o,r;for(i=0,o=e.length;i<o;++i)if(r=e[i],r!==void 0&&r!==void 0)return r}function Ax(e,t,n){const{min:s,max:i}=e,o=ud(t,(i-s)/2),r=(a,l)=>n&&a===0?0:a+l;return{min:r(s,-Math.abs(o)),max:r(i,o)}}function xn(e,t){return Object.assign(Object.create(e),t)}function sl(e,t=[""],n,s,i=()=>e[0]){const o=n||e;typeof s>"u"&&(s=Ed("_fallback",e));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:e,_rootScopes:o,_fallback:s,_getTarget:i,override:a=>sl([a,...e],t,o,s)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete e[0][l],!0},get(a,l){return Md(a,l,()=>Nx(l,t,e,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(e[0])},has(a,l){return qc(a).includes(l)},ownKeys(a){return qc(a)},set(a,l,c){const u=a._storage||(a._storage=i());return a[l]=u[l]=c,delete a._keys,!0}})}function ds(e,t,n,s){const i={_cacheable:!1,_proxy:e,_context:t,_subProxy:n,_stack:new Set,_descriptors:Sd(e,s),setContext:o=>ds(e,o,n,s),override:o=>ds(e.override(o),t,n,s)};return new Proxy(i,{deleteProperty(o,r){return delete o[r],delete e[r],!0},get(o,r,a){return Md(o,r,()=>Rx(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(e,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(e,r)},getPrototypeOf(){return Reflect.getPrototypeOf(e)},has(o,r){return Reflect.has(e,r)},ownKeys(){return Reflect.ownKeys(e)},set(o,r,a){return e[r]=a,delete o[r],!0}})}function Sd(e,t={scriptable:!0,indexable:!0}){const{_scriptable:n=t.scriptable,_indexable:s=t.indexable,_allKeys:i=t.allKeys}=e;return{allKeys:i,scriptable:n,indexable:s,isScriptable:_n(n)?n:()=>n,isIndexable:_n(s)?s:()=>s}}const Ox=(e,t)=>e?e+Ja(t):t,il=(e,t)=>ot(t)&&e!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function Md(e,t,n){if(Object.prototype.hasOwnProperty.call(e,t)||t==="constructor")return e[t];const s=n();return e[t]=s,s}function Rx(e,t,n){const{_proxy:s,_context:i,_subProxy:o,_descriptors:r}=e;let a=s[t];return _n(a)&&r.isScriptable(t)&&(a=Tx(t,a,e,n)),wt(a)&&a.length&&(a=Dx(t,a,e,r.isIndexable)),il(t,a)&&(a=ds(a,i,o&&o[t],r)),a}function Tx(e,t,n,s){const{_proxy:i,_context:o,_subProxy:r,_stack:a}=n;if(a.has(e))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+e);a.add(e);let l=t(o,r||s);return a.delete(e),il(e,l)&&(l=ol(i._scopes,i,e,l)),l}function Dx(e,t,n,s){const{_proxy:i,_context:o,_subProxy:r,_descriptors:a}=n;if(typeof o.index<"u"&&s(e))return t[o.index%t.length];if(ot(t[0])){const l=t,c=i._scopes.filter(u=>u!==l);t=[];for(const u of l){const f=ol(c,i,e,u);t.push(ds(f,o,r&&r[e],a))}}return t}function Cd(e,t,n){return _n(e)?e(t,n):e}const Lx=(e,t)=>e===!0?t:typeof e=="string"?bn(t,e):void 0;function Fx(e,t,n,s,i){for(const o of t){const r=Lx(n,o);if(r){e.add(r);const a=Cd(r._fallback,n,i);if(typeof a<"u"&&a!==n&&a!==s)return a}else if(r===!1&&typeof s<"u"&&n!==s)return null}return!1}function ol(e,t,n,s){const i=t._rootScopes,o=Cd(t._fallback,n,s),r=[...e,...i],a=new Set;a.add(s);let l=Kc(a,r,n,o||n,s);return l===null||typeof o<"u"&&o!==n&&(l=Kc(a,r,o,l,s),l===null)?!1:sl(Array.from(a),[""],i,o,()=>Ix(t,n,s))}function Kc(e,t,n,s,i){for(;n;)n=Fx(e,t,n,s,i);return n}function Ix(e,t,n){const s=e._getTarget();t in s||(s[t]={});const i=s[t];return wt(i)&&ot(n)?n:i||{}}function Nx(e,t,n,s){let i;for(const o of t)if(i=Ed(Ox(o,e),n),typeof i<"u")return il(e,i)?ol(n,s,e,i):i}function Ed(e,t){for(const n of t){if(!n)continue;const s=n[e];if(typeof s<"u")return s}}function qc(e){let t=e._keys;return t||(t=e._keys=Bx(e._scopes)),t}function Bx(e){const t=new Set;for(const n of e)for(const s of Object.keys(n).filter(i=>!i.startsWith("_")))t.add(s);return Array.from(t)}function Pd(e,t,n,s){const{iScale:i}=e,{key:o="r"}=this._parsing,r=new Array(s);let a,l,c,u;for(a=0,l=s;a<l;++a)c=a+n,u=t[c],r[a]={r:i.parse(bn(u,o),c)};return r}const zx=Number.EPSILON||1e-14,ps=(e,t)=>t<e.length&&!e[t].skip&&e[t],kd=e=>e==="x"?"y":"x";function Hx(e,t,n,s){const i=e.skip?t:e,o=t,r=n.skip?t:n,a=ia(o,i),l=ia(r,o);let c=a/(a+l),u=l/(a+l);c=isNaN(c)?0:c,u=isNaN(u)?0:u;const f=s*c,h=s*u;return{previous:{x:o.x-f*(r.x-i.x),y:o.y-f*(r.y-i.y)},next:{x:o.x+h*(r.x-i.x),y:o.y+h*(r.y-i.y)}}}function Vx(e,t,n){const s=e.length;let i,o,r,a,l,c=ps(e,0);for(let u=0;u<s-1;++u)if(l=c,c=ps(e,u+1),!(!l||!c)){if(qs(t[u],0,zx)){n[u]=n[u+1]=0;continue}i=n[u]/t[u],o=n[u+1]/t[u],a=Math.pow(i,2)+Math.pow(o,2),!(a<=9)&&(r=3/Math.sqrt(a),n[u]=i*r*t[u],n[u+1]=o*r*t[u])}}function jx(e,t,n="x"){const s=kd(n),i=e.length;let o,r,a,l=ps(e,0);for(let c=0;c<i;++c){if(r=a,a=l,l=ps(e,c+1),!a)continue;const u=a[n],f=a[s];r&&(o=(u-r[n])/3,a[`cp1${n}`]=u-o,a[`cp1${s}`]=f-o*t[c]),l&&(o=(l[n]-u)/3,a[`cp2${n}`]=u+o,a[`cp2${s}`]=f+o*t[c])}}function Wx(e,t="x"){const n=kd(t),s=e.length,i=Array(s).fill(0),o=Array(s);let r,a,l,c=ps(e,0);for(r=0;r<s;++r)if(a=l,l=c,c=ps(e,r+1),!!l){if(c){const u=c[t]-l[t];i[r]=u!==0?(c[n]-l[n])/u:0}o[r]=a?c?Fe(i[r-1])!==Fe(i[r])?0:(i[r-1]+i[r])/2:i[r-1]:i[r]}Vx(e,i,o),jx(e,o,t)}function Oi(e,t,n){return Math.max(Math.min(e,n),t)}function $x(e,t){let n,s,i,o,r,a=Ye(e[0],t);for(n=0,s=e.length;n<s;++n)r=o,o=a,a=n<s-1&&Ye(e[n+1],t),o&&(i=e[n],r&&(i.cp1x=Oi(i.cp1x,t.left,t.right),i.cp1y=Oi(i.cp1y,t.top,t.bottom)),a&&(i.cp2x=Oi(i.cp2x,t.left,t.right),i.cp2y=Oi(i.cp2y,t.top,t.bottom)))}function Ux(e,t,n,s,i){let o,r,a,l;if(t.spanGaps&&(e=e.filter(c=>!c.skip)),t.cubicInterpolationMode==="monotone")Wx(e,i);else{let c=s?e[e.length-1]:e[0];for(o=0,r=e.length;o<r;++o)a=e[o],l=Hx(c,a,e[Math.min(o+1,r-(s?0:1))%r],t.tension),a.cp1x=l.previous.x,a.cp1y=l.previous.y,a.cp2x=l.next.x,a.cp2y=l.next.y,c=a}t.capBezierPoints&&$x(e,n)}function rl(){return typeof window<"u"&&typeof document<"u"}function al(e){let t=e.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function So(e,t,n){let s;return typeof e=="string"?(s=parseInt(e,10),e.indexOf("%")!==-1&&(s=s/100*t.parentNode[n])):s=e,s}const Jo=e=>e.ownerDocument.defaultView.getComputedStyle(e,null);function Kx(e,t){return Jo(e).getPropertyValue(t)}const qx=["top","right","bottom","left"];function Hn(e,t,n){const s={};n=n?"-"+n:"";for(let i=0;i<4;i++){const o=qx[i];s[o]=parseFloat(e[t+"-"+o+n])||0}return s.width=s.left+s.right,s.height=s.top+s.bottom,s}const Yx=(e,t,n)=>(e>0||t>0)&&(!n||!n.shadowRoot);function Xx(e,t){const n=e.touches,s=n&&n.length?n[0]:e,{offsetX:i,offsetY:o}=s;let r=!1,a,l;if(Yx(i,o,e.target))a=i,l=o;else{const c=t.getBoundingClientRect();a=s.clientX-c.left,l=s.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function An(e,t){if("native"in e)return e;const{canvas:n,currentDevicePixelRatio:s}=t,i=Jo(n),o=i.boxSizing==="border-box",r=Hn(i,"padding"),a=Hn(i,"border","width"),{x:l,y:c,box:u}=Xx(e,n),f=r.left+(u&&a.left),h=r.top+(u&&a.top);let{width:d,height:p}=t;return o&&(d-=r.width+a.width,p-=r.height+a.height),{x:Math.round((l-f)/d*n.width/s),y:Math.round((c-h)/p*n.height/s)}}function Gx(e,t,n){let s,i;if(t===void 0||n===void 0){const o=e&&al(e);if(!o)t=e.clientWidth,n=e.clientHeight;else{const r=o.getBoundingClientRect(),a=Jo(o),l=Hn(a,"border","width"),c=Hn(a,"padding");t=r.width-c.width-l.width,n=r.height-c.height-l.height,s=So(a.maxWidth,o,"clientWidth"),i=So(a.maxHeight,o,"clientHeight")}}return{width:t,height:n,maxWidth:s||vo,maxHeight:i||vo}}const Ri=e=>Math.round(e*10)/10;function Jx(e,t,n,s){const i=Jo(e),o=Hn(i,"margin"),r=So(i.maxWidth,e,"clientWidth")||vo,a=So(i.maxHeight,e,"clientHeight")||vo,l=Gx(e,t,n);let{width:c,height:u}=l;if(i.boxSizing==="content-box"){const h=Hn(i,"border","width"),d=Hn(i,"padding");c-=d.width+h.width,u-=d.height+h.height}return c=Math.max(0,c-o.width),u=Math.max(0,s?c/s:u-o.height),c=Ri(Math.min(c,r,l.maxWidth)),u=Ri(Math.min(u,a,l.maxHeight)),c&&!u&&(u=Ri(c/2)),(t!==void 0||n!==void 0)&&s&&l.height&&u>l.height&&(u=l.height,c=Ri(Math.floor(u*s))),{width:c,height:u}}function Yc(e,t,n){const s=t||1,i=Math.floor(e.height*s),o=Math.floor(e.width*s);e.height=Math.floor(e.height),e.width=Math.floor(e.width);const r=e.canvas;return r.style&&(n||!r.style.height&&!r.style.width)&&(r.style.height=`${e.height}px`,r.style.width=`${e.width}px`),e.currentDevicePixelRatio!==s||r.height!==i||r.width!==o?(e.currentDevicePixelRatio=s,r.height=i,r.width=o,e.ctx.setTransform(s,0,0,s,0,0),!0):!1}const Qx=function(){let e=!1;try{const t={get passive(){return e=!0,!1}};rl()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return e}();function Xc(e,t){const n=Kx(e,t),s=n&&n.match(/^(\d+)(\.\d+)?px$/);return s?+s[1]:void 0}function On(e,t,n,s){return{x:e.x+n*(t.x-e.x),y:e.y+n*(t.y-e.y)}}function Zx(e,t,n,s){return{x:e.x+n*(t.x-e.x),y:s==="middle"?n<.5?e.y:t.y:s==="after"?n<1?e.y:t.y:n>0?t.y:e.y}}function t0(e,t,n,s){const i={x:e.cp2x,y:e.cp2y},o={x:t.cp1x,y:t.cp1y},r=On(e,i,n),a=On(i,o,n),l=On(o,t,n),c=On(r,a,n),u=On(a,l,n);return On(c,u,n)}const e0=function(e,t){return{x(n){return e+e+t-n},setWidth(n){t=n},textAlign(n){return n==="center"?n:n==="right"?"left":"right"},xPlus(n,s){return n-s},leftForLtr(n,s){return n-s}}},n0=function(){return{x(e){return e},setWidth(e){},textAlign(e){return e},xPlus(e,t){return e+t},leftForLtr(e,t){return e}}};function os(e,t,n){return e?e0(t,n):n0()}function Ad(e,t){let n,s;(t==="ltr"||t==="rtl")&&(n=e.canvas.style,s=[n.getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",t,"important"),e.prevTextDirection=s)}function Od(e,t){t!==void 0&&(delete e.prevTextDirection,e.canvas.style.setProperty("direction",t[0],t[1]))}function Rd(e){return e==="angle"?{between:li,compare:sx,normalize:oe}:{between:Ke,compare:(t,n)=>t-n,normalize:t=>t}}function Gc({start:e,end:t,count:n,loop:s,style:i}){return{start:e%n,end:t%n,loop:s&&(t-e+1)%n===0,style:i}}function s0(e,t,n){const{property:s,start:i,end:o}=n,{between:r,normalize:a}=Rd(s),l=t.length;let{start:c,end:u,loop:f}=e,h,d;if(f){for(c+=l,u+=l,h=0,d=l;h<d&&r(a(t[c%l][s]),i,o);++h)c--,u--;c%=l,u%=l}return u<c&&(u+=l),{start:c,end:u,loop:f,style:e.style}}function Td(e,t,n){if(!n)return[e];const{property:s,start:i,end:o}=n,r=t.length,{compare:a,between:l,normalize:c}=Rd(s),{start:u,end:f,loop:h,style:d}=s0(e,t,n),p=[];let g=!1,m=null,_,b,w;const v=()=>l(i,w,_)&&a(i,w)!==0,S=()=>a(o,_)===0||l(o,w,_),A=()=>g||v(),k=()=>!g||S();for(let E=u,C=u;E<=f;++E)b=t[E%r],!b.skip&&(_=c(b[s]),_!==w&&(g=l(_,i,o),m===null&&A()&&(m=a(_,i)===0?E:C),m!==null&&k()&&(p.push(Gc({start:m,end:E,loop:h,count:r,style:d})),m=null),C=E,w=_));return m!==null&&p.push(Gc({start:m,end:f,loop:h,count:r,style:d})),p}function Dd(e,t){const n=[],s=e.segments;for(let i=0;i<s.length;i++){const o=Td(s[i],e.points,t);o.length&&n.push(...o)}return n}function i0(e,t,n,s){let i=0,o=t-1;if(n&&!s)for(;i<t&&!e[i].skip;)i++;for(;i<t&&e[i].skip;)i++;for(i%=t,n&&(o+=i);o>i&&e[o%t].skip;)o--;return o%=t,{start:i,end:o}}function o0(e,t,n,s){const i=e.length,o=[];let r=t,a=e[t],l;for(l=t+1;l<=n;++l){const c=e[l%i];c.skip||c.stop?a.skip||(s=!1,o.push({start:t%i,end:(l-1)%i,loop:s}),t=r=c.stop?l:null):(r=l,a.skip&&(t=l)),a=c}return r!==null&&o.push({start:t%i,end:r%i,loop:s}),o}function r0(e,t){const n=e.points,s=e.options.spanGaps,i=n.length;if(!i)return[];const o=!!e._loop,{start:r,end:a}=i0(n,i,o,s);if(s===!0)return Jc(e,[{start:r,end:a,loop:o}],n,t);const l=a<r?a+i:a,c=!!e._fullLoop&&r===0&&a===i-1;return Jc(e,o0(n,r,l,c),n,t)}function Jc(e,t,n,s){return!s||!s.setContext||!n?t:a0(e,t,n,s)}function a0(e,t,n,s){const i=e._chart.getContext(),o=Qc(e.options),{_datasetIndex:r,options:{spanGaps:a}}=e,l=n.length,c=[];let u=o,f=t[0].start,h=f;function d(p,g,m,_){const b=a?-1:1;if(p!==g){for(p+=l;n[p%l].skip;)p-=b;for(;n[g%l].skip;)g+=b;p%l!==g%l&&(c.push({start:p%l,end:g%l,loop:m,style:_}),u=_,f=g%l)}}for(const p of t){f=a?f:p.start;let g=n[f%l],m;for(h=f+1;h<=p.end;h++){const _=n[h%l];m=Qc(s.setContext(xn(i,{type:"segment",p0:g,p1:_,p0DataIndex:(h-1)%l,p1DataIndex:h%l,datasetIndex:r}))),l0(m,u)&&d(f,h-1,p.loop,u),g=_,u=m}f<h-1&&d(f,h-1,p.loop,u)}return c}function Qc(e){return{backgroundColor:e.backgroundColor,borderCapStyle:e.borderCapStyle,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderJoinStyle:e.borderJoinStyle,borderWidth:e.borderWidth,borderColor:e.borderColor}}function l0(e,t){if(!t)return!1;const n=[],s=function(i,o){return el(o)?(n.includes(o)||n.push(o),n.indexOf(o)):o};return JSON.stringify(e,s)!==JSON.stringify(t,s)}function Ti(e,t,n){return e.options.clip?e[n]:t[n]}function c0(e,t){const{xScale:n,yScale:s}=e;return n&&s?{left:Ti(n,t,"left"),right:Ti(n,t,"right"),top:Ti(s,t,"top"),bottom:Ti(s,t,"bottom")}:t}function Ld(e,t){const n=t._clip;if(n.disabled)return!1;const s=c0(t,e.chartArea);return{left:n.left===!1?0:s.left-(n.left===!0?0:n.left),right:n.right===!1?e.width:s.right+(n.right===!0?0:n.right),top:n.top===!1?0:s.top-(n.top===!0?0:n.top),bottom:n.bottom===!1?e.height:s.bottom+(n.bottom===!0?0:n.bottom)}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class u0{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,n,s,i){const o=n.listeners[i],r=n.duration;o.forEach(a=>a({chart:t,initial:n.initial,numSteps:r,currentStep:Math.min(s-n.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=md.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let n=0;this._charts.forEach((s,i)=>{if(!s.running||!s.items.length)return;const o=s.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>s.duration&&(s.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(i.draw(),this._notify(i,s,t,"progress")),o.length||(s.running=!1,this._notify(i,s,t,"complete"),s.initial=!1),n+=o.length}),this._lastDate=t,n===0&&(this._running=!1)}_getAnims(t){const n=this._charts;let s=n.get(t);return s||(s={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},n.set(t,s)),s}listen(t,n,s){this._getAnims(t).listeners[n].push(s)}add(t,n){!n||!n.length||this._getAnims(t).items.push(...n)}has(t){return this._getAnims(t).items.length>0}start(t){const n=this._charts.get(t);n&&(n.running=!0,n.start=Date.now(),n.duration=n.items.reduce((s,i)=>Math.max(s,i._duration),0),this._refresh())}running(t){if(!this._running)return!1;const n=this._charts.get(t);return!(!n||!n.running||!n.items.length)}stop(t){const n=this._charts.get(t);if(!n||!n.items.length)return;const s=n.items;let i=s.length-1;for(;i>=0;--i)s[i].cancel();n.items=[],this._notify(t,n,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var ze=new u0;const Zc="transparent",f0={boolean(e,t,n){return n>.5?t:e},color(e,t,n){const s=Wc(e||Zc),i=s.valid&&Wc(t||Zc);return i&&i.valid?i.mix(s,n).hexString():t},number(e,t,n){return e+(t-e)*n}};class h0{constructor(t,n,s,i){const o=n[s];i=Ts([t.to,i,o,t.from]);const r=Ts([t.from,o,i]);this._active=!0,this._fn=t.fn||f0[t.type||typeof r],this._easing=Ys[t.easing]||Ys.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=n,this._prop=s,this._from=r,this._to=i,this._promises=void 0}active(){return this._active}update(t,n,s){if(this._active){this._notify(!1);const i=this._target[this._prop],o=s-this._start,r=this._duration-o;this._start=s,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=Ts([t.to,n,i,t.from]),this._from=Ts([t.from,i,n])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const n=t-this._start,s=this._duration,i=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||n<s),!this._active){this._target[i]=a,this._notify(!0);return}if(n<0){this._target[i]=o;return}l=n/s%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[i]=this._fn(o,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((n,s)=>{t.push({res:n,rej:s})})}_notify(t){const n=t?"res":"rej",s=this._promises||[];for(let i=0;i<s.length;i++)s[i][n]()}}class Fd{constructor(t,n){this._chart=t,this._properties=new Map,this.configure(n)}configure(t){if(!ot(t))return;const n=Object.keys(St.animation),s=this._properties;Object.getOwnPropertyNames(t).forEach(i=>{const o=t[i];if(!ot(o))return;const r={};for(const a of n)r[a]=o[a];(wt(o.properties)&&o.properties||[i]).forEach(a=>{(a===i||!s.has(a))&&s.set(a,r)})})}_animateOptions(t,n){const s=n.options,i=p0(t,s);if(!i)return[];const o=this._createAnimations(i,s);return s.$shared&&d0(t.options.$animations,s).then(()=>{t.options=s},()=>{}),o}_createAnimations(t,n){const s=this._properties,i=[],o=t.$animations||(t.$animations={}),r=Object.keys(n),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){i.push(...this._animateOptions(t,n));continue}const u=n[c];let f=o[c];const h=s.get(c);if(f)if(h&&f.active()){f.update(h,u,a);continue}else f.cancel();if(!h||!h.duration){t[c]=u;continue}o[c]=f=new h0(h,t,c,u),i.push(f)}return i}update(t,n){if(this._properties.size===0){Object.assign(t,n);return}const s=this._createAnimations(t,n);if(s.length)return ze.add(this._chart,s),!0}}function d0(e,t){const n=[],s=Object.keys(t);for(let i=0;i<s.length;i++){const o=e[s[i]];o&&o.active()&&n.push(o.wait())}return Promise.all(n)}function p0(e,t){if(!t)return;let n=e.options;if(!n){e.options=t;return}return n.$shared&&(e.options=n=Object.assign({},n,{$shared:!1,$animations:{}})),n}function tu(e,t){const n=e&&e.options||{},s=n.reverse,i=n.min===void 0?t:0,o=n.max===void 0?t:0;return{start:s?o:i,end:s?i:o}}function g0(e,t,n){if(n===!1)return!1;const s=tu(e,n),i=tu(t,n);return{top:i.end,right:s.end,bottom:i.start,left:s.start}}function m0(e){let t,n,s,i;return ot(e)?(t=e.top,n=e.right,s=e.bottom,i=e.left):t=n=s=i=e,{top:t,right:n,bottom:s,left:i,disabled:e===!1}}function Id(e,t){const n=[],s=e._getSortedDatasetMetas(t);let i,o;for(i=0,o=s.length;i<o;++i)n.push(s[i].index);return n}function eu(e,t,n,s={}){const i=e.keys,o=s.mode==="single";let r,a,l,c;if(t===null)return;let u=!1;for(r=0,a=i.length;r<a;++r){if(l=+i[r],l===n){if(u=!0,s.all)continue;break}c=e.values[l],Ct(c)&&(o||t===0||Fe(t)===Fe(c))&&(t+=c)}return!u&&!s.all?0:t}function b0(e,t){const{iScale:n,vScale:s}=t,i=n.axis==="x"?"x":"y",o=s.axis==="x"?"x":"y",r=Object.keys(e),a=new Array(r.length);let l,c,u;for(l=0,c=r.length;l<c;++l)u=r[l],a[l]={[i]:u,[o]:e[u]};return a}function vr(e,t){const n=e&&e.options.stacked;return n||n===void 0&&t.stack!==void 0}function _0(e,t,n){return`${e.id}.${t.id}.${n.stack||n.type}`}function y0(e){const{min:t,max:n,minDefined:s,maxDefined:i}=e.getUserBounds();return{min:s?t:Number.NEGATIVE_INFINITY,max:i?n:Number.POSITIVE_INFINITY}}function x0(e,t,n){const s=e[t]||(e[t]={});return s[n]||(s[n]={})}function nu(e,t,n,s){for(const i of t.getMatchingVisibleMetas(s).reverse()){const o=e[i.index];if(n&&o>0||!n&&o<0)return i.index}return null}function su(e,t){const{chart:n,_cachedMeta:s}=e,i=n._stacks||(n._stacks={}),{iScale:o,vScale:r,index:a}=s,l=o.axis,c=r.axis,u=_0(o,r,s),f=t.length;let h;for(let d=0;d<f;++d){const p=t[d],{[l]:g,[c]:m}=p,_=p._stacks||(p._stacks={});h=_[c]=x0(i,u,g),h[a]=m,h._top=nu(h,r,!0,s.type),h._bottom=nu(h,r,!1,s.type);const b=h._visualValues||(h._visualValues={});b[a]=m}}function wr(e,t){const n=e.scales;return Object.keys(n).filter(s=>n[s].axis===t).shift()}function v0(e,t){return xn(e,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function w0(e,t,n){return xn(e,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:n,index:t,mode:"default",type:"data"})}function Ms(e,t){const n=e.controller.index,s=e.vScale&&e.vScale.axis;if(s){t=t||e._parsed;for(const i of t){const o=i._stacks;if(!o||o[s]===void 0||o[s][n]===void 0)return;delete o[s][n],o[s]._visualValues!==void 0&&o[s]._visualValues[n]!==void 0&&delete o[s]._visualValues[n]}}}const Sr=e=>e==="reset"||e==="none",iu=(e,t)=>t?e:Object.assign({},e),S0=(e,t,n)=>e&&!t.hidden&&t._stacked&&{keys:Id(n,!0),values:null};class _e{constructor(t,n){this.chart=t,this._ctx=t.ctx,this.index=n,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=vr(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&Ms(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,n=this._cachedMeta,s=this.getDataset(),i=(f,h,d,p)=>f==="x"?h:f==="r"?p:d,o=n.xAxisID=st(s.xAxisID,wr(t,"x")),r=n.yAxisID=st(s.yAxisID,wr(t,"y")),a=n.rAxisID=st(s.rAxisID,wr(t,"r")),l=n.indexAxis,c=n.iAxisID=i(l,o,r,a),u=n.vAxisID=i(l,r,o,a);n.xScale=this.getScaleForId(o),n.yScale=this.getScaleForId(r),n.rScale=this.getScaleForId(a),n.iScale=this.getScaleForId(c),n.vScale=this.getScaleForId(u)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const n=this._cachedMeta;return t===n.iScale?n.vScale:n.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&Hc(this._data,this),t._stacked&&Ms(t)}_dataCheck(){const t=this.getDataset(),n=t.data||(t.data=[]),s=this._data;if(ot(n)){const i=this._cachedMeta;this._data=b0(n,i)}else if(s!==n){if(s){Hc(s,this);const i=this._cachedMeta;Ms(i),i._parsed=[]}n&&Object.isExtensible(n)&&ax(n,this),this._syncList=[],this._data=n}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const n=this._cachedMeta,s=this.getDataset();let i=!1;this._dataCheck();const o=n._stacked;n._stacked=vr(n.vScale,n),n.stack!==s.stack&&(i=!0,Ms(n),n.stack=s.stack),this._resyncElements(t),(i||o!==n._stacked)&&(su(this,n._parsed),n._stacked=vr(n.vScale,n))}configure(){const t=this.chart.config,n=t.datasetScopeKeys(this._type),s=t.getOptionScopes(this.getDataset(),n,!0);this.options=t.createResolver(s,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,n){const{_cachedMeta:s,_data:i}=this,{iScale:o,_stacked:r}=s,a=o.axis;let l=t===0&&n===i.length?!0:s._sorted,c=t>0&&s._parsed[t-1],u,f,h;if(this._parsing===!1)s._parsed=i,s._sorted=!0,h=i;else{wt(i[t])?h=this.parseArrayData(s,i,t,n):ot(i[t])?h=this.parseObjectData(s,i,t,n):h=this.parsePrimitiveData(s,i,t,n);const d=()=>f[a]===null||c&&f[a]<c[a];for(u=0;u<n;++u)s._parsed[u+t]=f=h[u],l&&(d()&&(l=!1),c=f);s._sorted=l}r&&su(this,h)}parsePrimitiveData(t,n,s,i){const{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,c=o.getLabels(),u=o===r,f=new Array(i);let h,d,p;for(h=0,d=i;h<d;++h)p=h+s,f[h]={[a]:u||o.parse(c[p],p),[l]:r.parse(n[p],p)};return f}parseArrayData(t,n,s,i){const{xScale:o,yScale:r}=t,a=new Array(i);let l,c,u,f;for(l=0,c=i;l<c;++l)u=l+s,f=n[u],a[l]={x:o.parse(f[0],u),y:r.parse(f[1],u)};return a}parseObjectData(t,n,s,i){const{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(i);let u,f,h,d;for(u=0,f=i;u<f;++u)h=u+s,d=n[h],c[u]={x:o.parse(bn(d,a),h),y:r.parse(bn(d,l),h)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,n,s){const i=this.chart,o=this._cachedMeta,r=n[t.axis],a={keys:Id(i,!0),values:n._stacks[t.axis]._visualValues};return eu(a,r,o.index,{mode:s})}updateRangeFromParsed(t,n,s,i){const o=s[n.axis];let r=o===null?NaN:o;const a=i&&s._stacks[n.axis];i&&a&&(i.values=a,r=eu(i,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,n){const s=this._cachedMeta,i=s._parsed,o=s._sorted&&t===s.iScale,r=i.length,a=this._getOtherScale(t),l=S0(n,s,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:u,max:f}=y0(a);let h,d;function p(){d=i[h];const g=d[a.axis];return!Ct(d[t.axis])||u>g||f<g}for(h=0;h<r&&!(!p()&&(this.updateRangeFromParsed(c,t,d,l),o));++h);if(o){for(h=r-1;h>=0;--h)if(!p()){this.updateRangeFromParsed(c,t,d,l);break}}return c}getAllParsedValues(t){const n=this._cachedMeta._parsed,s=[];let i,o,r;for(i=0,o=n.length;i<o;++i)r=n[i][t.axis],Ct(r)&&s.push(r);return s}getMaxOverflow(){return!1}getLabelAndValue(t){const n=this._cachedMeta,s=n.iScale,i=n.vScale,o=this.getParsed(t);return{label:s?""+s.getLabelForValue(o[s.axis]):"",value:i?""+i.getLabelForValue(o[i.axis]):""}}_update(t){const n=this._cachedMeta;this.update(t||"default"),n._clip=m0(st(this.options.clip,g0(n.xScale,n.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,n=this.chart,s=this._cachedMeta,i=s.data||[],o=n.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||i.length-a,c=this.options.drawActiveElementsOnTop;let u;for(s.dataset&&s.dataset.draw(t,o,a,l),u=a;u<a+l;++u){const f=i[u];f.hidden||(f.active&&c?r.push(f):f.draw(t,o))}for(u=0;u<r.length;++u)r[u].draw(t,o)}getStyle(t,n){const s=n?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(s):this.resolveDataElementOptions(t||0,s)}getContext(t,n,s){const i=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];o=r.$context||(r.$context=w0(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=i.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=v0(this.chart.getContext(),this.index)),o.dataset=i,o.index=o.datasetIndex=this.index;return o.active=!!n,o.mode=s,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,n){return this._resolveElementOptions(this.dataElementType.id,n,t)}_resolveElementOptions(t,n="default",s){const i=n==="active",o=this._cachedDataOpts,r=t+"-"+n,a=o[r],l=this.enableOptionSharing&&ai(s);if(a)return iu(a,l);const c=this.chart.config,u=c.datasetElementScopeKeys(this._type,t),f=i?[`${t}Hover`,"hover",t,""]:[t,""],h=c.getOptionScopes(this.getDataset(),u),d=Object.keys(St.elements[t]),p=()=>this.getContext(s,i,n),g=c.resolveNamedOptions(h,d,p,f);return g.$shared&&(g.$shared=l,o[r]=Object.freeze(iu(g,l))),g}_resolveAnimations(t,n,s){const i=this.chart,o=this._cachedDataOpts,r=`animation-${n}`,a=o[r];if(a)return a;let l;if(i.options.animation!==!1){const u=this.chart.config,f=u.datasetAnimationScopeKeys(this._type,n),h=u.getOptionScopes(this.getDataset(),f);l=u.createResolver(h,this.getContext(t,s,n))}const c=new Fd(i,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,n){return!n||Sr(t)||this.chart._animationsDisabled}_getSharedOptions(t,n){const s=this.resolveDataElementOptions(t,n),i=this._sharedOptions,o=this.getSharedOptions(s),r=this.includeOptions(n,o)||o!==i;return this.updateSharedOptions(o,n,s),{sharedOptions:o,includeOptions:r}}updateElement(t,n,s,i){Sr(i)?Object.assign(t,s):this._resolveAnimations(n,i).update(t,s)}updateSharedOptions(t,n,s){t&&!Sr(n)&&this._resolveAnimations(void 0,n).update(t,s)}_setStyle(t,n,s,i){t.active=i;const o=this.getStyle(n,i);this._resolveAnimations(n,s,i).update(t,{options:!i&&this.getSharedOptions(o)||o})}removeHoverStyle(t,n,s){this._setStyle(t,s,"active",!1)}setHoverStyle(t,n,s){this._setStyle(t,s,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const n=this._data,s=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const i=s.length,o=n.length,r=Math.min(o,i);r&&this.parse(0,r),o>i?this._insertElements(i,o-i,t):o<i&&this._removeElements(o,i-o)}_insertElements(t,n,s=!0){const i=this._cachedMeta,o=i.data,r=t+n;let a;const l=c=>{for(c.length+=n,a=c.length-1;a>=r;a--)c[a]=c[a-n]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(i._parsed),this.parse(t,n),s&&this.updateElements(o,t,n,"reset")}updateElements(t,n,s,i){}_removeElements(t,n){const s=this._cachedMeta;if(this._parsing){const i=s._parsed.splice(t,n);s._stacked&&Ms(s,i)}s.data.splice(t,n)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[n,s,i]=t;this[n](s,i)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,n){n&&this._sync(["_removeElements",t,n]);const s=arguments.length-2;s&&this._sync(["_insertElements",t,s])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}N(_e,"defaults",{}),N(_e,"datasetElementType",null),N(_e,"dataElementType",null);function M0(e,t){if(!e._cache.$bar){const n=e.getMatchingVisibleMetas(t);let s=[];for(let i=0,o=n.length;i<o;i++)s=s.concat(n[i].controller.getAllParsedValues(e));e._cache.$bar=gd(s.sort((i,o)=>i-o))}return e._cache.$bar}function C0(e){const t=e.iScale,n=M0(t,e.type);let s=t._length,i,o,r,a;const l=()=>{r===32767||r===-32768||(ai(a)&&(s=Math.min(s,Math.abs(r-a)||s)),a=r)};for(i=0,o=n.length;i<o;++i)r=t.getPixelForValue(n[i]),l();for(a=void 0,i=0,o=t.ticks.length;i<o;++i)r=t.getPixelForTick(i),l();return s}function E0(e,t,n,s){const i=n.barThickness;let o,r;return it(i)?(o=t.min*n.categoryPercentage,r=n.barPercentage):(o=i*s,r=1),{chunk:o/s,ratio:r,start:t.pixels[e]-o/2}}function P0(e,t,n,s){const i=t.pixels,o=i[e];let r=e>0?i[e-1]:null,a=e<i.length-1?i[e+1]:null;const l=n.categoryPercentage;r===null&&(r=o-(a===null?t.end-t.start:a-o)),a===null&&(a=o+o-r);const c=o-(o-Math.min(r,a))/2*l;return{chunk:Math.abs(a-r)/2*l/s,ratio:n.barPercentage,start:c}}function k0(e,t,n,s){const i=n.parse(e[0],s),o=n.parse(e[1],s),r=Math.min(i,o),a=Math.max(i,o);let l=r,c=a;Math.abs(r)>Math.abs(a)&&(l=a,c=r),t[n.axis]=c,t._custom={barStart:l,barEnd:c,start:i,end:o,min:r,max:a}}function Nd(e,t,n,s){return wt(e)?k0(e,t,n,s):t[n.axis]=n.parse(e,s),t}function ou(e,t,n,s){const i=e.iScale,o=e.vScale,r=i.getLabels(),a=i===o,l=[];let c,u,f,h;for(c=n,u=n+s;c<u;++c)h=t[c],f={},f[i.axis]=a||i.parse(r[c],c),l.push(Nd(h,f,o,c));return l}function Mr(e){return e&&e.barStart!==void 0&&e.barEnd!==void 0}function A0(e,t,n){return e!==0?Fe(e):(t.isHorizontal()?1:-1)*(t.min>=n?1:-1)}function O0(e){let t,n,s,i,o;return e.horizontal?(t=e.base>e.x,n="left",s="right"):(t=e.base<e.y,n="bottom",s="top"),t?(i="end",o="start"):(i="start",o="end"),{start:n,end:s,reverse:t,top:i,bottom:o}}function R0(e,t,n,s){let i=t.borderSkipped;const o={};if(!i){e.borderSkipped=o;return}if(i===!0){e.borderSkipped={top:!0,right:!0,bottom:!0,left:!0};return}const{start:r,end:a,reverse:l,top:c,bottom:u}=O0(e);i==="middle"&&n&&(e.enableBorderRadius=!0,(n._top||0)===s?i=c:(n._bottom||0)===s?i=u:(o[ru(u,r,a,l)]=!0,i=c)),o[ru(i,r,a,l)]=!0,e.borderSkipped=o}function ru(e,t,n,s){return s?(e=T0(e,t,n),e=au(e,n,t)):e=au(e,t,n),e}function T0(e,t,n){return e===t?n:e===n?t:e}function au(e,t,n){return e==="start"?t:e==="end"?n:e}function D0(e,{inflateAmount:t},n){e.inflateAmount=t==="auto"?n===1?.33:0:t}class Xi extends _e{parsePrimitiveData(t,n,s,i){return ou(t,n,s,i)}parseArrayData(t,n,s,i){return ou(t,n,s,i)}parseObjectData(t,n,s,i){const{iScale:o,vScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=o.axis==="x"?a:l,u=r.axis==="x"?a:l,f=[];let h,d,p,g;for(h=s,d=s+i;h<d;++h)g=n[h],p={},p[o.axis]=o.parse(bn(g,c),h),f.push(Nd(bn(g,u),p,r,h));return f}updateRangeFromParsed(t,n,s,i){super.updateRangeFromParsed(t,n,s,i);const o=s._custom;o&&n===this._cachedMeta.vScale&&(t.min=Math.min(t.min,o.min),t.max=Math.max(t.max,o.max))}getMaxOverflow(){return 0}getLabelAndValue(t){const n=this._cachedMeta,{iScale:s,vScale:i}=n,o=this.getParsed(t),r=o._custom,a=Mr(r)?"["+r.start+", "+r.end+"]":""+i.getLabelForValue(o[i.axis]);return{label:""+s.getLabelForValue(o[s.axis]),value:a}}initialize(){this.enableOptionSharing=!0,super.initialize();const t=this._cachedMeta;t.stack=this.getDataset().stack}update(t){const n=this._cachedMeta;this.updateElements(n.data,0,n.data.length,t)}updateElements(t,n,s,i){const o=i==="reset",{index:r,_cachedMeta:{vScale:a}}=this,l=a.getBasePixel(),c=a.isHorizontal(),u=this._getRuler(),{sharedOptions:f,includeOptions:h}=this._getSharedOptions(n,i);for(let d=n;d<n+s;d++){const p=this.getParsed(d),g=o||it(p[a.axis])?{base:l,head:l}:this._calculateBarValuePixels(d),m=this._calculateBarIndexPixels(d,u),_=(p._stacks||{})[a.axis],b={horizontal:c,base:g.base,enableBorderRadius:!_||Mr(p._custom)||r===_._top||r===_._bottom,x:c?g.head:m.center,y:c?m.center:g.head,height:c?m.size:Math.abs(g.size),width:c?Math.abs(g.size):m.size};h&&(b.options=f||this.resolveDataElementOptions(d,t[d].active?"active":i));const w=b.options||t[d].options;R0(b,w,_,r),D0(b,w,u.ratio),this.updateElement(t[d],d,b,i)}}_getStacks(t,n){const{iScale:s}=this._cachedMeta,i=s.getMatchingVisibleMetas(this._type).filter(u=>u.controller.options.grouped),o=s.options.stacked,r=[],a=this._cachedMeta.controller.getParsed(n),l=a&&a[s.axis],c=u=>{const f=u._parsed.find(d=>d[s.axis]===l),h=f&&f[u.vScale.axis];if(it(h)||isNaN(h))return!0};for(const u of i)if(!(n!==void 0&&c(u))&&((o===!1||r.indexOf(u.stack)===-1||o===void 0&&u.stack===void 0)&&r.push(u.stack),u.index===t))break;return r.length||r.push(void 0),r}_getStackCount(t){return this._getStacks(void 0,t).length}_getStackIndex(t,n,s){const i=this._getStacks(t,s),o=n!==void 0?i.indexOf(n):-1;return o===-1?i.length-1:o}_getRuler(){const t=this.options,n=this._cachedMeta,s=n.iScale,i=[];let o,r;for(o=0,r=n.data.length;o<r;++o)i.push(s.getPixelForValue(this.getParsed(o)[s.axis],o));const a=t.barThickness;return{min:a||C0(n),pixels:i,start:s._startPixel,end:s._endPixel,stackCount:this._getStackCount(),scale:s,grouped:t.grouped,ratio:a?1:t.categoryPercentage*t.barPercentage}}_calculateBarValuePixels(t){const{_cachedMeta:{vScale:n,_stacked:s,index:i},options:{base:o,minBarLength:r}}=this,a=o||0,l=this.getParsed(t),c=l._custom,u=Mr(c);let f=l[n.axis],h=0,d=s?this.applyStack(n,l,s):f,p,g;d!==f&&(h=d-f,d=f),u&&(f=c.barStart,d=c.barEnd-c.barStart,f!==0&&Fe(f)!==Fe(c.barEnd)&&(h=0),h+=f);const m=!it(o)&&!u?o:h;let _=n.getPixelForValue(m);if(this.chart.getDataVisibility(t)?p=n.getPixelForValue(h+d):p=_,g=p-_,Math.abs(g)<r){g=A0(g,n,a)*r,f===a&&(_-=g/2);const b=n.getPixelForDecimal(0),w=n.getPixelForDecimal(1),v=Math.min(b,w),S=Math.max(b,w);_=Math.max(Math.min(_,S),v),p=_+g,s&&!u&&(l._stacks[n.axis]._visualValues[i]=n.getValueForPixel(p)-n.getValueForPixel(_))}if(_===n.getPixelForValue(a)){const b=Fe(g)*n.getLineWidthForValue(a)/2;_+=b,g-=b}return{size:g,base:_,head:p,center:p+g/2}}_calculateBarIndexPixels(t,n){const s=n.scale,i=this.options,o=i.skipNull,r=st(i.maxBarThickness,1/0);let a,l;if(n.grouped){const c=o?this._getStackCount(t):n.stackCount,u=i.barThickness==="flex"?P0(t,n,i,c):E0(t,n,i,c),f=this._getStackIndex(this.index,this._cachedMeta.stack,o?t:void 0);a=u.start+u.chunk*f+u.chunk/2,l=Math.min(r,u.chunk*u.ratio)}else a=s.getPixelForValue(this.getParsed(t)[s.axis],t),l=Math.min(r,n.min*n.ratio);return{base:a-l/2,head:a+l/2,center:a,size:l}}draw(){const t=this._cachedMeta,n=t.vScale,s=t.data,i=s.length;let o=0;for(;o<i;++o)this.getParsed(o)[n.axis]!==null&&!s[o].hidden&&s[o].draw(this._ctx)}}N(Xi,"id","bar"),N(Xi,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),N(Xi,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});class Gi extends _e{initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(t,n,s,i){const o=super.parsePrimitiveData(t,n,s,i);for(let r=0;r<o.length;r++)o[r]._custom=this.resolveDataElementOptions(r+s).radius;return o}parseArrayData(t,n,s,i){const o=super.parseArrayData(t,n,s,i);for(let r=0;r<o.length;r++){const a=n[s+r];o[r]._custom=st(a[2],this.resolveDataElementOptions(r+s).radius)}return o}parseObjectData(t,n,s,i){const o=super.parseObjectData(t,n,s,i);for(let r=0;r<o.length;r++){const a=n[s+r];o[r]._custom=st(a&&a.r&&+a.r,this.resolveDataElementOptions(r+s).radius)}return o}getMaxOverflow(){const t=this._cachedMeta.data;let n=0;for(let s=t.length-1;s>=0;--s)n=Math.max(n,t[s].size(this.resolveDataElementOptions(s))/2);return n>0&&n}getLabelAndValue(t){const n=this._cachedMeta,s=this.chart.data.labels||[],{xScale:i,yScale:o}=n,r=this.getParsed(t),a=i.getLabelForValue(r.x),l=o.getLabelForValue(r.y),c=r._custom;return{label:s[t]||"",value:"("+a+", "+l+(c?", "+c:"")+")"}}update(t){const n=this._cachedMeta.data;this.updateElements(n,0,n.length,t)}updateElements(t,n,s,i){const o=i==="reset",{iScale:r,vScale:a}=this._cachedMeta,{sharedOptions:l,includeOptions:c}=this._getSharedOptions(n,i),u=r.axis,f=a.axis;for(let h=n;h<n+s;h++){const d=t[h],p=!o&&this.getParsed(h),g={},m=g[u]=o?r.getPixelForDecimal(.5):r.getPixelForValue(p[u]),_=g[f]=o?a.getBasePixel():a.getPixelForValue(p[f]);g.skip=isNaN(m)||isNaN(_),c&&(g.options=l||this.resolveDataElementOptions(h,d.active?"active":i),o&&(g.options.radius=0)),this.updateElement(d,h,g,i)}}resolveDataElementOptions(t,n){const s=this.getParsed(t);let i=super.resolveDataElementOptions(t,n);i.$shared&&(i=Object.assign({},i,{$shared:!1}));const o=i.radius;return n!=="active"&&(i.radius=0),i.radius+=st(s&&s._custom,o),i}}N(Gi,"id","bubble"),N(Gi,"defaults",{datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}}),N(Gi,"overrides",{scales:{x:{type:"linear"},y:{type:"linear"}}});function L0(e,t,n){let s=1,i=1,o=0,r=0;if(t<xt){const a=e,l=a+t,c=Math.cos(a),u=Math.sin(a),f=Math.cos(l),h=Math.sin(l),d=(w,v,S)=>li(w,a,l,!0)?1:Math.max(v,v*n,S,S*n),p=(w,v,S)=>li(w,a,l,!0)?-1:Math.min(v,v*n,S,S*n),g=d(0,c,f),m=d(Pt,u,h),_=p(vt,c,f),b=p(vt+Pt,u,h);s=(g-_)/2,i=(m-b)/2,o=-(g+_)/2,r=-(m+b)/2}return{ratioX:s,ratioY:i,offsetX:o,offsetY:r}}class Ln extends _e{constructor(t,n){super(t,n),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,n){const s=this.getDataset().data,i=this._cachedMeta;if(this._parsing===!1)i._parsed=s;else{let o=l=>+s[l];if(ot(s[t])){const{key:l="value"}=this._parsing;o=c=>+bn(s[c],l)}let r,a;for(r=t,a=t+n;r<a;++r)i._parsed[r]=o(r)}}_getRotation(){return me(this.options.rotation-90)}_getCircumference(){return me(this.options.circumference)}_getRotationExtents(){let t=xt,n=-xt;for(let s=0;s<this.chart.data.datasets.length;++s)if(this.chart.isDatasetVisible(s)&&this.chart.getDatasetMeta(s).type===this._type){const i=this.chart.getDatasetMeta(s).controller,o=i._getRotation(),r=i._getCircumference();t=Math.min(t,o),n=Math.max(n,o+r)}return{rotation:t,circumference:n-t}}update(t){const n=this.chart,{chartArea:s}=n,i=this._cachedMeta,o=i.data,r=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,a=Math.max((Math.min(s.width,s.height)-r)/2,0),l=Math.min(Ky(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:u,rotation:f}=this._getRotationExtents(),{ratioX:h,ratioY:d,offsetX:p,offsetY:g}=L0(f,u,l),m=(s.width-r)/h,_=(s.height-r)/d,b=Math.max(Math.min(m,_)/2,0),w=ud(this.options.radius,b),v=Math.max(w*l,0),S=(w-v)/this._getVisibleDatasetWeightTotal();this.offsetX=p*w,this.offsetY=g*w,i.total=this.calculateTotal(),this.outerRadius=w-S*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-S*c,0),this.updateElements(o,0,o.length,t)}_circumference(t,n){const s=this.options,i=this._cachedMeta,o=this._getCircumference();return n&&s.animation.animateRotate||!this.chart.getDataVisibility(t)||i._parsed[t]===null||i.data[t].hidden?0:this.calculateCircumference(i._parsed[t]*o/xt)}updateElements(t,n,s,i){const o=i==="reset",r=this.chart,a=r.chartArea,c=r.options.animation,u=(a.left+a.right)/2,f=(a.top+a.bottom)/2,h=o&&c.animateScale,d=h?0:this.innerRadius,p=h?0:this.outerRadius,{sharedOptions:g,includeOptions:m}=this._getSharedOptions(n,i);let _=this._getRotation(),b;for(b=0;b<n;++b)_+=this._circumference(b,o);for(b=n;b<n+s;++b){const w=this._circumference(b,o),v=t[b],S={x:u+this.offsetX,y:f+this.offsetY,startAngle:_,endAngle:_+w,circumference:w,outerRadius:p,innerRadius:d};m&&(S.options=g||this.resolveDataElementOptions(b,v.active?"active":i)),_+=w,this.updateElement(v,b,S,i)}}calculateTotal(){const t=this._cachedMeta,n=t.data;let s=0,i;for(i=0;i<n.length;i++){const o=t._parsed[i];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(i)&&!n[i].hidden&&(s+=Math.abs(o))}return s}calculateCircumference(t){const n=this._cachedMeta.total;return n>0&&!isNaN(t)?xt*(Math.abs(t)/n):0}getLabelAndValue(t){const n=this._cachedMeta,s=this.chart,i=s.data.labels||[],o=yi(n._parsed[t],s.options.locale);return{label:i[t]||"",value:o}}getMaxBorderWidth(t){let n=0;const s=this.chart;let i,o,r,a,l;if(!t){for(i=0,o=s.data.datasets.length;i<o;++i)if(s.isDatasetVisible(i)){r=s.getDatasetMeta(i),t=r.data,a=r.controller;break}}if(!t)return 0;for(i=0,o=t.length;i<o;++i)l=a.resolveDataElementOptions(i),l.borderAlign!=="inner"&&(n=Math.max(n,l.borderWidth||0,l.hoverBorderWidth||0));return n}getMaxOffset(t){let n=0;for(let s=0,i=t.length;s<i;++s){const o=this.resolveDataElementOptions(s);n=Math.max(n,o.offset||0,o.hoverOffset||0)}return n}_getRingWeightOffset(t){let n=0;for(let s=0;s<t;++s)this.chart.isDatasetVisible(s)&&(n+=this._getRingWeight(s));return n}_getRingWeight(t){return Math.max(st(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}N(Ln,"id","doughnut"),N(Ln,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),N(Ln,"descriptors",{_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")}),N(Ln,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const n=t.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:s,color:i}}=t.legend.options;return n.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:i,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,n,s){s.chart.toggleDataVisibility(n.index),s.chart.update()}}}});class Ji extends _e{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(t){const n=this._cachedMeta,{dataset:s,data:i=[],_dataset:o}=n,r=this.chart._animationsDisabled;let{start:a,count:l}=_d(n,i,r);this._drawStart=a,this._drawCount=l,yd(n)&&(a=0,l=i.length),s._chart=this.chart,s._datasetIndex=this.index,s._decimated=!!o._decimated,s.points=i;const c=this.resolveDatasetElementOptions(t);this.options.showLine||(c.borderWidth=0),c.segment=this.options.segment,this.updateElement(s,void 0,{animated:!r,options:c},t),this.updateElements(i,a,l,t)}updateElements(t,n,s,i){const o=i==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,{sharedOptions:u,includeOptions:f}=this._getSharedOptions(n,i),h=r.axis,d=a.axis,{spanGaps:p,segment:g}=this.options,m=hs(p)?p:Number.POSITIVE_INFINITY,_=this.chart._animationsDisabled||o||i==="none",b=n+s,w=t.length;let v=n>0&&this.getParsed(n-1);for(let S=0;S<w;++S){const A=t[S],k=_?A:{};if(S<n||S>=b){k.skip=!0;continue}const E=this.getParsed(S),C=it(E[d]),F=k[h]=r.getPixelForValue(E[h],S),B=k[d]=o||C?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,E,l):E[d],S);k.skip=isNaN(F)||isNaN(B)||C,k.stop=S>0&&Math.abs(E[h]-v[h])>m,g&&(k.parsed=E,k.raw=c.data[S]),f&&(k.options=u||this.resolveDataElementOptions(S,A.active?"active":i)),_||this.updateElement(A,S,k,i),v=E}}getMaxOverflow(){const t=this._cachedMeta,n=t.dataset,s=n.options&&n.options.borderWidth||0,i=t.data||[];if(!i.length)return s;const o=i[0].size(this.resolveDataElementOptions(0)),r=i[i.length-1].size(this.resolveDataElementOptions(i.length-1));return Math.max(s,o,r)/2}draw(){const t=this._cachedMeta;t.dataset.updateControlPoints(this.chart.chartArea,t.iScale.axis),super.draw()}}N(Ji,"id","line"),N(Ji,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),N(Ji,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});class Gs extends _e{constructor(t,n){super(t,n),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){const n=this._cachedMeta,s=this.chart,i=s.data.labels||[],o=yi(n._parsed[t].r,s.options.locale);return{label:i[t]||"",value:o}}parseObjectData(t,n,s,i){return Pd.bind(this)(t,n,s,i)}update(t){const n=this._cachedMeta.data;this._updateRadius(),this.updateElements(n,0,n.length,t)}getMinMax(){const t=this._cachedMeta,n={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((s,i)=>{const o=this.getParsed(i).r;!isNaN(o)&&this.chart.getDataVisibility(i)&&(o<n.min&&(n.min=o),o>n.max&&(n.max=o))}),n}_updateRadius(){const t=this.chart,n=t.chartArea,s=t.options,i=Math.min(n.right-n.left,n.bottom-n.top),o=Math.max(i/2,0),r=Math.max(s.cutoutPercentage?o/100*s.cutoutPercentage:1,0),a=(o-r)/t.getVisibleDatasetCount();this.outerRadius=o-a*this.index,this.innerRadius=this.outerRadius-a}updateElements(t,n,s,i){const o=i==="reset",r=this.chart,l=r.options.animation,c=this._cachedMeta.rScale,u=c.xCenter,f=c.yCenter,h=c.getIndexAngle(0)-.5*vt;let d=h,p;const g=360/this.countVisibleElements();for(p=0;p<n;++p)d+=this._computeAngle(p,i,g);for(p=n;p<n+s;p++){const m=t[p];let _=d,b=d+this._computeAngle(p,i,g),w=r.getDataVisibility(p)?c.getDistanceFromCenterForValue(this.getParsed(p).r):0;d=b,o&&(l.animateScale&&(w=0),l.animateRotate&&(_=b=h));const v={x:u,y:f,innerRadius:0,outerRadius:w,startAngle:_,endAngle:b,options:this.resolveDataElementOptions(p,m.active?"active":i)};this.updateElement(m,p,v,i)}}countVisibleElements(){const t=this._cachedMeta;let n=0;return t.data.forEach((s,i)=>{!isNaN(this.getParsed(i).r)&&this.chart.getDataVisibility(i)&&n++}),n}_computeAngle(t,n,s){return this.chart.getDataVisibility(t)?me(this.resolveDataElementOptions(t,n).angle||s):0}}N(Gs,"id","polarArea"),N(Gs,"defaults",{dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0}),N(Gs,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const n=t.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:s,color:i}}=t.legend.options;return n.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:i,lineWidth:l.borderWidth,pointStyle:s,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,n,s){s.chart.toggleDataVisibility(n.index),s.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}});class aa extends Ln{}N(aa,"id","pie"),N(aa,"defaults",{cutout:0,rotation:0,circumference:360,radius:"100%"});class Qi extends _e{getLabelAndValue(t){const n=this._cachedMeta.vScale,s=this.getParsed(t);return{label:n.getLabels()[t],value:""+n.getLabelForValue(s[n.axis])}}parseObjectData(t,n,s,i){return Pd.bind(this)(t,n,s,i)}update(t){const n=this._cachedMeta,s=n.dataset,i=n.data||[],o=n.iScale.getLabels();if(s.points=i,t!=="resize"){const r=this.resolveDatasetElementOptions(t);this.options.showLine||(r.borderWidth=0);const a={_loop:!0,_fullLoop:o.length===i.length,options:r};this.updateElement(s,void 0,a,t)}this.updateElements(i,0,i.length,t)}updateElements(t,n,s,i){const o=this._cachedMeta.rScale,r=i==="reset";for(let a=n;a<n+s;a++){const l=t[a],c=this.resolveDataElementOptions(a,l.active?"active":i),u=o.getPointPositionForValue(a,this.getParsed(a).r),f=r?o.xCenter:u.x,h=r?o.yCenter:u.y,d={x:f,y:h,angle:u.angle,skip:isNaN(f)||isNaN(h),options:c};this.updateElement(l,a,d,i)}}}N(Qi,"id","radar"),N(Qi,"defaults",{datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}}),N(Qi,"overrides",{aspectRatio:1,scales:{r:{type:"radialLinear"}}});class Zi extends _e{getLabelAndValue(t){const n=this._cachedMeta,s=this.chart.data.labels||[],{xScale:i,yScale:o}=n,r=this.getParsed(t),a=i.getLabelForValue(r.x),l=o.getLabelForValue(r.y);return{label:s[t]||"",value:"("+a+", "+l+")"}}update(t){const n=this._cachedMeta,{data:s=[]}=n,i=this.chart._animationsDisabled;let{start:o,count:r}=_d(n,s,i);if(this._drawStart=o,this._drawCount=r,yd(n)&&(o=0,r=s.length),this.options.showLine){this.datasetElementType||this.addElements();const{dataset:a,_dataset:l}=n;a._chart=this.chart,a._datasetIndex=this.index,a._decimated=!!l._decimated,a.points=s;const c=this.resolveDatasetElementOptions(t);c.segment=this.options.segment,this.updateElement(a,void 0,{animated:!i,options:c},t)}else this.datasetElementType&&(delete n.dataset,this.datasetElementType=!1);this.updateElements(s,o,r,t)}addElements(){const{showLine:t}=this.options;!this.datasetElementType&&t&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(t,n,s,i){const o=i==="reset",{iScale:r,vScale:a,_stacked:l,_dataset:c}=this._cachedMeta,u=this.resolveDataElementOptions(n,i),f=this.getSharedOptions(u),h=this.includeOptions(i,f),d=r.axis,p=a.axis,{spanGaps:g,segment:m}=this.options,_=hs(g)?g:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||o||i==="none";let w=n>0&&this.getParsed(n-1);for(let v=n;v<n+s;++v){const S=t[v],A=this.getParsed(v),k=b?S:{},E=it(A[p]),C=k[d]=r.getPixelForValue(A[d],v),F=k[p]=o||E?a.getBasePixel():a.getPixelForValue(l?this.applyStack(a,A,l):A[p],v);k.skip=isNaN(C)||isNaN(F)||E,k.stop=v>0&&Math.abs(A[d]-w[d])>_,m&&(k.parsed=A,k.raw=c.data[v]),h&&(k.options=f||this.resolveDataElementOptions(v,S.active?"active":i)),b||this.updateElement(S,v,k,i),w=A}this.updateSharedOptions(f,i,u)}getMaxOverflow(){const t=this._cachedMeta,n=t.data||[];if(!this.options.showLine){let a=0;for(let l=n.length-1;l>=0;--l)a=Math.max(a,n[l].size(this.resolveDataElementOptions(l))/2);return a>0&&a}const s=t.dataset,i=s.options&&s.options.borderWidth||0;if(!n.length)return i;const o=n[0].size(this.resolveDataElementOptions(0)),r=n[n.length-1].size(this.resolveDataElementOptions(n.length-1));return Math.max(i,o,r)/2}}N(Zi,"id","scatter"),N(Zi,"defaults",{datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1}),N(Zi,"overrides",{interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}});var F0=Object.freeze({__proto__:null,BarController:Xi,BubbleController:Gi,DoughnutController:Ln,LineController:Ji,PieController:aa,PolarAreaController:Gs,RadarController:Qi,ScatterController:Zi});function En(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class ll{constructor(t){N(this,"options");this.options=t||{}}static override(t){Object.assign(ll.prototype,t)}init(){}formats(){return En()}parse(){return En()}format(){return En()}add(){return En()}diff(){return En()}startOf(){return En()}endOf(){return En()}}var I0={_date:ll};function N0(e,t,n,s){const{controller:i,data:o,_sorted:r}=e,a=i._cachedMeta.iScale,l=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null;if(a&&t===a.axis&&t!=="r"&&r&&o.length){const c=a._reversePixels?ox:qe;if(s){if(i._sharedOptions){const u=o[0],f=typeof u.getRange=="function"&&u.getRange(t);if(f){const h=c(o,t,n-f),d=c(o,t,n+f);return{lo:h.lo,hi:d.hi}}}}else{const u=c(o,t,n);if(l){const{vScale:f}=i._cachedMeta,{_parsed:h}=e,d=h.slice(0,u.lo+1).reverse().findIndex(g=>!it(g[f.axis]));u.lo-=Math.max(0,d);const p=h.slice(u.hi).findIndex(g=>!it(g[f.axis]));u.hi+=Math.max(0,p)}return u}}return{lo:0,hi:o.length-1}}function Qo(e,t,n,s,i){const o=e.getSortedVisibleDatasetMetas(),r=n[t];for(let a=0,l=o.length;a<l;++a){const{index:c,data:u}=o[a],{lo:f,hi:h}=N0(o[a],t,r,i);for(let d=f;d<=h;++d){const p=u[d];p.skip||s(p,c,d)}}}function B0(e){const t=e.indexOf("x")!==-1,n=e.indexOf("y")!==-1;return function(s,i){const o=t?Math.abs(s.x-i.x):0,r=n?Math.abs(s.y-i.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function Cr(e,t,n,s,i){const o=[];return!i&&!e.isPointInArea(t)||Qo(e,n,t,function(a,l,c){!i&&!Ye(a,e.chartArea,0)||a.inRange(t.x,t.y,s)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function z0(e,t,n,s){let i=[];function o(r,a,l){const{startAngle:c,endAngle:u}=r.getProps(["startAngle","endAngle"],s),{angle:f}=dd(r,{x:t.x,y:t.y});li(f,c,u)&&i.push({element:r,datasetIndex:a,index:l})}return Qo(e,n,t,o),i}function H0(e,t,n,s,i,o){let r=[];const a=B0(n);let l=Number.POSITIVE_INFINITY;function c(u,f,h){const d=u.inRange(t.x,t.y,i);if(s&&!d)return;const p=u.getCenterPoint(i);if(!(!!o||e.isPointInArea(p))&&!d)return;const m=a(t,p);m<l?(r=[{element:u,datasetIndex:f,index:h}],l=m):m===l&&r.push({element:u,datasetIndex:f,index:h})}return Qo(e,n,t,c),r}function Er(e,t,n,s,i,o){return!o&&!e.isPointInArea(t)?[]:n==="r"&&!s?z0(e,t,n,i):H0(e,t,n,s,i,o)}function lu(e,t,n,s,i){const o=[],r=n==="x"?"inXRange":"inYRange";let a=!1;return Qo(e,n,t,(l,c,u)=>{l[r]&&l[r](t[n],i)&&(o.push({element:l,datasetIndex:c,index:u}),a=a||l.inRange(t.x,t.y,i))}),s&&!a?[]:o}var V0={modes:{index(e,t,n,s){const i=An(t,e),o=n.axis||"x",r=n.includeInvisible||!1,a=n.intersect?Cr(e,i,o,s,r):Er(e,i,o,!1,s,r),l=[];return a.length?(e.getSortedVisibleDatasetMetas().forEach(c=>{const u=a[0].index,f=c.data[u];f&&!f.skip&&l.push({element:f,datasetIndex:c.index,index:u})}),l):[]},dataset(e,t,n,s){const i=An(t,e),o=n.axis||"xy",r=n.includeInvisible||!1;let a=n.intersect?Cr(e,i,o,s,r):Er(e,i,o,!1,s,r);if(a.length>0){const l=a[0].datasetIndex,c=e.getDatasetMeta(l).data;a=[];for(let u=0;u<c.length;++u)a.push({element:c[u],datasetIndex:l,index:u})}return a},point(e,t,n,s){const i=An(t,e),o=n.axis||"xy",r=n.includeInvisible||!1;return Cr(e,i,o,s,r)},nearest(e,t,n,s){const i=An(t,e),o=n.axis||"xy",r=n.includeInvisible||!1;return Er(e,i,o,n.intersect,s,r)},x(e,t,n,s){const i=An(t,e);return lu(e,i,"x",n.intersect,s)},y(e,t,n,s){const i=An(t,e);return lu(e,i,"y",n.intersect,s)}}};const Bd=["left","top","right","bottom"];function Cs(e,t){return e.filter(n=>n.pos===t)}function cu(e,t){return e.filter(n=>Bd.indexOf(n.pos)===-1&&n.box.axis===t)}function Es(e,t){return e.sort((n,s)=>{const i=t?s:n,o=t?n:s;return i.weight===o.weight?i.index-o.index:i.weight-o.weight})}function j0(e){const t=[];let n,s,i,o,r,a;for(n=0,s=(e||[]).length;n<s;++n)i=e[n],{position:o,options:{stack:r,stackWeight:a=1}}=i,t.push({index:n,box:i,pos:o,horizontal:i.isHorizontal(),weight:i.weight,stack:r&&o+r,stackWeight:a});return t}function W0(e){const t={};for(const n of e){const{stack:s,pos:i,stackWeight:o}=n;if(!s||!Bd.includes(i))continue;const r=t[s]||(t[s]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function $0(e,t){const n=W0(e),{vBoxMaxWidth:s,hBoxMaxHeight:i}=t;let o,r,a;for(o=0,r=e.length;o<r;++o){a=e[o];const{fullSize:l}=a.box,c=n[a.stack],u=c&&a.stackWeight/c.weight;a.horizontal?(a.width=u?u*s:l&&t.availableWidth,a.height=i):(a.width=s,a.height=u?u*i:l&&t.availableHeight)}return n}function U0(e){const t=j0(e),n=Es(t.filter(c=>c.box.fullSize),!0),s=Es(Cs(t,"left"),!0),i=Es(Cs(t,"right")),o=Es(Cs(t,"top"),!0),r=Es(Cs(t,"bottom")),a=cu(t,"x"),l=cu(t,"y");return{fullSize:n,leftAndTop:s.concat(o),rightAndBottom:i.concat(l).concat(r).concat(a),chartArea:Cs(t,"chartArea"),vertical:s.concat(i).concat(l),horizontal:o.concat(r).concat(a)}}function uu(e,t,n,s){return Math.max(e[n],t[n])+Math.max(e[s],t[s])}function zd(e,t){e.top=Math.max(e.top,t.top),e.left=Math.max(e.left,t.left),e.bottom=Math.max(e.bottom,t.bottom),e.right=Math.max(e.right,t.right)}function K0(e,t,n,s){const{pos:i,box:o}=n,r=e.maxPadding;if(!ot(i)){n.size&&(e[i]-=n.size);const f=s[n.stack]||{size:0,count:1};f.size=Math.max(f.size,n.horizontal?o.height:o.width),n.size=f.size/f.count,e[i]+=n.size}o.getPadding&&zd(r,o.getPadding());const a=Math.max(0,t.outerWidth-uu(r,e,"left","right")),l=Math.max(0,t.outerHeight-uu(r,e,"top","bottom")),c=a!==e.w,u=l!==e.h;return e.w=a,e.h=l,n.horizontal?{same:c,other:u}:{same:u,other:c}}function q0(e){const t=e.maxPadding;function n(s){const i=Math.max(t[s]-e[s],0);return e[s]+=i,i}e.y+=n("top"),e.x+=n("left"),n("right"),n("bottom")}function Y0(e,t){const n=t.maxPadding;function s(i){const o={left:0,top:0,right:0,bottom:0};return i.forEach(r=>{o[r]=Math.max(t[r],n[r])}),o}return s(e?["left","right"]:["top","bottom"])}function Ds(e,t,n,s){const i=[];let o,r,a,l,c,u;for(o=0,r=e.length,c=0;o<r;++o){a=e[o],l=a.box,l.update(a.width||t.w,a.height||t.h,Y0(a.horizontal,t));const{same:f,other:h}=K0(t,n,a,s);c|=f&&i.length,u=u||h,l.fullSize||i.push(a)}return c&&Ds(i,t,n,s)||u}function Di(e,t,n,s,i){e.top=n,e.left=t,e.right=t+s,e.bottom=n+i,e.width=s,e.height=i}function fu(e,t,n,s){const i=n.padding;let{x:o,y:r}=t;for(const a of e){const l=a.box,c=s[a.stack]||{placed:0,weight:1},u=a.stackWeight/c.weight||1;if(a.horizontal){const f=t.w*u,h=c.size||l.height;ai(c.start)&&(r=c.start),l.fullSize?Di(l,i.left,r,n.outerWidth-i.right-i.left,h):Di(l,t.left+c.placed,r,f,h),c.start=r,c.placed+=f,r=l.bottom}else{const f=t.h*u,h=c.size||l.width;ai(c.start)&&(o=c.start),l.fullSize?Di(l,o,i.top,h,n.outerHeight-i.bottom-i.top):Di(l,o,t.top+c.placed,h,f),c.start=o,c.placed+=f,o=l.right}}t.x=o,t.y=r}var Wt={addBox(e,t){e.boxes||(e.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(n){t.draw(n)}}]},e.boxes.push(t)},removeBox(e,t){const n=e.boxes?e.boxes.indexOf(t):-1;n!==-1&&e.boxes.splice(n,1)},configure(e,t,n){t.fullSize=n.fullSize,t.position=n.position,t.weight=n.weight},update(e,t,n,s){if(!e)return;const i=$t(e.options.layout.padding),o=Math.max(t-i.width,0),r=Math.max(n-i.height,0),a=U0(e.boxes),l=a.vertical,c=a.horizontal;ht(e.boxes,g=>{typeof g.beforeLayout=="function"&&g.beforeLayout()});const u=l.reduce((g,m)=>m.box.options&&m.box.options.display===!1?g:g+1,0)||1,f=Object.freeze({outerWidth:t,outerHeight:n,padding:i,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/u,hBoxMaxHeight:r/2}),h=Object.assign({},i);zd(h,$t(s));const d=Object.assign({maxPadding:h,w:o,h:r,x:i.left,y:i.top},i),p=$0(l.concat(c),f);Ds(a.fullSize,d,f,p),Ds(l,d,f,p),Ds(c,d,f,p)&&Ds(l,d,f,p),q0(d),fu(a.leftAndTop,d,f,p),d.x+=d.w,d.y+=d.h,fu(a.rightAndBottom,d,f,p),e.chartArea={left:d.left,top:d.top,right:d.left+d.w,bottom:d.top+d.h,height:d.h,width:d.w},ht(a.chartArea,g=>{const m=g.box;Object.assign(m,e.chartArea),m.update(d.w,d.h,{left:0,top:0,right:0,bottom:0})})}};class Hd{acquireContext(t,n){}releaseContext(t){return!1}addEventListener(t,n,s){}removeEventListener(t,n,s){}getDevicePixelRatio(){return 1}getMaximumSize(t,n,s,i){return n=Math.max(0,n||t.width),s=s||t.height,{width:n,height:Math.max(0,i?Math.floor(n/i):s)}}isAttached(t){return!0}updateConfig(t){}}class X0 extends Hd{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const to="$chartjs",G0={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},hu=e=>e===null||e==="";function J0(e,t){const n=e.style,s=e.getAttribute("height"),i=e.getAttribute("width");if(e[to]={initial:{height:s,width:i,style:{display:n.display,height:n.height,width:n.width}}},n.display=n.display||"block",n.boxSizing=n.boxSizing||"border-box",hu(i)){const o=Xc(e,"width");o!==void 0&&(e.width=o)}if(hu(s))if(e.style.height==="")e.height=e.width/(t||2);else{const o=Xc(e,"height");o!==void 0&&(e.height=o)}return e}const Vd=Qx?{passive:!0}:!1;function Q0(e,t,n){e&&e.addEventListener(t,n,Vd)}function Z0(e,t,n){e&&e.canvas&&e.canvas.removeEventListener(t,n,Vd)}function tv(e,t){const n=G0[e.type]||e.type,{x:s,y:i}=An(e,t);return{type:n,chart:t,native:e,x:s!==void 0?s:null,y:i!==void 0?i:null}}function Mo(e,t){for(const n of e)if(n===t||n.contains(t))return!0}function ev(e,t,n){const s=e.canvas,i=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Mo(a.addedNodes,s),r=r&&!Mo(a.removedNodes,s);r&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}function nv(e,t,n){const s=e.canvas,i=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Mo(a.removedNodes,s),r=r&&!Mo(a.addedNodes,s);r&&n()});return i.observe(document,{childList:!0,subtree:!0}),i}const ui=new Map;let du=0;function jd(){const e=window.devicePixelRatio;e!==du&&(du=e,ui.forEach((t,n)=>{n.currentDevicePixelRatio!==e&&t()}))}function sv(e,t){ui.size||window.addEventListener("resize",jd),ui.set(e,t)}function iv(e){ui.delete(e),ui.size||window.removeEventListener("resize",jd)}function ov(e,t,n){const s=e.canvas,i=s&&al(s);if(!i)return;const o=bd((a,l)=>{const c=i.clientWidth;n(a,l),c<i.clientWidth&&n()},window),r=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,u=l.contentRect.height;c===0&&u===0||o(c,u)});return r.observe(i),sv(e,o),r}function Pr(e,t,n){n&&n.disconnect(),t==="resize"&&iv(e)}function rv(e,t,n){const s=e.canvas,i=bd(o=>{e.ctx!==null&&n(tv(o,e))},e);return Q0(s,t,i),i}class av extends Hd{acquireContext(t,n){const s=t&&t.getContext&&t.getContext("2d");return s&&s.canvas===t?(J0(t,n),s):null}releaseContext(t){const n=t.canvas;if(!n[to])return!1;const s=n[to].initial;["height","width"].forEach(o=>{const r=s[o];it(r)?n.removeAttribute(o):n.setAttribute(o,r)});const i=s.style||{};return Object.keys(i).forEach(o=>{n.style[o]=i[o]}),n.width=n.width,delete n[to],!0}addEventListener(t,n,s){this.removeEventListener(t,n);const i=t.$proxies||(t.$proxies={}),r={attach:ev,detach:nv,resize:ov}[n]||rv;i[n]=r(t,n,s)}removeEventListener(t,n){const s=t.$proxies||(t.$proxies={}),i=s[n];if(!i)return;({attach:Pr,detach:Pr,resize:Pr}[n]||Z0)(t,n,i),s[n]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,n,s,i){return Jx(t,n,s,i)}isAttached(t){const n=t&&al(t);return!!(n&&n.isConnected)}}function lv(e){return!rl()||typeof OffscreenCanvas<"u"&&e instanceof OffscreenCanvas?X0:av}var Hi;let Qe=(Hi=class{constructor(){N(this,"x");N(this,"y");N(this,"active",!1);N(this,"options");N(this,"$animations")}tooltipPosition(t){const{x:n,y:s}=this.getProps(["x","y"],t);return{x:n,y:s}}hasValue(){return hs(this.x)&&hs(this.y)}getProps(t,n){const s=this.$animations;if(!n||!s)return this;const i={};return t.forEach(o=>{i[o]=s[o]&&s[o].active()?s[o]._to:this[o]}),i}},N(Hi,"defaults",{}),N(Hi,"defaultRoutes"),Hi);function cv(e,t){const n=e.options.ticks,s=uv(e),i=Math.min(n.maxTicksLimit||s,s),o=n.major.enabled?hv(t):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>i)return dv(t,c,o,r/i),c;const u=fv(o,t,i);if(r>0){let f,h;const d=r>1?Math.round((l-a)/(r-1)):null;for(Li(t,c,u,it(d)?0:a-d,a),f=0,h=r-1;f<h;f++)Li(t,c,u,o[f],o[f+1]);return Li(t,c,u,l,it(d)?t.length:l+d),c}return Li(t,c,u),c}function uv(e){const t=e.options.offset,n=e._tickSize(),s=e._length/n+(t?0:1),i=e._maxLength/n;return Math.floor(Math.min(s,i))}function fv(e,t,n){const s=pv(e),i=t.length/n;if(!s)return Math.max(i,1);const o=tx(s);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>i)return l}return Math.max(i,1)}function hv(e){const t=[];let n,s;for(n=0,s=e.length;n<s;n++)e[n].major&&t.push(n);return t}function dv(e,t,n,s){let i=0,o=n[0],r;for(s=Math.ceil(s),r=0;r<e.length;r++)r===o&&(t.push(e[r]),i++,o=n[i*s])}function Li(e,t,n,s,i){const o=st(s,0),r=Math.min(st(i,e.length),e.length);let a=0,l,c,u;for(n=Math.ceil(n),i&&(l=i-s,n=l/Math.floor(l/n)),u=o;u<0;)a++,u=Math.round(o+a*n);for(c=Math.max(o,0);c<r;c++)c===u&&(t.push(e[c]),a++,u=Math.round(o+a*n))}function pv(e){const t=e.length;let n,s;if(t<2)return!1;for(s=e[0],n=1;n<t;++n)if(e[n]-e[n-1]!==s)return!1;return s}const gv=e=>e==="left"?"right":e==="right"?"left":e,pu=(e,t,n)=>t==="top"||t==="left"?e[t]+n:e[t]-n,gu=(e,t)=>Math.min(t||e,e);function mu(e,t){const n=[],s=e.length/t,i=e.length;let o=0;for(;o<i;o+=s)n.push(e[Math.floor(o)]);return n}function mv(e,t,n){const s=e.ticks.length,i=Math.min(t,s-1),o=e._startPixel,r=e._endPixel,a=1e-6;let l=e.getPixelForTick(i),c;if(!(n&&(s===1?c=Math.max(l-o,r-l):t===0?c=(e.getPixelForTick(1)-l)/2:c=(l-e.getPixelForTick(i-1))/2,l+=i<t?c:-c,l<o-a||l>r+a)))return l}function bv(e,t){ht(e,n=>{const s=n.gc,i=s.length/2;let o;if(i>t){for(o=0;o<i;++o)delete n.data[s[o]];s.splice(0,i)}})}function Ps(e){return e.drawTicks?e.tickLength:0}function bu(e,t){if(!e.display)return 0;const n=Dt(e.font,t),s=$t(e.padding);return(wt(e.text)?e.text.length:1)*n.lineHeight+s.height}function _v(e,t){return xn(e,{scale:t,type:"scale"})}function yv(e,t,n){return xn(e,{tick:n,index:t,type:"tick"})}function xv(e,t,n){let s=tl(e);return(n&&t!=="right"||!n&&t==="right")&&(s=gv(s)),s}function vv(e,t,n,s){const{top:i,left:o,bottom:r,right:a,chart:l}=e,{chartArea:c,scales:u}=l;let f=0,h,d,p;const g=r-i,m=a-o;if(e.isHorizontal()){if(d=Bt(s,o,a),ot(n)){const _=Object.keys(n)[0],b=n[_];p=u[_].getPixelForValue(b)+g-t}else n==="center"?p=(c.bottom+c.top)/2+g-t:p=pu(e,n,t);h=a-o}else{if(ot(n)){const _=Object.keys(n)[0],b=n[_];d=u[_].getPixelForValue(b)-m+t}else n==="center"?d=(c.left+c.right)/2-m+t:d=pu(e,n,t);p=Bt(s,r,i),f=n==="left"?-Pt:Pt}return{titleX:d,titleY:p,maxWidth:h,rotation:f}}class Un extends Qe{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,n){return t}getUserBounds(){let{_userMin:t,_userMax:n,_suggestedMin:s,_suggestedMax:i}=this;return t=ie(t,Number.POSITIVE_INFINITY),n=ie(n,Number.NEGATIVE_INFINITY),s=ie(s,Number.POSITIVE_INFINITY),i=ie(i,Number.NEGATIVE_INFINITY),{min:ie(t,s),max:ie(n,i),minDefined:Ct(t),maxDefined:Ct(n)}}getMinMax(t){let{min:n,max:s,minDefined:i,maxDefined:o}=this.getUserBounds(),r;if(i&&o)return{min:n,max:s};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,t),i||(n=Math.min(n,r.min)),o||(s=Math.max(s,r.max));return n=o&&n>s?s:n,s=i&&n>s?n:s,{min:ie(n,ie(s,n)),max:ie(s,ie(n,s))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){yt(this.options.beforeUpdate,[this])}update(t,n,s){const{beginAtZero:i,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=n,this._margins=s=Object.assign({left:0,right:0,top:0,bottom:0},s),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+s.left+s.right:this.height+s.top+s.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=Ax(this,o,i),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?mu(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=cv(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,n,s;this.isHorizontal()?(n=this.left,s=this.right):(n=this.top,s=this.bottom,t=!t),this._startPixel=n,this._endPixel=s,this._reversePixels=t,this._length=s-n,this._alignToPixels=this.options.alignToPixels}afterUpdate(){yt(this.options.afterUpdate,[this])}beforeSetDimensions(){yt(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){yt(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),yt(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){yt(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const n=this.options.ticks;let s,i,o;for(s=0,i=t.length;s<i;s++)o=t[s],o.label=yt(n.callback,[o.value,s,t],this)}afterTickToLabelConversion(){yt(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){yt(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,n=t.ticks,s=gu(this.ticks.length,t.ticks.maxTicksLimit),i=n.minRotation||0,o=n.maxRotation;let r=i,a,l,c;if(!this._isVisible()||!n.display||i>=o||s<=1||!this.isHorizontal()){this.labelRotation=i;return}const u=this._getLabelSizes(),f=u.widest.width,h=u.highest.height,d=It(this.chart.width-f,0,this.maxWidth);a=t.offset?this.maxWidth/s:d/(s-1),f+6>a&&(a=d/(s-(t.offset?.5:1)),l=this.maxHeight-Ps(t.grid)-n.padding-bu(t.title,this.chart.options.font),c=Math.sqrt(f*f+h*h),r=Qa(Math.min(Math.asin(It((u.highest.height+6)/a,-1,1)),Math.asin(It(l/c,-1,1))-Math.asin(It(h/c,-1,1)))),r=Math.max(i,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){yt(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){yt(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:n,options:{ticks:s,title:i,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=bu(i,n.options.font);if(a?(t.width=this.maxWidth,t.height=Ps(o)+l):(t.height=this.maxHeight,t.width=Ps(o)+l),s.display&&this.ticks.length){const{first:c,last:u,widest:f,highest:h}=this._getLabelSizes(),d=s.padding*2,p=me(this.labelRotation),g=Math.cos(p),m=Math.sin(p);if(a){const _=s.mirror?0:m*f.width+g*h.height;t.height=Math.min(this.maxHeight,t.height+_+d)}else{const _=s.mirror?0:g*f.width+m*h.height;t.width=Math.min(this.maxWidth,t.width+_+d)}this._calculatePadding(c,u,m,g)}}this._handleMargins(),a?(this.width=this._length=n.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=n.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,n,s,i){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const u=this.getPixelForTick(0)-this.left,f=this.right-this.getPixelForTick(this.ticks.length-1);let h=0,d=0;l?c?(h=i*t.width,d=s*n.height):(h=s*t.height,d=i*n.width):o==="start"?d=n.width:o==="end"?h=t.width:o!=="inner"&&(h=t.width/2,d=n.width/2),this.paddingLeft=Math.max((h-u+r)*this.width/(this.width-u),0),this.paddingRight=Math.max((d-f+r)*this.width/(this.width-f),0)}else{let u=n.height/2,f=t.height/2;o==="start"?(u=0,f=t.height):o==="end"&&(u=n.height,f=0),this.paddingTop=u+r,this.paddingBottom=f+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){yt(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:n}=this.options;return n==="top"||n==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let n,s;for(n=0,s=t.length;n<s;n++)it(t[n].label)&&(t.splice(n,1),s--,n--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const n=this.options.ticks.sampleSize;let s=this.ticks;n<s.length&&(s=mu(s,n)),this._labelSizes=t=this._computeLabelSizes(s,s.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,n,s){const{ctx:i,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(n/gu(n,s));let c=0,u=0,f,h,d,p,g,m,_,b,w,v,S;for(f=0;f<n;f+=l){if(p=t[f].label,g=this._resolveTickFontOptions(f),i.font=m=g.string,_=o[m]=o[m]||{data:{},gc:[]},b=g.lineHeight,w=v=0,!it(p)&&!wt(p))w=wo(i,_.data,_.gc,w,p),v=b;else if(wt(p))for(h=0,d=p.length;h<d;++h)S=p[h],!it(S)&&!wt(S)&&(w=wo(i,_.data,_.gc,w,S),v+=b);r.push(w),a.push(v),c=Math.max(w,c),u=Math.max(v,u)}bv(o,n);const A=r.indexOf(c),k=a.indexOf(u),E=C=>({width:r[C]||0,height:a[C]||0});return{first:E(0),last:E(n-1),widest:E(A),highest:E(k),widths:r,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,n){return NaN}getValueForPixel(t){}getPixelForTick(t){const n=this.ticks;return t<0||t>n.length-1?null:this.getPixelForValue(n[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const n=this._startPixel+t*this._length;return ix(this._alignToPixels?Cn(this.chart,n,0):n)}getDecimalForPixel(t){const n=(t-this._startPixel)/this._length;return this._reversePixels?1-n:n}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:n}=this;return t<0&&n<0?n:t>0&&n>0?t:0}getContext(t){const n=this.ticks||[];if(t>=0&&t<n.length){const s=n[t];return s.$context||(s.$context=yv(this.getContext(),t,s))}return this.$context||(this.$context=_v(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,n=me(this.labelRotation),s=Math.abs(Math.cos(n)),i=Math.abs(Math.sin(n)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*s>a*i?a/s:l/i:l*i<a*s?l/s:a/i}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const n=this.axis,s=this.chart,i=this.options,{grid:o,position:r,border:a}=i,l=o.offset,c=this.isHorizontal(),f=this.ticks.length+(l?1:0),h=Ps(o),d=[],p=a.setContext(this.getContext()),g=p.display?p.width:0,m=g/2,_=function(G){return Cn(s,G,g)};let b,w,v,S,A,k,E,C,F,B,L,X;if(r==="top")b=_(this.bottom),k=this.bottom-h,C=b-m,B=_(t.top)+m,X=t.bottom;else if(r==="bottom")b=_(this.top),B=t.top,X=_(t.bottom)-m,k=b+m,C=this.top+h;else if(r==="left")b=_(this.right),A=this.right-h,E=b-m,F=_(t.left)+m,L=t.right;else if(r==="right")b=_(this.left),F=t.left,L=_(t.right)-m,A=b+m,E=this.left+h;else if(n==="x"){if(r==="center")b=_((t.top+t.bottom)/2+.5);else if(ot(r)){const G=Object.keys(r)[0],U=r[G];b=_(this.chart.scales[G].getPixelForValue(U))}B=t.top,X=t.bottom,k=b+m,C=k+h}else if(n==="y"){if(r==="center")b=_((t.left+t.right)/2);else if(ot(r)){const G=Object.keys(r)[0],U=r[G];b=_(this.chart.scales[G].getPixelForValue(U))}A=b-m,E=A-h,F=t.left,L=t.right}const rt=st(i.ticks.maxTicksLimit,f),Z=Math.max(1,Math.ceil(f/rt));for(w=0;w<f;w+=Z){const G=this.getContext(w),U=o.setContext(G),et=a.setContext(G),mt=U.lineWidth,Ut=U.color,Kt=et.dash||[],Et=et.dashOffset,ae=U.tickWidth,Xt=U.tickColor,Me=U.tickBorderDash||[],At=U.tickBorderDashOffset;v=mv(this,w,l),v!==void 0&&(S=Cn(s,v,mt),c?A=E=F=L=S:k=C=B=X=S,d.push({tx1:A,ty1:k,tx2:E,ty2:C,x1:F,y1:B,x2:L,y2:X,width:mt,color:Ut,borderDash:Kt,borderDashOffset:Et,tickWidth:ae,tickColor:Xt,tickBorderDash:Me,tickBorderDashOffset:At}))}return this._ticksLength=f,this._borderValue=b,d}_computeLabelItems(t){const n=this.axis,s=this.options,{position:i,ticks:o}=s,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:u,mirror:f}=o,h=Ps(s.grid),d=h+u,p=f?-u:d,g=-me(this.labelRotation),m=[];let _,b,w,v,S,A,k,E,C,F,B,L,X="middle";if(i==="top")A=this.bottom-p,k=this._getXAxisLabelAlignment();else if(i==="bottom")A=this.top+p,k=this._getXAxisLabelAlignment();else if(i==="left"){const Z=this._getYAxisLabelAlignment(h);k=Z.textAlign,S=Z.x}else if(i==="right"){const Z=this._getYAxisLabelAlignment(h);k=Z.textAlign,S=Z.x}else if(n==="x"){if(i==="center")A=(t.top+t.bottom)/2+d;else if(ot(i)){const Z=Object.keys(i)[0],G=i[Z];A=this.chart.scales[Z].getPixelForValue(G)+d}k=this._getXAxisLabelAlignment()}else if(n==="y"){if(i==="center")S=(t.left+t.right)/2-d;else if(ot(i)){const Z=Object.keys(i)[0],G=i[Z];S=this.chart.scales[Z].getPixelForValue(G)}k=this._getYAxisLabelAlignment(h).textAlign}n==="y"&&(l==="start"?X="top":l==="end"&&(X="bottom"));const rt=this._getLabelSizes();for(_=0,b=a.length;_<b;++_){w=a[_],v=w.label;const Z=o.setContext(this.getContext(_));E=this.getPixelForTick(_)+o.labelOffset,C=this._resolveTickFontOptions(_),F=C.lineHeight,B=wt(v)?v.length:1;const G=B/2,U=Z.color,et=Z.textStrokeColor,mt=Z.textStrokeWidth;let Ut=k;r?(S=E,k==="inner"&&(_===b-1?Ut=this.options.reverse?"left":"right":_===0?Ut=this.options.reverse?"right":"left":Ut="center"),i==="top"?c==="near"||g!==0?L=-B*F+F/2:c==="center"?L=-rt.highest.height/2-G*F+F:L=-rt.highest.height+F/2:c==="near"||g!==0?L=F/2:c==="center"?L=rt.highest.height/2-G*F:L=rt.highest.height-B*F,f&&(L*=-1),g!==0&&!Z.showLabelBackdrop&&(S+=F/2*Math.sin(g))):(A=E,L=(1-B)*F/2);let Kt;if(Z.showLabelBackdrop){const Et=$t(Z.backdropPadding),ae=rt.heights[_],Xt=rt.widths[_];let Me=L-Et.top,At=0-Et.left;switch(X){case"middle":Me-=ae/2;break;case"bottom":Me-=ae;break}switch(k){case"center":At-=Xt/2;break;case"right":At-=Xt;break;case"inner":_===b-1?At-=Xt:_>0&&(At-=Xt/2);break}Kt={left:At,top:Me,width:Xt+Et.width,height:ae+Et.height,color:Z.backdropColor}}m.push({label:v,font:C,textOffset:L,options:{rotation:g,color:U,strokeColor:et,strokeWidth:mt,textAlign:Ut,textBaseline:X,translation:[S,A],backdrop:Kt}})}return m}_getXAxisLabelAlignment(){const{position:t,ticks:n}=this.options;if(-me(this.labelRotation))return t==="top"?"left":"right";let i="center";return n.align==="start"?i="left":n.align==="end"?i="right":n.align==="inner"&&(i="inner"),i}_getYAxisLabelAlignment(t){const{position:n,ticks:{crossAlign:s,mirror:i,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width;let c,u;return n==="left"?i?(u=this.right+o,s==="near"?c="left":s==="center"?(c="center",u+=l/2):(c="right",u+=l)):(u=this.right-a,s==="near"?c="right":s==="center"?(c="center",u-=l/2):(c="left",u=this.left)):n==="right"?i?(u=this.left+o,s==="near"?c="right":s==="center"?(c="center",u-=l/2):(c="left",u-=l)):(u=this.left+a,s==="near"?c="left":s==="center"?(c="center",u+=l/2):(c="right",u=this.right)):c="right",{textAlign:c,x:u}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,n=this.options.position;if(n==="left"||n==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(n==="top"||n==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:n},left:s,top:i,width:o,height:r}=this;n&&(t.save(),t.fillStyle=n,t.fillRect(s,i,o,r),t.restore())}getLineWidthForValue(t){const n=this.options.grid;if(!this._isVisible()||!n.display)return 0;const i=this.ticks.findIndex(o=>o.value===t);return i>=0?n.setContext(this.getContext(i)).lineWidth:0}drawGrid(t){const n=this.options.grid,s=this.ctx,i=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,r;const a=(l,c,u)=>{!u.width||!u.color||(s.save(),s.lineWidth=u.width,s.strokeStyle=u.color,s.setLineDash(u.borderDash||[]),s.lineDashOffset=u.borderDashOffset,s.beginPath(),s.moveTo(l.x,l.y),s.lineTo(c.x,c.y),s.stroke(),s.restore())};if(n.display)for(o=0,r=i.length;o<r;++o){const l=i[o];n.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),n.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:n,options:{border:s,grid:i}}=this,o=s.setContext(this.getContext()),r=s.display?o.width:0;if(!r)return;const a=i.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,u,f,h;this.isHorizontal()?(c=Cn(t,this.left,r)-r/2,u=Cn(t,this.right,a)+a/2,f=h=l):(f=Cn(t,this.top,r)-r/2,h=Cn(t,this.bottom,a)+a/2,c=u=l),n.save(),n.lineWidth=o.width,n.strokeStyle=o.color,n.beginPath(),n.moveTo(c,f),n.lineTo(u,h),n.stroke(),n.restore()}drawLabels(t){if(!this.options.ticks.display)return;const s=this.ctx,i=this._computeLabelArea();i&&Xo(s,i);const o=this.getLabelItems(t);for(const r of o){const a=r.options,l=r.font,c=r.label,u=r.textOffset;$n(s,c,0,u,l,a)}i&&Go(s)}drawTitle(){const{ctx:t,options:{position:n,title:s,reverse:i}}=this;if(!s.display)return;const o=Dt(s.font),r=$t(s.padding),a=s.align;let l=o.lineHeight/2;n==="bottom"||n==="center"||ot(n)?(l+=r.bottom,wt(s.text)&&(l+=o.lineHeight*(s.text.length-1))):l+=r.top;const{titleX:c,titleY:u,maxWidth:f,rotation:h}=vv(this,l,n,a);$n(t,s.text,0,0,o,{color:s.color,maxWidth:f,rotation:h,textAlign:xv(a,n,i),textBaseline:"middle",translation:[c,u]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,n=t.ticks&&t.ticks.z||0,s=st(t.grid&&t.grid.z,-1),i=st(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==Un.prototype.draw?[{z:n,draw:o=>{this.draw(o)}}]:[{z:s,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:i,draw:()=>{this.drawBorder()}},{z:n,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const n=this.chart.getSortedVisibleDatasetMetas(),s=this.axis+"AxisID",i=[];let o,r;for(o=0,r=n.length;o<r;++o){const a=n[o];a[s]===this.id&&(!t||a.type===t)&&i.push(a)}return i}_resolveTickFontOptions(t){const n=this.options.ticks.setContext(this.getContext(t));return Dt(n.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class Fi{constructor(t,n,s){this.type=t,this.scope=n,this.override=s,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const n=Object.getPrototypeOf(t);let s;Mv(n)&&(s=this.register(n));const i=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in i||(i[o]=t,wv(t,r,s),this.override&&St.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){const n=this.items,s=t.id,i=this.scope;s in n&&delete n[s],i&&s in St[i]&&(delete St[i][s],this.override&&delete Wn[s])}}function wv(e,t,n){const s=ri(Object.create(null),[n?St.get(n):{},St.get(t),e.defaults]);St.set(t,s),e.defaultRoutes&&Sv(t,e.defaultRoutes),e.descriptors&&St.describe(t,e.descriptors)}function Sv(e,t){Object.keys(t).forEach(n=>{const s=n.split("."),i=s.pop(),o=[e].concat(s).join("."),r=t[n].split("."),a=r.pop(),l=r.join(".");St.route(o,i,l,a)})}function Mv(e){return"id"in e&&"defaults"in e}class Cv{constructor(){this.controllers=new Fi(_e,"datasets",!0),this.elements=new Fi(Qe,"elements"),this.plugins=new Fi(Object,"plugins"),this.scales=new Fi(Un,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,n,s){[...n].forEach(i=>{const o=s||this._getRegistryForType(i);s||o.isForType(i)||o===this.plugins&&i.id?this._exec(t,o,i):ht(i,r=>{const a=s||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,n,s){const i=Ja(t);yt(s["before"+i],[],s),n[t](s),yt(s["after"+i],[],s)}_getRegistryForType(t){for(let n=0;n<this._typedRegistries.length;n++){const s=this._typedRegistries[n];if(s.isForType(t))return s}return this.plugins}_get(t,n,s){const i=n.get(t);if(i===void 0)throw new Error('"'+t+'" is not a registered '+s+".");return i}}var Te=new Cv;class Ev{constructor(){this._init=[]}notify(t,n,s,i){n==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=i?this._descriptors(t).filter(i):this._descriptors(t),r=this._notify(o,t,n,s);return n==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,n,s,i){i=i||{};for(const o of t){const r=o.plugin,a=r[s],l=[n,i,o.options];if(yt(a,l,r)===!1&&i.cancelable)return!1}return!0}invalidate(){it(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const n=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),n}_createDescriptors(t,n){const s=t&&t.config,i=st(s.options&&s.options.plugins,{}),o=Pv(s);return i===!1&&!n?[]:Av(t,o,i,n)}_notifyStateChanges(t){const n=this._oldCache||[],s=this._cache,i=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(i(n,s),t,"stop"),this._notify(i(s,n),t,"start")}}function Pv(e){const t={},n=[],s=Object.keys(Te.plugins.items);for(let o=0;o<s.length;o++)n.push(Te.getPlugin(s[o]));const i=e.plugins||[];for(let o=0;o<i.length;o++){const r=i[o];n.indexOf(r)===-1&&(n.push(r),t[r.id]=!0)}return{plugins:n,localIds:t}}function kv(e,t){return!t&&e===!1?null:e===!0?{}:e}function Av(e,{plugins:t,localIds:n},s,i){const o=[],r=e.getContext();for(const a of t){const l=a.id,c=kv(s[l],i);c!==null&&o.push({plugin:a,options:Ov(e.config,{plugin:a,local:n[l]},c,r)})}return o}function Ov(e,{plugin:t,local:n},s,i){const o=e.pluginScopeKeys(t),r=e.getOptionScopes(s,o);return n&&t.defaults&&r.push(t.defaults),e.createResolver(r,i,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function la(e,t){const n=St.datasets[e]||{};return((t.datasets||{})[e]||{}).indexAxis||t.indexAxis||n.indexAxis||"x"}function Rv(e,t){let n=e;return e==="_index_"?n=t:e==="_value_"&&(n=t==="x"?"y":"x"),n}function Tv(e,t){return e===t?"_index_":"_value_"}function _u(e){if(e==="x"||e==="y"||e==="r")return e}function Dv(e){if(e==="top"||e==="bottom")return"x";if(e==="left"||e==="right")return"y"}function ca(e,...t){if(_u(e))return e;for(const n of t){const s=n.axis||Dv(n.position)||e.length>1&&_u(e[0].toLowerCase());if(s)return s}throw new Error(`Cannot determine type of '${e}' axis. Please provide 'axis' or 'position' option.`)}function yu(e,t,n){if(n[t+"AxisID"]===e)return{axis:t}}function Lv(e,t){if(t.data&&t.data.datasets){const n=t.data.datasets.filter(s=>s.xAxisID===e||s.yAxisID===e);if(n.length)return yu(e,"x",n[0])||yu(e,"y",n[0])}return{}}function Fv(e,t){const n=Wn[e.type]||{scales:{}},s=t.scales||{},i=la(e.type,t),o=Object.create(null);return Object.keys(s).forEach(r=>{const a=s[r];if(!ot(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=ca(r,a,Lv(r,e),St.scales[a.type]),c=Tv(l,i),u=n.scales||{};o[r]=Ks(Object.create(null),[{axis:l},a,u[l],u[c]])}),e.data.datasets.forEach(r=>{const a=r.type||e.type,l=r.indexAxis||la(a,t),u=(Wn[a]||{}).scales||{};Object.keys(u).forEach(f=>{const h=Rv(f,l),d=r[h+"AxisID"]||h;o[d]=o[d]||Object.create(null),Ks(o[d],[{axis:h},s[d],u[f]])})}),Object.keys(o).forEach(r=>{const a=o[r];Ks(a,[St.scales[a.type],St.scale])}),o}function Wd(e){const t=e.options||(e.options={});t.plugins=st(t.plugins,{}),t.scales=Fv(e,t)}function $d(e){return e=e||{},e.datasets=e.datasets||[],e.labels=e.labels||[],e}function Iv(e){return e=e||{},e.data=$d(e.data),Wd(e),e}const xu=new Map,Ud=new Set;function Ii(e,t){let n=xu.get(e);return n||(n=t(),xu.set(e,n),Ud.add(n)),n}const ks=(e,t,n)=>{const s=bn(t,n);s!==void 0&&e.add(s)};class Nv{constructor(t){this._config=Iv(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=$d(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),Wd(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return Ii(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,n){return Ii(`${t}.transition.${n}`,()=>[[`datasets.${t}.transitions.${n}`,`transitions.${n}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,n){return Ii(`${t}-${n}`,()=>[[`datasets.${t}.elements.${n}`,`datasets.${t}`,`elements.${n}`,""]])}pluginScopeKeys(t){const n=t.id,s=this.type;return Ii(`${s}-plugin-${n}`,()=>[[`plugins.${n}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,n){const s=this._scopeCache;let i=s.get(t);return(!i||n)&&(i=new Map,s.set(t,i)),i}getOptionScopes(t,n,s){const{options:i,type:o}=this,r=this._cachedScopes(t,s),a=r.get(n);if(a)return a;const l=new Set;n.forEach(u=>{t&&(l.add(t),u.forEach(f=>ks(l,t,f))),u.forEach(f=>ks(l,i,f)),u.forEach(f=>ks(l,Wn[o]||{},f)),u.forEach(f=>ks(l,St,f)),u.forEach(f=>ks(l,oa,f))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),Ud.has(n)&&r.set(n,c),c}chartOptionScopes(){const{options:t,type:n}=this;return[t,Wn[n]||{},St.datasets[n]||{},{type:n},St,oa]}resolveNamedOptions(t,n,s,i=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=vu(this._resolverCache,t,i);let l=r;if(zv(r,n)){o.$shared=!1,s=_n(s)?s():s;const c=this.createResolver(t,s,a);l=ds(r,s,c)}for(const c of n)o[c]=l[c];return o}createResolver(t,n,s=[""],i){const{resolver:o}=vu(this._resolverCache,t,s);return ot(n)?ds(o,n,void 0,i):o}}function vu(e,t,n){let s=e.get(t);s||(s=new Map,e.set(t,s));const i=n.join();let o=s.get(i);return o||(o={resolver:sl(t,n),subPrefixes:n.filter(a=>!a.toLowerCase().includes("hover"))},s.set(i,o)),o}const Bv=e=>ot(e)&&Object.getOwnPropertyNames(e).some(t=>_n(e[t]));function zv(e,t){const{isScriptable:n,isIndexable:s}=Sd(e);for(const i of t){const o=n(i),r=s(i),a=(r||o)&&e[i];if(o&&(_n(a)||Bv(a))||r&&wt(a))return!0}return!1}var Hv="4.4.9";const Vv=["top","bottom","left","right","chartArea"];function wu(e,t){return e==="top"||e==="bottom"||Vv.indexOf(e)===-1&&t==="x"}function Su(e,t){return function(n,s){return n[e]===s[e]?n[t]-s[t]:n[e]-s[e]}}function Mu(e){const t=e.chart,n=t.options.animation;t.notifyPlugins("afterRender"),yt(n&&n.onComplete,[e],t)}function jv(e){const t=e.chart,n=t.options.animation;yt(n&&n.onProgress,[e],t)}function Kd(e){return rl()&&typeof e=="string"?e=document.getElementById(e):e&&e.length&&(e=e[0]),e&&e.canvas&&(e=e.canvas),e}const eo={},Cu=e=>{const t=Kd(e);return Object.values(eo).filter(n=>n.canvas===t).pop()};function Wv(e,t,n){const s=Object.keys(e);for(const i of s){const o=+i;if(o>=t){const r=e[i];delete e[i],(n>0||o>t)&&(e[o+n]=r)}}}function $v(e,t,n,s){return!n||e.type==="mouseout"?null:s?t:e}class Rn{static register(...t){Te.add(...t),Eu()}static unregister(...t){Te.remove(...t),Eu()}constructor(t,n){const s=this.config=new Nv(n),i=Kd(t),o=Cu(i);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=s.createResolver(s.chartOptionScopes(),this.getContext());this.platform=new(s.platform||lv(i)),this.platform.updateConfig(s);const a=this.platform.acquireContext(i,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,u=l&&l.width;if(this.id=Uy(),this.ctx=a,this.canvas=l,this.width=u,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new Ev,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=lx(f=>this.update(f),r.resizeDelay||0),this._dataChanges=[],eo[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}ze.listen(this,"complete",Mu),ze.listen(this,"progress",jv),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:n},width:s,height:i,_aspectRatio:o}=this;return it(t)?n&&o?o:i?s/i:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return Te}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Yc(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Uc(this.canvas,this.ctx),this}stop(){return ze.stop(this),this}resize(t,n){ze.running(this)?this._resizeBeforeDraw={width:t,height:n}:this._resize(t,n)}_resize(t,n){const s=this.options,i=this.canvas,o=s.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(i,t,n,o),a=s.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,Yc(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),yt(s.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const n=this.options.scales||{};ht(n,(s,i)=>{s.id=i})}buildOrUpdateScales(){const t=this.options,n=t.scales,s=this.scales,i=Object.keys(s).reduce((r,a)=>(r[a]=!1,r),{});let o=[];n&&(o=o.concat(Object.keys(n).map(r=>{const a=n[r],l=ca(r,a),c=l==="r",u=l==="x";return{options:a,dposition:c?"chartArea":u?"bottom":"left",dtype:c?"radialLinear":u?"category":"linear"}}))),ht(o,r=>{const a=r.options,l=a.id,c=ca(l,a),u=st(a.type,r.dtype);(a.position===void 0||wu(a.position,c)!==wu(r.dposition))&&(a.position=r.dposition),i[l]=!0;let f=null;if(l in s&&s[l].type===u)f=s[l];else{const h=Te.getScale(u);f=new h({id:l,type:u,ctx:this.ctx,chart:this}),s[f.id]=f}f.init(a,t)}),ht(i,(r,a)=>{r||delete s[a]}),ht(s,r=>{Wt.configure(this,r,r.options),Wt.addBox(this,r)})}_updateMetasets(){const t=this._metasets,n=this.data.datasets.length,s=t.length;if(t.sort((i,o)=>i.index-o.index),s>n){for(let i=n;i<s;++i)this._destroyDatasetMeta(i);t.splice(n,s-n)}this._sortedMetasets=t.slice(0).sort(Su("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:n}}=this;t.length>n.length&&delete this._stacks,t.forEach((s,i)=>{n.filter(o=>o===s._dataset).length===0&&this._destroyDatasetMeta(i)})}buildOrUpdateControllers(){const t=[],n=this.data.datasets;let s,i;for(this._removeUnreferencedMetasets(),s=0,i=n.length;s<i;s++){const o=n[s];let r=this.getDatasetMeta(s);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(s),r=this.getDatasetMeta(s)),r.type=a,r.indexAxis=o.indexAxis||la(a,this.options),r.order=o.order||0,r.index=s,r.label=""+o.label,r.visible=this.isDatasetVisible(s),r.controller)r.controller.updateIndex(s),r.controller.linkScales();else{const l=Te.getController(a),{datasetElementType:c,dataElementType:u}=St.datasets[a];Object.assign(l,{dataElementType:Te.getElement(u),datasetElementType:c&&Te.getElement(c)}),r.controller=new l(this,s),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){ht(this.data.datasets,(t,n)=>{this.getDatasetMeta(n).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const n=this.config;n.update();const s=this._options=n.createResolver(n.chartOptionScopes(),this.getContext()),i=this._animationsDisabled=!s.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,u=this.data.datasets.length;c<u;c++){const{controller:f}=this.getDatasetMeta(c),h=!i&&o.indexOf(f)===-1;f.buildOrUpdateElements(h),r=Math.max(+f.getMaxOverflow(),r)}r=this._minPadding=s.layout.autoPadding?r:0,this._updateLayout(r),i||ht(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(Su("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){ht(this.scales,t=>{Wt.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,n=new Set(Object.keys(this._listeners)),s=new Set(t.events);(!Ic(n,s)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,n=this._getUniformDataChanges()||[];for(const{method:s,start:i,count:o}of n){const r=s==="_removeElements"?-o:o;Wv(t,i,r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const n=this.data.datasets.length,s=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),i=s(0);for(let o=1;o<n;o++)if(!Ic(i,s(o)))return;return Array.from(i).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;Wt.update(this,this.width,this.height,t);const n=this.chartArea,s=n.width<=0||n.height<=0;this._layers=[],ht(this.boxes,i=>{s&&i.position==="chartArea"||(i.configure&&i.configure(),this._layers.push(...i._layers()))},this),this._layers.forEach((i,o)=>{i._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let n=0,s=this.data.datasets.length;n<s;++n)this.getDatasetMeta(n).controller.configure();for(let n=0,s=this.data.datasets.length;n<s;++n)this._updateDataset(n,_n(t)?t({datasetIndex:n}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,n){const s=this.getDatasetMeta(t),i={meta:s,index:t,mode:n,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",i)!==!1&&(s.controller._update(n),i.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",i))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(ze.has(this)?this.attached&&!ze.running(this)&&ze.start(this):(this.draw(),Mu({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:s,height:i}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(s,i)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const n=this._layers;for(t=0;t<n.length&&n[t].z<=0;++t)n[t].draw(this.chartArea);for(this._drawDatasets();t<n.length;++t)n[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const n=this._sortedMetasets,s=[];let i,o;for(i=0,o=n.length;i<o;++i){const r=n[i];(!t||r.visible)&&s.push(r)}return s}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let n=t.length-1;n>=0;--n)this._drawDataset(t[n]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const n=this.ctx,s={meta:t,index:t.index,cancelable:!0},i=Ld(this,t);this.notifyPlugins("beforeDatasetDraw",s)!==!1&&(i&&Xo(n,i),t.controller.draw(),i&&Go(n),s.cancelable=!1,this.notifyPlugins("afterDatasetDraw",s))}isPointInArea(t){return Ye(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,n,s,i){const o=V0.modes[n];return typeof o=="function"?o(this,t,s,i):[]}getDatasetMeta(t){const n=this.data.datasets[t],s=this._metasets;let i=s.filter(o=>o&&o._dataset===n).pop();return i||(i={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:n&&n.order||0,index:t,_dataset:n,_parsed:[],_sorted:!1},s.push(i)),i}getContext(){return this.$context||(this.$context=xn(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const n=this.data.datasets[t];if(!n)return!1;const s=this.getDatasetMeta(t);return typeof s.hidden=="boolean"?!s.hidden:!n.hidden}setDatasetVisibility(t,n){const s=this.getDatasetMeta(t);s.hidden=!n}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,n,s){const i=s?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,i);ai(n)?(o.data[n].hidden=!s,this.update()):(this.setDatasetVisibility(t,s),r.update(o,{visible:s}),this.update(a=>a.datasetIndex===t?i:void 0))}hide(t,n){this._updateVisibility(t,n,!1)}show(t,n){this._updateVisibility(t,n,!0)}_destroyDatasetMeta(t){const n=this._metasets[t];n&&n.controller&&n.controller._destroy(),delete this._metasets[t]}_stop(){let t,n;for(this.stop(),ze.remove(this),t=0,n=this.data.datasets.length;t<n;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:n}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),Uc(t,n),this.platform.releaseContext(n),this.canvas=null,this.ctx=null),delete eo[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,n=this.platform,s=(o,r)=>{n.addEventListener(this,o,r),t[o]=r},i=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};ht(this.options.events,o=>s(o,i))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,n=this.platform,s=(l,c)=>{n.addEventListener(this,l,c),t[l]=c},i=(l,c)=>{t[l]&&(n.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let r;const a=()=>{i("attach",a),this.attached=!0,this.resize(),s("resize",o),s("detach",r)};r=()=>{this.attached=!1,i("resize",o),this._stop(),this._resize(0,0),s("attach",a)},n.isAttached(this.canvas)?a():r()}unbindEvents(){ht(this._listeners,(t,n)=>{this.platform.removeEventListener(this,n,t)}),this._listeners={},ht(this._responsiveListeners,(t,n)=>{this.platform.removeEventListener(this,n,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,n,s){const i=s?"set":"remove";let o,r,a,l;for(n==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+i+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];const c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[i+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const n=this._active||[],s=t.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!yo(s,n)&&(this._active=s,this._lastEvent=null,this._updateHoverStyles(s,n))}notifyPlugins(t,n,s){return this._plugins.notify(this,t,n,s)}isPluginEnabled(t){return this._plugins._cache.filter(n=>n.plugin.id===t).length===1}_updateHoverStyles(t,n,s){const i=this.options.hover,o=(l,c)=>l.filter(u=>!c.some(f=>u.datasetIndex===f.datasetIndex&&u.index===f.index)),r=o(n,t),a=s?t:o(t,n);r.length&&this.updateHoverStyle(r,i.mode,!1),a.length&&i.mode&&this.updateHoverStyle(a,i.mode,!0)}_eventHandler(t,n){const s={event:t,replay:n,cancelable:!0,inChartArea:this.isPointInArea(t)},i=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",s,i)===!1)return;const o=this._handleEvent(t,n,s.inChartArea);return s.cancelable=!1,this.notifyPlugins("afterEvent",s,i),(o||s.changed)&&this.render(),this}_handleEvent(t,n,s){const{_active:i=[],options:o}=this,r=n,a=this._getActiveElements(t,i,s,r),l=Jy(t),c=$v(t,this._lastEvent,s,l);s&&(this._lastEvent=null,yt(o.onHover,[t,a,this],this),l&&yt(o.onClick,[t,a,this],this));const u=!yo(a,i);return(u||n)&&(this._active=a,this._updateHoverStyles(a,i,n)),this._lastEvent=c,u}_getActiveElements(t,n,s,i){if(t.type==="mouseout")return[];if(!s)return n;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,i)}}N(Rn,"defaults",St),N(Rn,"instances",eo),N(Rn,"overrides",Wn),N(Rn,"registry",Te),N(Rn,"version",Hv),N(Rn,"getChart",Cu);function Eu(){return ht(Rn.instances,e=>e._plugins.invalidate())}function Uv(e,t,n){const{startAngle:s,pixelMargin:i,x:o,y:r,outerRadius:a,innerRadius:l}=t;let c=i/a;e.beginPath(),e.arc(o,r,a,s-c,n+c),l>i?(c=i/l,e.arc(o,r,l,n+c,s-c,!0)):e.arc(o,r,i,n+Pt,s-Pt),e.closePath(),e.clip()}function Kv(e){return nl(e,["outerStart","outerEnd","innerStart","innerEnd"])}function qv(e,t,n,s){const i=Kv(e.options.borderRadius),o=(n-t)/2,r=Math.min(o,s*t/2),a=l=>{const c=(n-Math.min(o,l))*s/2;return It(l,0,Math.min(o,c))};return{outerStart:a(i.outerStart),outerEnd:a(i.outerEnd),innerStart:It(i.innerStart,0,r),innerEnd:It(i.innerEnd,0,r)}}function Gn(e,t,n,s){return{x:n+e*Math.cos(t),y:s+e*Math.sin(t)}}function Co(e,t,n,s,i,o){const{x:r,y:a,startAngle:l,pixelMargin:c,innerRadius:u}=t,f=Math.max(t.outerRadius+s+n-c,0),h=u>0?u+s+n+c:0;let d=0;const p=i-l;if(s){const Z=u>0?u-s:0,G=f>0?f-s:0,U=(Z+G)/2,et=U!==0?p*U/(U+s):p;d=(p-et)/2}const g=Math.max(.001,p*f-n/vt)/f,m=(p-g)/2,_=l+m+d,b=i-m-d,{outerStart:w,outerEnd:v,innerStart:S,innerEnd:A}=qv(t,h,f,b-_),k=f-w,E=f-v,C=_+w/k,F=b-v/E,B=h+S,L=h+A,X=_+S/B,rt=b-A/L;if(e.beginPath(),o){const Z=(C+F)/2;if(e.arc(r,a,f,C,Z),e.arc(r,a,f,Z,F),v>0){const mt=Gn(E,F,r,a);e.arc(mt.x,mt.y,v,F,b+Pt)}const G=Gn(L,b,r,a);if(e.lineTo(G.x,G.y),A>0){const mt=Gn(L,rt,r,a);e.arc(mt.x,mt.y,A,b+Pt,rt+Math.PI)}const U=(b-A/h+(_+S/h))/2;if(e.arc(r,a,h,b-A/h,U,!0),e.arc(r,a,h,U,_+S/h,!0),S>0){const mt=Gn(B,X,r,a);e.arc(mt.x,mt.y,S,X+Math.PI,_-Pt)}const et=Gn(k,_,r,a);if(e.lineTo(et.x,et.y),w>0){const mt=Gn(k,C,r,a);e.arc(mt.x,mt.y,w,_-Pt,C)}}else{e.moveTo(r,a);const Z=Math.cos(C)*f+r,G=Math.sin(C)*f+a;e.lineTo(Z,G);const U=Math.cos(F)*f+r,et=Math.sin(F)*f+a;e.lineTo(U,et)}e.closePath()}function Yv(e,t,n,s,i){const{fullCircles:o,startAngle:r,circumference:a}=t;let l=t.endAngle;if(o){Co(e,t,n,s,l,i);for(let c=0;c<o;++c)e.fill();isNaN(a)||(l=r+(a%xt||xt))}return Co(e,t,n,s,l,i),e.fill(),l}function Xv(e,t,n,s,i){const{fullCircles:o,startAngle:r,circumference:a,options:l}=t,{borderWidth:c,borderJoinStyle:u,borderDash:f,borderDashOffset:h}=l,d=l.borderAlign==="inner";if(!c)return;e.setLineDash(f||[]),e.lineDashOffset=h,d?(e.lineWidth=c*2,e.lineJoin=u||"round"):(e.lineWidth=c,e.lineJoin=u||"bevel");let p=t.endAngle;if(o){Co(e,t,n,s,p,i);for(let g=0;g<o;++g)e.stroke();isNaN(a)||(p=r+(a%xt||xt))}d&&Uv(e,t,p),o||(Co(e,t,n,s,p,i),e.stroke())}class Ls extends Qe{constructor(n){super();N(this,"circumference");N(this,"endAngle");N(this,"fullCircles");N(this,"innerRadius");N(this,"outerRadius");N(this,"pixelMargin");N(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,n&&Object.assign(this,n)}inRange(n,s,i){const o=this.getProps(["x","y"],i),{angle:r,distance:a}=dd(o,{x:n,y:s}),{startAngle:l,endAngle:c,innerRadius:u,outerRadius:f,circumference:h}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],i),d=(this.options.spacing+this.options.borderWidth)/2,p=st(h,c-l),g=li(r,l,c)&&l!==c,m=p>=xt||g,_=Ke(a,u+d,f+d);return m&&_}getCenterPoint(n){const{x:s,y:i,startAngle:o,endAngle:r,innerRadius:a,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],n),{offset:c,spacing:u}=this.options,f=(o+r)/2,h=(a+l+u+c)/2;return{x:s+Math.cos(f)*h,y:i+Math.sin(f)*h}}tooltipPosition(n){return this.getCenterPoint(n)}draw(n){const{options:s,circumference:i}=this,o=(s.offset||0)/4,r=(s.spacing||0)/2,a=s.circular;if(this.pixelMargin=s.borderAlign==="inner"?.33:0,this.fullCircles=i>xt?Math.floor(i/xt):0,i===0||this.innerRadius<0||this.outerRadius<0)return;n.save();const l=(this.startAngle+this.endAngle)/2;n.translate(Math.cos(l)*o,Math.sin(l)*o);const c=1-Math.sin(Math.min(vt,i||0)),u=o*c;n.fillStyle=s.backgroundColor,n.strokeStyle=s.borderColor,Yv(n,this,u,r,a),Xv(n,this,u,r,a),n.restore()}}N(Ls,"id","arc"),N(Ls,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0}),N(Ls,"defaultRoutes",{backgroundColor:"backgroundColor"}),N(Ls,"descriptors",{_scriptable:!0,_indexable:n=>n!=="borderDash"});function qd(e,t,n=t){e.lineCap=st(n.borderCapStyle,t.borderCapStyle),e.setLineDash(st(n.borderDash,t.borderDash)),e.lineDashOffset=st(n.borderDashOffset,t.borderDashOffset),e.lineJoin=st(n.borderJoinStyle,t.borderJoinStyle),e.lineWidth=st(n.borderWidth,t.borderWidth),e.strokeStyle=st(n.borderColor,t.borderColor)}function Gv(e,t,n){e.lineTo(n.x,n.y)}function Jv(e){return e.stepped?xx:e.tension||e.cubicInterpolationMode==="monotone"?vx:Gv}function Yd(e,t,n={}){const s=e.length,{start:i=0,end:o=s-1}=n,{start:r,end:a}=t,l=Math.max(i,r),c=Math.min(o,a),u=i<r&&o<r||i>a&&o>a;return{count:s,start:l,loop:t.loop,ilen:c<l&&!u?s+c-l:c-l}}function Qv(e,t,n,s){const{points:i,options:o}=t,{count:r,start:a,loop:l,ilen:c}=Yd(i,n,s),u=Jv(o);let{move:f=!0,reverse:h}=s||{},d,p,g;for(d=0;d<=c;++d)p=i[(a+(h?c-d:d))%r],!p.skip&&(f?(e.moveTo(p.x,p.y),f=!1):u(e,g,p,h,o.stepped),g=p);return l&&(p=i[(a+(h?c:0))%r],u(e,g,p,h,o.stepped)),!!l}function Zv(e,t,n,s){const i=t.points,{count:o,start:r,ilen:a}=Yd(i,n,s),{move:l=!0,reverse:c}=s||{};let u=0,f=0,h,d,p,g,m,_;const b=v=>(r+(c?a-v:v))%o,w=()=>{g!==m&&(e.lineTo(u,m),e.lineTo(u,g),e.lineTo(u,_))};for(l&&(d=i[b(0)],e.moveTo(d.x,d.y)),h=0;h<=a;++h){if(d=i[b(h)],d.skip)continue;const v=d.x,S=d.y,A=v|0;A===p?(S<g?g=S:S>m&&(m=S),u=(f*u+v)/++f):(w(),e.lineTo(v,S),p=A,f=0,g=m=S),_=S}w()}function ua(e){const t=e.options,n=t.borderDash&&t.borderDash.length;return!e._decimated&&!e._loop&&!t.tension&&t.cubicInterpolationMode!=="monotone"&&!t.stepped&&!n?Zv:Qv}function tw(e){return e.stepped?Zx:e.tension||e.cubicInterpolationMode==="monotone"?t0:On}function ew(e,t,n,s){let i=t._path;i||(i=t._path=new Path2D,t.path(i,n,s)&&i.closePath()),qd(e,t.options),e.stroke(i)}function nw(e,t,n,s){const{segments:i,options:o}=t,r=ua(t);for(const a of i)qd(e,o,a.style),e.beginPath(),r(e,t,a,{start:n,end:n+s-1})&&e.closePath(),e.stroke()}const sw=typeof Path2D=="function";function iw(e,t,n,s){sw&&!t.options.segment?ew(e,t,n,s):nw(e,t,n,s)}class cn extends Qe{constructor(t){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,t&&Object.assign(this,t)}updateControlPoints(t,n){const s=this.options;if((s.tension||s.cubicInterpolationMode==="monotone")&&!s.stepped&&!this._pointsUpdated){const i=s.spanGaps?this._loop:this._fullLoop;Ux(this._points,s,t,i,n),this._pointsUpdated=!0}}set points(t){this._points=t,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=r0(this,this.options.segment))}first(){const t=this.segments,n=this.points;return t.length&&n[t[0].start]}last(){const t=this.segments,n=this.points,s=t.length;return s&&n[t[s-1].end]}interpolate(t,n){const s=this.options,i=t[n],o=this.points,r=Dd(this,{property:n,start:i,end:i});if(!r.length)return;const a=[],l=tw(s);let c,u;for(c=0,u=r.length;c<u;++c){const{start:f,end:h}=r[c],d=o[f],p=o[h];if(d===p){a.push(d);continue}const g=Math.abs((i-d[n])/(p[n]-d[n])),m=l(d,p,g,s.stepped);m[n]=t[n],a.push(m)}return a.length===1?a[0]:a}pathSegment(t,n,s){return ua(this)(t,this,n,s)}path(t,n,s){const i=this.segments,o=ua(this);let r=this._loop;n=n||0,s=s||this.points.length-n;for(const a of i)r&=o(t,this,a,{start:n,end:n+s-1});return!!r}draw(t,n,s,i){const o=this.options||{};(this.points||[]).length&&o.borderWidth&&(t.save(),iw(t,this,s,i),t.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}N(cn,"id","line"),N(cn,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),N(cn,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),N(cn,"descriptors",{_scriptable:!0,_indexable:t=>t!=="borderDash"&&t!=="fill"});function Pu(e,t,n,s){const i=e.options,{[n]:o}=e.getProps([n],s);return Math.abs(t-o)<i.radius+i.hitRadius}class no extends Qe{constructor(n){super();N(this,"parsed");N(this,"skip");N(this,"stop");this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,n&&Object.assign(this,n)}inRange(n,s,i){const o=this.options,{x:r,y:a}=this.getProps(["x","y"],i);return Math.pow(n-r,2)+Math.pow(s-a,2)<Math.pow(o.hitRadius+o.radius,2)}inXRange(n,s){return Pu(this,n,"x",s)}inYRange(n,s){return Pu(this,n,"y",s)}getCenterPoint(n){const{x:s,y:i}=this.getProps(["x","y"],n);return{x:s,y:i}}size(n){n=n||this.options||{};let s=n.radius||0;s=Math.max(s,s&&n.hoverRadius||0);const i=s&&n.borderWidth||0;return(s+i)*2}draw(n,s){const i=this.options;this.skip||i.radius<.1||!Ye(this,s,this.size(i)/2)||(n.strokeStyle=i.borderColor,n.lineWidth=i.borderWidth,n.fillStyle=i.backgroundColor,ra(n,i,this.x,this.y))}getRange(){const n=this.options||{};return n.radius+n.hitRadius}}N(no,"id","point"),N(no,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),N(no,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Xd(e,t){const{x:n,y:s,base:i,width:o,height:r}=e.getProps(["x","y","base","width","height"],t);let a,l,c,u,f;return e.horizontal?(f=r/2,a=Math.min(n,i),l=Math.max(n,i),c=s-f,u=s+f):(f=o/2,a=n-f,l=n+f,c=Math.min(s,i),u=Math.max(s,i)),{left:a,top:c,right:l,bottom:u}}function un(e,t,n,s){return e?0:It(t,n,s)}function ow(e,t,n){const s=e.options.borderWidth,i=e.borderSkipped,o=wd(s);return{t:un(i.top,o.top,0,n),r:un(i.right,o.right,0,t),b:un(i.bottom,o.bottom,0,n),l:un(i.left,o.left,0,t)}}function rw(e,t,n){const{enableBorderRadius:s}=e.getProps(["enableBorderRadius"]),i=e.options.borderRadius,o=zn(i),r=Math.min(t,n),a=e.borderSkipped,l=s||ot(i);return{topLeft:un(!l||a.top||a.left,o.topLeft,0,r),topRight:un(!l||a.top||a.right,o.topRight,0,r),bottomLeft:un(!l||a.bottom||a.left,o.bottomLeft,0,r),bottomRight:un(!l||a.bottom||a.right,o.bottomRight,0,r)}}function aw(e){const t=Xd(e),n=t.right-t.left,s=t.bottom-t.top,i=ow(e,n/2,s/2),o=rw(e,n/2,s/2);return{outer:{x:t.left,y:t.top,w:n,h:s,radius:o},inner:{x:t.left+i.l,y:t.top+i.t,w:n-i.l-i.r,h:s-i.t-i.b,radius:{topLeft:Math.max(0,o.topLeft-Math.max(i.t,i.l)),topRight:Math.max(0,o.topRight-Math.max(i.t,i.r)),bottomLeft:Math.max(0,o.bottomLeft-Math.max(i.b,i.l)),bottomRight:Math.max(0,o.bottomRight-Math.max(i.b,i.r))}}}}function kr(e,t,n,s){const i=t===null,o=n===null,a=e&&!(i&&o)&&Xd(e,s);return a&&(i||Ke(t,a.left,a.right))&&(o||Ke(n,a.top,a.bottom))}function lw(e){return e.topLeft||e.topRight||e.bottomLeft||e.bottomRight}function cw(e,t){e.rect(t.x,t.y,t.w,t.h)}function Ar(e,t,n={}){const s=e.x!==n.x?-t:0,i=e.y!==n.y?-t:0,o=(e.x+e.w!==n.x+n.w?t:0)-s,r=(e.y+e.h!==n.y+n.h?t:0)-i;return{x:e.x+s,y:e.y+i,w:e.w+o,h:e.h+r,radius:e.radius}}class so extends Qe{constructor(t){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,t&&Object.assign(this,t)}draw(t){const{inflateAmount:n,options:{borderColor:s,backgroundColor:i}}=this,{inner:o,outer:r}=aw(this),a=lw(r.radius)?ci:cw;t.save(),(r.w!==o.w||r.h!==o.h)&&(t.beginPath(),a(t,Ar(r,n,o)),t.clip(),a(t,Ar(o,-n,r)),t.fillStyle=s,t.fill("evenodd")),t.beginPath(),a(t,Ar(o,n)),t.fillStyle=i,t.fill(),t.restore()}inRange(t,n,s){return kr(this,t,n,s)}inXRange(t,n){return kr(this,t,null,n)}inYRange(t,n){return kr(this,null,t,n)}getCenterPoint(t){const{x:n,y:s,base:i,horizontal:o}=this.getProps(["x","y","base","horizontal"],t);return{x:o?(n+i)/2:n,y:o?s:(s+i)/2}}getRange(t){return t==="x"?this.width/2:this.height/2}}N(so,"id","bar"),N(so,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),N(so,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});var uw=Object.freeze({__proto__:null,ArcElement:Ls,BarElement:so,LineElement:cn,PointElement:no});const fa=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],ku=fa.map(e=>e.replace("rgb(","rgba(").replace(")",", 0.5)"));function Gd(e){return fa[e%fa.length]}function Jd(e){return ku[e%ku.length]}function fw(e,t){return e.borderColor=Gd(t),e.backgroundColor=Jd(t),++t}function hw(e,t){return e.backgroundColor=e.data.map(()=>Gd(t++)),t}function dw(e,t){return e.backgroundColor=e.data.map(()=>Jd(t++)),t}function pw(e){let t=0;return(n,s)=>{const i=e.getDatasetMeta(s).controller;i instanceof Ln?t=hw(n,t):i instanceof Gs?t=dw(n,t):i&&(t=fw(n,t))}}function Au(e){let t;for(t in e)if(e[t].borderColor||e[t].backgroundColor)return!0;return!1}function gw(e){return e&&(e.borderColor||e.backgroundColor)}function mw(){return St.borderColor!=="rgba(0,0,0,0.1)"||St.backgroundColor!=="rgba(0,0,0,0.1)"}var bw={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(e,t,n){if(!n.enabled)return;const{data:{datasets:s},options:i}=e.config,{elements:o}=i,r=Au(s)||gw(i)||o&&Au(o)||mw();if(!n.forceOverride&&r)return;const a=pw(e);s.forEach(a)}};function _w(e,t,n,s,i){const o=i.samples||s;if(o>=n)return e.slice(t,t+n);const r=[],a=(n-2)/(o-2);let l=0;const c=t+n-1;let u=t,f,h,d,p,g;for(r[l++]=e[u],f=0;f<o-2;f++){let m=0,_=0,b;const w=Math.floor((f+1)*a)+1+t,v=Math.min(Math.floor((f+2)*a)+1,n)+t,S=v-w;for(b=w;b<v;b++)m+=e[b].x,_+=e[b].y;m/=S,_/=S;const A=Math.floor(f*a)+1+t,k=Math.min(Math.floor((f+1)*a)+1,n)+t,{x:E,y:C}=e[u];for(d=p=-1,b=A;b<k;b++)p=.5*Math.abs((E-m)*(e[b].y-C)-(E-e[b].x)*(_-C)),p>d&&(d=p,h=e[b],g=b);r[l++]=h,u=g}return r[l++]=e[c],r}function yw(e,t,n,s){let i=0,o=0,r,a,l,c,u,f,h,d,p,g;const m=[],_=t+n-1,b=e[t].x,v=e[_].x-b;for(r=t;r<t+n;++r){a=e[r],l=(a.x-b)/v*s,c=a.y;const S=l|0;if(S===u)c<p?(p=c,f=r):c>g&&(g=c,h=r),i=(o*i+a.x)/++o;else{const A=r-1;if(!it(f)&&!it(h)){const k=Math.min(f,h),E=Math.max(f,h);k!==d&&k!==A&&m.push({...e[k],x:i}),E!==d&&E!==A&&m.push({...e[E],x:i})}r>0&&A!==d&&m.push(e[A]),m.push(a),u=S,o=0,p=g=c,f=h=d=r}}return m}function Qd(e){if(e._decimated){const t=e._data;delete e._decimated,delete e._data,Object.defineProperty(e,"data",{configurable:!0,enumerable:!0,writable:!0,value:t})}}function Ou(e){e.data.datasets.forEach(t=>{Qd(t)})}function xw(e,t){const n=t.length;let s=0,i;const{iScale:o}=e,{min:r,max:a,minDefined:l,maxDefined:c}=o.getUserBounds();return l&&(s=It(qe(t,o.axis,r).lo,0,n-1)),c?i=It(qe(t,o.axis,a).hi+1,s,n)-s:i=n-s,{start:s,count:i}}var vw={id:"decimation",defaults:{algorithm:"min-max",enabled:!1},beforeElementsUpdate:(e,t,n)=>{if(!n.enabled){Ou(e);return}const s=e.width;e.data.datasets.forEach((i,o)=>{const{_data:r,indexAxis:a}=i,l=e.getDatasetMeta(o),c=r||i.data;if(Ts([a,e.options.indexAxis])==="y"||!l.controller.supportsDecimation)return;const u=e.scales[l.xAxisID];if(u.type!=="linear"&&u.type!=="time"||e.options.parsing)return;let{start:f,count:h}=xw(l,c);const d=n.threshold||4*s;if(h<=d){Qd(i);return}it(r)&&(i._data=c,delete i.data,Object.defineProperty(i,"data",{configurable:!0,enumerable:!0,get:function(){return this._decimated},set:function(g){this._data=g}}));let p;switch(n.algorithm){case"lttb":p=_w(c,f,h,s,n);break;case"min-max":p=yw(c,f,h,s);break;default:throw new Error(`Unsupported decimation algorithm '${n.algorithm}'`)}i._decimated=p})},destroy(e){Ou(e)}};function ww(e,t,n){const s=e.segments,i=e.points,o=t.points,r=[];for(const a of s){let{start:l,end:c}=a;c=cl(l,c,i);const u=ha(n,i[l],i[c],a.loop);if(!t.segments){r.push({source:a,target:u,start:i[l],end:i[c]});continue}const f=Dd(t,u);for(const h of f){const d=ha(n,o[h.start],o[h.end],h.loop),p=Td(a,i,d);for(const g of p)r.push({source:g,target:h,start:{[n]:Ru(u,d,"start",Math.max)},end:{[n]:Ru(u,d,"end",Math.min)}})}}return r}function ha(e,t,n,s){if(s)return;let i=t[e],o=n[e];return e==="angle"&&(i=oe(i),o=oe(o)),{property:e,start:i,end:o}}function Sw(e,t){const{x:n=null,y:s=null}=e||{},i=t.points,o=[];return t.segments.forEach(({start:r,end:a})=>{a=cl(r,a,i);const l=i[r],c=i[a];s!==null?(o.push({x:l.x,y:s}),o.push({x:c.x,y:s})):n!==null&&(o.push({x:n,y:l.y}),o.push({x:n,y:c.y}))}),o}function cl(e,t,n){for(;t>e;t--){const s=n[t];if(!isNaN(s.x)&&!isNaN(s.y))break}return t}function Ru(e,t,n,s){return e&&t?s(e[n],t[n]):e?e[n]:t?t[n]:0}function Zd(e,t){let n=[],s=!1;return wt(e)?(s=!0,n=e):n=Sw(e,t),n.length?new cn({points:n,options:{tension:0},_loop:s,_fullLoop:s}):null}function Tu(e){return e&&e.fill!==!1}function Mw(e,t,n){let i=e[t].fill;const o=[t];let r;if(!n)return i;for(;i!==!1&&o.indexOf(i)===-1;){if(!Ct(i))return i;if(r=e[i],!r)return!1;if(r.visible)return i;o.push(i),i=r.fill}return!1}function Cw(e,t,n){const s=Aw(e);if(ot(s))return isNaN(s.value)?!1:s;let i=parseFloat(s);return Ct(i)&&Math.floor(i)===i?Ew(s[0],t,i,n):["origin","start","end","stack","shape"].indexOf(s)>=0&&s}function Ew(e,t,n,s){return(e==="-"||e==="+")&&(n=t+n),n===t||n<0||n>=s?!1:n}function Pw(e,t){let n=null;return e==="start"?n=t.bottom:e==="end"?n=t.top:ot(e)?n=t.getPixelForValue(e.value):t.getBasePixel&&(n=t.getBasePixel()),n}function kw(e,t,n){let s;return e==="start"?s=n:e==="end"?s=t.options.reverse?t.min:t.max:ot(e)?s=e.value:s=t.getBaseValue(),s}function Aw(e){const t=e.options,n=t.fill;let s=st(n&&n.target,n);return s===void 0&&(s=!!t.backgroundColor),s===!1||s===null?!1:s===!0?"origin":s}function Ow(e){const{scale:t,index:n,line:s}=e,i=[],o=s.segments,r=s.points,a=Rw(t,n);a.push(Zd({x:null,y:t.bottom},s));for(let l=0;l<o.length;l++){const c=o[l];for(let u=c.start;u<=c.end;u++)Tw(i,r[u],a)}return new cn({points:i,options:{}})}function Rw(e,t){const n=[],s=e.getMatchingVisibleMetas("line");for(let i=0;i<s.length;i++){const o=s[i];if(o.index===t)break;o.hidden||n.unshift(o.dataset)}return n}function Tw(e,t,n){const s=[];for(let i=0;i<n.length;i++){const o=n[i],{first:r,last:a,point:l}=Dw(o,t,"x");if(!(!l||r&&a)){if(r)s.unshift(l);else if(e.push(l),!a)break}}e.push(...s)}function Dw(e,t,n){const s=e.interpolate(t,n);if(!s)return{};const i=s[n],o=e.segments,r=e.points;let a=!1,l=!1;for(let c=0;c<o.length;c++){const u=o[c],f=r[u.start][n],h=r[u.end][n];if(Ke(i,f,h)){a=i===f,l=i===h;break}}return{first:a,last:l,point:s}}class tp{constructor(t){this.x=t.x,this.y=t.y,this.radius=t.radius}pathSegment(t,n,s){const{x:i,y:o,radius:r}=this;return n=n||{start:0,end:xt},t.arc(i,o,r,n.end,n.start,!0),!s.bounds}interpolate(t){const{x:n,y:s,radius:i}=this,o=t.angle;return{x:n+Math.cos(o)*i,y:s+Math.sin(o)*i,angle:o}}}function Lw(e){const{chart:t,fill:n,line:s}=e;if(Ct(n))return Fw(t,n);if(n==="stack")return Ow(e);if(n==="shape")return!0;const i=Iw(e);return i instanceof tp?i:Zd(i,s)}function Fw(e,t){const n=e.getDatasetMeta(t);return n&&e.isDatasetVisible(t)?n.dataset:null}function Iw(e){return(e.scale||{}).getPointPositionForValue?Bw(e):Nw(e)}function Nw(e){const{scale:t={},fill:n}=e,s=Pw(n,t);if(Ct(s)){const i=t.isHorizontal();return{x:i?s:null,y:i?null:s}}return null}function Bw(e){const{scale:t,fill:n}=e,s=t.options,i=t.getLabels().length,o=s.reverse?t.max:t.min,r=kw(n,t,o),a=[];if(s.grid.circular){const l=t.getPointPositionForValue(0,o);return new tp({x:l.x,y:l.y,radius:t.getDistanceFromCenterForValue(r)})}for(let l=0;l<i;++l)a.push(t.getPointPositionForValue(l,r));return a}function Or(e,t,n){const s=Lw(t),{chart:i,index:o,line:r,scale:a,axis:l}=t,c=r.options,u=c.fill,f=c.backgroundColor,{above:h=f,below:d=f}=u||{},p=i.getDatasetMeta(o),g=Ld(i,p);s&&r.points.length&&(Xo(e,n),zw(e,{line:r,target:s,above:h,below:d,area:n,scale:a,axis:l,clip:g}),Go(e))}function zw(e,t){const{line:n,target:s,above:i,below:o,area:r,scale:a,clip:l}=t,c=n._loop?"angle":t.axis;e.save(),c==="x"&&o!==i&&(Du(e,s,r.top),Lu(e,{line:n,target:s,color:i,scale:a,property:c,clip:l}),e.restore(),e.save(),Du(e,s,r.bottom)),Lu(e,{line:n,target:s,color:o,scale:a,property:c,clip:l}),e.restore()}function Du(e,t,n){const{segments:s,points:i}=t;let o=!0,r=!1;e.beginPath();for(const a of s){const{start:l,end:c}=a,u=i[l],f=i[cl(l,c,i)];o?(e.moveTo(u.x,u.y),o=!1):(e.lineTo(u.x,n),e.lineTo(u.x,u.y)),r=!!t.pathSegment(e,a,{move:r}),r?e.closePath():e.lineTo(f.x,n)}e.lineTo(t.first().x,n),e.closePath(),e.clip()}function Lu(e,t){const{line:n,target:s,property:i,color:o,scale:r,clip:a}=t,l=ww(n,s,i);for(const{source:c,target:u,start:f,end:h}of l){const{style:{backgroundColor:d=o}={}}=c,p=s!==!0;e.save(),e.fillStyle=d,Hw(e,r,a,p&&ha(i,f,h)),e.beginPath();const g=!!n.pathSegment(e,c);let m;if(p){g?e.closePath():Fu(e,s,h,i);const _=!!s.pathSegment(e,u,{move:g,reverse:!0});m=g&&_,m||Fu(e,s,f,i)}e.closePath(),e.fill(m?"evenodd":"nonzero"),e.restore()}}function Hw(e,t,n,s){const i=t.chart.chartArea,{property:o,start:r,end:a}=s||{};if(o==="x"||o==="y"){let l,c,u,f;o==="x"?(l=r,c=i.top,u=a,f=i.bottom):(l=i.left,c=r,u=i.right,f=a),e.beginPath(),n&&(l=Math.max(l,n.left),u=Math.min(u,n.right),c=Math.max(c,n.top),f=Math.min(f,n.bottom)),e.rect(l,c,u-l,f-c),e.clip()}}function Fu(e,t,n,s){const i=t.interpolate(n,s);i&&e.lineTo(i.x,i.y)}var Vw={id:"filler",afterDatasetsUpdate(e,t,n){const s=(e.data.datasets||[]).length,i=[];let o,r,a,l;for(r=0;r<s;++r)o=e.getDatasetMeta(r),a=o.dataset,l=null,a&&a.options&&a instanceof cn&&(l={visible:e.isDatasetVisible(r),index:r,fill:Cw(a,r,s),chart:e,axis:o.controller.options.indexAxis,scale:o.vScale,line:a}),o.$filler=l,i.push(l);for(r=0;r<s;++r)l=i[r],!(!l||l.fill===!1)&&(l.fill=Mw(i,r,n.propagate))},beforeDraw(e,t,n){const s=n.drawTime==="beforeDraw",i=e.getSortedVisibleDatasetMetas(),o=e.chartArea;for(let r=i.length-1;r>=0;--r){const a=i[r].$filler;a&&(a.line.updateControlPoints(o,a.axis),s&&a.fill&&Or(e.ctx,a,o))}},beforeDatasetsDraw(e,t,n){if(n.drawTime!=="beforeDatasetsDraw")return;const s=e.getSortedVisibleDatasetMetas();for(let i=s.length-1;i>=0;--i){const o=s[i].$filler;Tu(o)&&Or(e.ctx,o,e.chartArea)}},beforeDatasetDraw(e,t,n){const s=t.meta.$filler;!Tu(s)||n.drawTime!=="beforeDatasetDraw"||Or(e.ctx,s,e.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const Iu=(e,t)=>{let{boxHeight:n=t,boxWidth:s=t}=e;return e.usePointStyle&&(n=Math.min(n,t),s=e.pointStyleWidth||Math.min(s,t)),{boxWidth:s,boxHeight:n,itemHeight:Math.max(t,n)}},jw=(e,t)=>e!==null&&t!==null&&e.datasetIndex===t.datasetIndex&&e.index===t.index;class Nu extends Qe{constructor(t){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,n,s){this.maxWidth=t,this.maxHeight=n,this._margins=s,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const t=this.options.labels||{};let n=yt(t.generateLabels,[this.chart],this)||[];t.filter&&(n=n.filter(s=>t.filter(s,this.chart.data))),t.sort&&(n=n.sort((s,i)=>t.sort(s,i,this.chart.data))),this.options.reverse&&n.reverse(),this.legendItems=n}fit(){const{options:t,ctx:n}=this;if(!t.display){this.width=this.height=0;return}const s=t.labels,i=Dt(s.font),o=i.size,r=this._computeTitleHeight(),{boxWidth:a,itemHeight:l}=Iu(s,o);let c,u;n.font=i.string,this.isHorizontal()?(c=this.maxWidth,u=this._fitRows(r,o,a,l)+10):(u=this.maxHeight,c=this._fitCols(r,i,a,l)+10),this.width=Math.min(c,t.maxWidth||this.maxWidth),this.height=Math.min(u,t.maxHeight||this.maxHeight)}_fitRows(t,n,s,i){const{ctx:o,maxWidth:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.lineWidths=[0],u=i+a;let f=t;o.textAlign="left",o.textBaseline="middle";let h=-1,d=-u;return this.legendItems.forEach((p,g)=>{const m=s+n/2+o.measureText(p.text).width;(g===0||c[c.length-1]+m+2*a>r)&&(f+=u,c[c.length-(g>0?0:1)]=0,d+=u,h++),l[g]={left:0,top:d,row:h,width:m,height:i},c[c.length-1]+=m+a}),f}_fitCols(t,n,s,i){const{ctx:o,maxHeight:r,options:{labels:{padding:a}}}=this,l=this.legendHitBoxes=[],c=this.columnSizes=[],u=r-t;let f=a,h=0,d=0,p=0,g=0;return this.legendItems.forEach((m,_)=>{const{itemWidth:b,itemHeight:w}=Ww(s,n,o,m,i);_>0&&d+w+2*a>u&&(f+=h+a,c.push({width:h,height:d}),p+=h+a,g++,h=d=0),l[_]={left:p,top:d,col:g,width:b,height:w},h=Math.max(h,b),d+=w+a}),f+=h,c.push({width:h,height:d}),f}adjustHitBoxes(){if(!this.options.display)return;const t=this._computeTitleHeight(),{legendHitBoxes:n,options:{align:s,labels:{padding:i},rtl:o}}=this,r=os(o,this.left,this.width);if(this.isHorizontal()){let a=0,l=Bt(s,this.left+i,this.right-this.lineWidths[a]);for(const c of n)a!==c.row&&(a=c.row,l=Bt(s,this.left+i,this.right-this.lineWidths[a])),c.top+=this.top+t+i,c.left=r.leftForLtr(r.x(l),c.width),l+=c.width+i}else{let a=0,l=Bt(s,this.top+t+i,this.bottom-this.columnSizes[a].height);for(const c of n)c.col!==a&&(a=c.col,l=Bt(s,this.top+t+i,this.bottom-this.columnSizes[a].height)),c.top=l,c.left+=this.left+i,c.left=r.leftForLtr(r.x(c.left),c.width),l+=c.height+i}}isHorizontal(){return this.options.position==="top"||this.options.position==="bottom"}draw(){if(this.options.display){const t=this.ctx;Xo(t,this),this._draw(),Go(t)}}_draw(){const{options:t,columnSizes:n,lineWidths:s,ctx:i}=this,{align:o,labels:r}=t,a=St.color,l=os(t.rtl,this.left,this.width),c=Dt(r.font),{padding:u}=r,f=c.size,h=f/2;let d;this.drawTitle(),i.textAlign=l.textAlign("left"),i.textBaseline="middle",i.lineWidth=.5,i.font=c.string;const{boxWidth:p,boxHeight:g,itemHeight:m}=Iu(r,f),_=function(A,k,E){if(isNaN(p)||p<=0||isNaN(g)||g<0)return;i.save();const C=st(E.lineWidth,1);if(i.fillStyle=st(E.fillStyle,a),i.lineCap=st(E.lineCap,"butt"),i.lineDashOffset=st(E.lineDashOffset,0),i.lineJoin=st(E.lineJoin,"miter"),i.lineWidth=C,i.strokeStyle=st(E.strokeStyle,a),i.setLineDash(st(E.lineDash,[])),r.usePointStyle){const F={radius:g*Math.SQRT2/2,pointStyle:E.pointStyle,rotation:E.rotation,borderWidth:C},B=l.xPlus(A,p/2),L=k+h;vd(i,F,B,L,r.pointStyleWidth&&p)}else{const F=k+Math.max((f-g)/2,0),B=l.leftForLtr(A,p),L=zn(E.borderRadius);i.beginPath(),Object.values(L).some(X=>X!==0)?ci(i,{x:B,y:F,w:p,h:g,radius:L}):i.rect(B,F,p,g),i.fill(),C!==0&&i.stroke()}i.restore()},b=function(A,k,E){$n(i,E.text,A,k+m/2,c,{strikethrough:E.hidden,textAlign:l.textAlign(E.textAlign)})},w=this.isHorizontal(),v=this._computeTitleHeight();w?d={x:Bt(o,this.left+u,this.right-s[0]),y:this.top+u+v,line:0}:d={x:this.left+u,y:Bt(o,this.top+v+u,this.bottom-n[0].height),line:0},Ad(this.ctx,t.textDirection);const S=m+u;this.legendItems.forEach((A,k)=>{i.strokeStyle=A.fontColor,i.fillStyle=A.fontColor;const E=i.measureText(A.text).width,C=l.textAlign(A.textAlign||(A.textAlign=r.textAlign)),F=p+h+E;let B=d.x,L=d.y;l.setWidth(this.width),w?k>0&&B+F+u>this.right&&(L=d.y+=S,d.line++,B=d.x=Bt(o,this.left+u,this.right-s[d.line])):k>0&&L+S>this.bottom&&(B=d.x=B+n[d.line].width+u,d.line++,L=d.y=Bt(o,this.top+v+u,this.bottom-n[d.line].height));const X=l.x(B);if(_(X,L,A),B=cx(C,B+p+h,w?B+F:this.right,t.rtl),b(l.x(B),L,A),w)d.x+=F+u;else if(typeof A.text!="string"){const rt=c.lineHeight;d.y+=ep(A,rt)+u}else d.y+=S}),Od(this.ctx,t.textDirection)}drawTitle(){const t=this.options,n=t.title,s=Dt(n.font),i=$t(n.padding);if(!n.display)return;const o=os(t.rtl,this.left,this.width),r=this.ctx,a=n.position,l=s.size/2,c=i.top+l;let u,f=this.left,h=this.width;if(this.isHorizontal())h=Math.max(...this.lineWidths),u=this.top+c,f=Bt(t.align,f,this.right-h);else{const p=this.columnSizes.reduce((g,m)=>Math.max(g,m.height),0);u=c+Bt(t.align,this.top,this.bottom-p-t.labels.padding-this._computeTitleHeight())}const d=Bt(a,f,f+h);r.textAlign=o.textAlign(tl(a)),r.textBaseline="middle",r.strokeStyle=n.color,r.fillStyle=n.color,r.font=s.string,$n(r,n.text,d,u,s)}_computeTitleHeight(){const t=this.options.title,n=Dt(t.font),s=$t(t.padding);return t.display?n.lineHeight+s.height:0}_getLegendItemAt(t,n){let s,i,o;if(Ke(t,this.left,this.right)&&Ke(n,this.top,this.bottom)){for(o=this.legendHitBoxes,s=0;s<o.length;++s)if(i=o[s],Ke(t,i.left,i.left+i.width)&&Ke(n,i.top,i.top+i.height))return this.legendItems[s]}return null}handleEvent(t){const n=this.options;if(!Kw(t.type,n))return;const s=this._getLegendItemAt(t.x,t.y);if(t.type==="mousemove"||t.type==="mouseout"){const i=this._hoveredItem,o=jw(i,s);i&&!o&&yt(n.onLeave,[t,i,this],this),this._hoveredItem=s,s&&!o&&yt(n.onHover,[t,s,this],this)}else s&&yt(n.onClick,[t,s,this],this)}}function Ww(e,t,n,s,i){const o=$w(s,e,t,n),r=Uw(i,s,t.lineHeight);return{itemWidth:o,itemHeight:r}}function $w(e,t,n,s){let i=e.text;return i&&typeof i!="string"&&(i=i.reduce((o,r)=>o.length>r.length?o:r)),t+n.size/2+s.measureText(i).width}function Uw(e,t,n){let s=e;return typeof t.text!="string"&&(s=ep(t,n)),s}function ep(e,t){const n=e.text?e.text.length:0;return t*n}function Kw(e,t){return!!((e==="mousemove"||e==="mouseout")&&(t.onHover||t.onLeave)||t.onClick&&(e==="click"||e==="mouseup"))}var qw={id:"legend",_element:Nu,start(e,t,n){const s=e.legend=new Nu({ctx:e.ctx,options:n,chart:e});Wt.configure(e,s,n),Wt.addBox(e,s)},stop(e){Wt.removeBox(e,e.legend),delete e.legend},beforeUpdate(e,t,n){const s=e.legend;Wt.configure(e,s,n),s.options=n},afterUpdate(e){const t=e.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(e,t){t.replay||e.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(e,t,n){const s=t.datasetIndex,i=n.chart;i.isDatasetVisible(s)?(i.hide(s),t.hidden=!0):(i.show(s),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:e=>e.chart.options.color,boxWidth:40,padding:10,generateLabels(e){const t=e.data.datasets,{labels:{usePointStyle:n,pointStyle:s,textAlign:i,color:o,useBorderRadius:r,borderRadius:a}}=e.legend.options;return e._getSortedDatasetMetas().map(l=>{const c=l.controller.getStyle(n?0:void 0),u=$t(c.borderWidth);return{text:t[l.index].label,fillStyle:c.backgroundColor,fontColor:o,hidden:!l.visible,lineCap:c.borderCapStyle,lineDash:c.borderDash,lineDashOffset:c.borderDashOffset,lineJoin:c.borderJoinStyle,lineWidth:(u.width+u.height)/4,strokeStyle:c.borderColor,pointStyle:s||c.pointStyle,rotation:c.rotation,textAlign:i||c.textAlign,borderRadius:r&&(a||c.borderRadius),datasetIndex:l.index}},this)}},title:{color:e=>e.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:e=>!e.startsWith("on"),labels:{_scriptable:e=>!["generateLabels","filter","sort"].includes(e)}}};class ul extends Qe{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,n){const s=this.options;if(this.left=0,this.top=0,!s.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=n;const i=wt(s.text)?s.text.length:1;this._padding=$t(s.padding);const o=i*Dt(s.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:n,left:s,bottom:i,right:o,options:r}=this,a=r.align;let l=0,c,u,f;return this.isHorizontal()?(u=Bt(a,s,o),f=n+t,c=o-s):(r.position==="left"?(u=s+t,f=Bt(a,i,n),l=vt*-.5):(u=o-t,f=Bt(a,n,i),l=vt*.5),c=i-n),{titleX:u,titleY:f,maxWidth:c,rotation:l}}draw(){const t=this.ctx,n=this.options;if(!n.display)return;const s=Dt(n.font),o=s.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);$n(t,n.text,0,0,s,{color:n.color,maxWidth:l,rotation:c,textAlign:tl(n.align),textBaseline:"middle",translation:[r,a]})}}function Yw(e,t){const n=new ul({ctx:e.ctx,options:t,chart:e});Wt.configure(e,n,t),Wt.addBox(e,n),e.titleBlock=n}var Xw={id:"title",_element:ul,start(e,t,n){Yw(e,n)},stop(e){const t=e.titleBlock;Wt.removeBox(e,t),delete e.titleBlock},beforeUpdate(e,t,n){const s=e.titleBlock;Wt.configure(e,s,n),s.options=n},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Ni=new WeakMap;var Gw={id:"subtitle",start(e,t,n){const s=new ul({ctx:e.ctx,options:n,chart:e});Wt.configure(e,s,n),Wt.addBox(e,s),Ni.set(e,s)},stop(e){Wt.removeBox(e,Ni.get(e)),Ni.delete(e)},beforeUpdate(e,t,n){const s=Ni.get(e);Wt.configure(e,s,n),s.options=n},defaults:{align:"center",display:!1,font:{weight:"normal"},fullSize:!0,padding:0,position:"top",text:"",weight:1500},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const Fs={average(e){if(!e.length)return!1;let t,n,s=new Set,i=0,o=0;for(t=0,n=e.length;t<n;++t){const a=e[t].element;if(a&&a.hasValue()){const l=a.tooltipPosition();s.add(l.x),i+=l.y,++o}}return o===0||s.size===0?!1:{x:[...s].reduce((a,l)=>a+l)/s.size,y:i/o}},nearest(e,t){if(!e.length)return!1;let n=t.x,s=t.y,i=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=e.length;o<r;++o){const l=e[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),u=ia(t,c);u<i&&(i=u,a=l)}}if(a){const l=a.tooltipPosition();n=l.x,s=l.y}return{x:n,y:s}}};function Oe(e,t){return t&&(wt(t)?Array.prototype.push.apply(e,t):e.push(t)),e}function He(e){return(typeof e=="string"||e instanceof String)&&e.indexOf(`
`)>-1?e.split(`
`):e}function Jw(e,t){const{element:n,datasetIndex:s,index:i}=t,o=e.getDatasetMeta(s).controller,{label:r,value:a}=o.getLabelAndValue(i);return{chart:e,label:r,parsed:o.getParsed(i),raw:e.data.datasets[s].data[i],formattedValue:a,dataset:o.getDataset(),dataIndex:i,datasetIndex:s,element:n}}function Bu(e,t){const n=e.chart.ctx,{body:s,footer:i,title:o}=e,{boxWidth:r,boxHeight:a}=t,l=Dt(t.bodyFont),c=Dt(t.titleFont),u=Dt(t.footerFont),f=o.length,h=i.length,d=s.length,p=$t(t.padding);let g=p.height,m=0,_=s.reduce((v,S)=>v+S.before.length+S.lines.length+S.after.length,0);if(_+=e.beforeBody.length+e.afterBody.length,f&&(g+=f*c.lineHeight+(f-1)*t.titleSpacing+t.titleMarginBottom),_){const v=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;g+=d*v+(_-d)*l.lineHeight+(_-1)*t.bodySpacing}h&&(g+=t.footerMarginTop+h*u.lineHeight+(h-1)*t.footerSpacing);let b=0;const w=function(v){m=Math.max(m,n.measureText(v).width+b)};return n.save(),n.font=c.string,ht(e.title,w),n.font=l.string,ht(e.beforeBody.concat(e.afterBody),w),b=t.displayColors?r+2+t.boxPadding:0,ht(s,v=>{ht(v.before,w),ht(v.lines,w),ht(v.after,w)}),b=0,n.font=u.string,ht(e.footer,w),n.restore(),m+=p.width,{width:m,height:g}}function Qw(e,t){const{y:n,height:s}=t;return n<s/2?"top":n>e.height-s/2?"bottom":"center"}function Zw(e,t,n,s){const{x:i,width:o}=s,r=n.caretSize+n.caretPadding;if(e==="left"&&i+o+r>t.width||e==="right"&&i-o-r<0)return!0}function tS(e,t,n,s){const{x:i,width:o}=n,{width:r,chartArea:{left:a,right:l}}=e;let c="center";return s==="center"?c=i<=(a+l)/2?"left":"right":i<=o/2?c="left":i>=r-o/2&&(c="right"),Zw(c,e,t,n)&&(c="center"),c}function zu(e,t,n){const s=n.yAlign||t.yAlign||Qw(e,n);return{xAlign:n.xAlign||t.xAlign||tS(e,t,n,s),yAlign:s}}function eS(e,t){let{x:n,width:s}=e;return t==="right"?n-=s:t==="center"&&(n-=s/2),n}function nS(e,t,n){let{y:s,height:i}=e;return t==="top"?s+=n:t==="bottom"?s-=i+n:s-=i/2,s}function Hu(e,t,n,s){const{caretSize:i,caretPadding:o,cornerRadius:r}=e,{xAlign:a,yAlign:l}=n,c=i+o,{topLeft:u,topRight:f,bottomLeft:h,bottomRight:d}=zn(r);let p=eS(t,a);const g=nS(t,l,c);return l==="center"?a==="left"?p+=c:a==="right"&&(p-=c):a==="left"?p-=Math.max(u,h)+i:a==="right"&&(p+=Math.max(f,d)+i),{x:It(p,0,s.width-t.width),y:It(g,0,s.height-t.height)}}function Bi(e,t,n){const s=$t(n.padding);return t==="center"?e.x+e.width/2:t==="right"?e.x+e.width-s.right:e.x+s.left}function Vu(e){return Oe([],He(e))}function sS(e,t,n){return xn(e,{tooltip:t,tooltipItems:n,type:"tooltip"})}function ju(e,t){const n=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return n?e.override(n):e}const np={beforeTitle:Be,title(e){if(e.length>0){const t=e[0],n=t.chart.data.labels,s=n?n.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(s>0&&t.dataIndex<s)return n[t.dataIndex]}return""},afterTitle:Be,beforeBody:Be,beforeLabel:Be,label(e){if(this&&this.options&&this.options.mode==="dataset")return e.label+": "+e.formattedValue||e.formattedValue;let t=e.dataset.label||"";t&&(t+=": ");const n=e.formattedValue;return it(n)||(t+=n),t},labelColor(e){const n=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{borderColor:n.borderColor,backgroundColor:n.backgroundColor,borderWidth:n.borderWidth,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(e){const n=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{pointStyle:n.pointStyle,rotation:n.rotation}},afterLabel:Be,afterBody:Be,beforeFooter:Be,footer:Be,afterFooter:Be};function Jt(e,t,n,s){const i=e[t].call(n,s);return typeof i>"u"?np[t].call(n,s):i}class da extends Qe{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const n=this.chart,s=this.options.setContext(this.getContext()),i=s.enabled&&n.options.animation&&s.animations,o=new Fd(this.chart,i);return i._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=sS(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,n){const{callbacks:s}=n,i=Jt(s,"beforeTitle",this,t),o=Jt(s,"title",this,t),r=Jt(s,"afterTitle",this,t);let a=[];return a=Oe(a,He(i)),a=Oe(a,He(o)),a=Oe(a,He(r)),a}getBeforeBody(t,n){return Vu(Jt(n.callbacks,"beforeBody",this,t))}getBody(t,n){const{callbacks:s}=n,i=[];return ht(t,o=>{const r={before:[],lines:[],after:[]},a=ju(s,o);Oe(r.before,He(Jt(a,"beforeLabel",this,o))),Oe(r.lines,Jt(a,"label",this,o)),Oe(r.after,He(Jt(a,"afterLabel",this,o))),i.push(r)}),i}getAfterBody(t,n){return Vu(Jt(n.callbacks,"afterBody",this,t))}getFooter(t,n){const{callbacks:s}=n,i=Jt(s,"beforeFooter",this,t),o=Jt(s,"footer",this,t),r=Jt(s,"afterFooter",this,t);let a=[];return a=Oe(a,He(i)),a=Oe(a,He(o)),a=Oe(a,He(r)),a}_createItems(t){const n=this._active,s=this.chart.data,i=[],o=[],r=[];let a=[],l,c;for(l=0,c=n.length;l<c;++l)a.push(Jw(this.chart,n[l]));return t.filter&&(a=a.filter((u,f,h)=>t.filter(u,f,h,s))),t.itemSort&&(a=a.sort((u,f)=>t.itemSort(u,f,s))),ht(a,u=>{const f=ju(t.callbacks,u);i.push(Jt(f,"labelColor",this,u)),o.push(Jt(f,"labelPointStyle",this,u)),r.push(Jt(f,"labelTextColor",this,u))}),this.labelColors=i,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(t,n){const s=this.options.setContext(this.getContext()),i=this._active;let o,r=[];if(!i.length)this.opacity!==0&&(o={opacity:0});else{const a=Fs[s.position].call(this,i,this._eventPosition);r=this._createItems(s),this.title=this.getTitle(r,s),this.beforeBody=this.getBeforeBody(r,s),this.body=this.getBody(r,s),this.afterBody=this.getAfterBody(r,s),this.footer=this.getFooter(r,s);const l=this._size=Bu(this,s),c=Object.assign({},a,l),u=zu(this.chart,s,c),f=Hu(s,c,u,this.chart);this.xAlign=u.xAlign,this.yAlign=u.yAlign,o={opacity:1,x:f.x,y:f.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&s.external&&s.external.call(this,{chart:this.chart,tooltip:this,replay:n})}drawCaret(t,n,s,i){const o=this.getCaretPosition(t,s,i);n.lineTo(o.x1,o.y1),n.lineTo(o.x2,o.y2),n.lineTo(o.x3,o.y3)}getCaretPosition(t,n,s){const{xAlign:i,yAlign:o}=this,{caretSize:r,cornerRadius:a}=s,{topLeft:l,topRight:c,bottomLeft:u,bottomRight:f}=zn(a),{x:h,y:d}=t,{width:p,height:g}=n;let m,_,b,w,v,S;return o==="center"?(v=d+g/2,i==="left"?(m=h,_=m-r,w=v+r,S=v-r):(m=h+p,_=m+r,w=v-r,S=v+r),b=m):(i==="left"?_=h+Math.max(l,u)+r:i==="right"?_=h+p-Math.max(c,f)-r:_=this.caretX,o==="top"?(w=d,v=w-r,m=_-r,b=_+r):(w=d+g,v=w+r,m=_+r,b=_-r),S=w),{x1:m,x2:_,x3:b,y1:w,y2:v,y3:S}}drawTitle(t,n,s){const i=this.title,o=i.length;let r,a,l;if(o){const c=os(s.rtl,this.x,this.width);for(t.x=Bi(this,s.titleAlign,s),n.textAlign=c.textAlign(s.titleAlign),n.textBaseline="middle",r=Dt(s.titleFont),a=s.titleSpacing,n.fillStyle=s.titleColor,n.font=r.string,l=0;l<o;++l)n.fillText(i[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+a,l+1===o&&(t.y+=s.titleMarginBottom-a)}}_drawColorBox(t,n,s,i,o){const r=this.labelColors[s],a=this.labelPointStyles[s],{boxHeight:l,boxWidth:c}=o,u=Dt(o.bodyFont),f=Bi(this,"left",o),h=i.x(f),d=l<u.lineHeight?(u.lineHeight-l)/2:0,p=n.y+d;if(o.usePointStyle){const g={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},m=i.leftForLtr(h,c)+c/2,_=p+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,ra(t,g,m,_),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,ra(t,g,m,_)}else{t.lineWidth=ot(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const g=i.leftForLtr(h,c),m=i.leftForLtr(i.xPlus(h,1),c-2),_=zn(r.borderRadius);Object.values(_).some(b=>b!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,ci(t,{x:g,y:p,w:c,h:l,radius:_}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),ci(t,{x:m,y:p+1,w:c-2,h:l-2,radius:_}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(g,p,c,l),t.strokeRect(g,p,c,l),t.fillStyle=r.backgroundColor,t.fillRect(m,p+1,c-2,l-2))}t.fillStyle=this.labelTextColors[s]}drawBody(t,n,s){const{body:i}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:u}=s,f=Dt(s.bodyFont);let h=f.lineHeight,d=0;const p=os(s.rtl,this.x,this.width),g=function(E){n.fillText(E,p.x(t.x+d),t.y+h/2),t.y+=h+o},m=p.textAlign(r);let _,b,w,v,S,A,k;for(n.textAlign=r,n.textBaseline="middle",n.font=f.string,t.x=Bi(this,m,s),n.fillStyle=s.bodyColor,ht(this.beforeBody,g),d=a&&m!=="right"?r==="center"?c/2+u:c+2+u:0,v=0,A=i.length;v<A;++v){for(_=i[v],b=this.labelTextColors[v],n.fillStyle=b,ht(_.before,g),w=_.lines,a&&w.length&&(this._drawColorBox(n,t,v,p,s),h=Math.max(f.lineHeight,l)),S=0,k=w.length;S<k;++S)g(w[S]),h=f.lineHeight;ht(_.after,g)}d=0,h=f.lineHeight,ht(this.afterBody,g),t.y-=o}drawFooter(t,n,s){const i=this.footer,o=i.length;let r,a;if(o){const l=os(s.rtl,this.x,this.width);for(t.x=Bi(this,s.footerAlign,s),t.y+=s.footerMarginTop,n.textAlign=l.textAlign(s.footerAlign),n.textBaseline="middle",r=Dt(s.footerFont),n.fillStyle=s.footerColor,n.font=r.string,a=0;a<o;++a)n.fillText(i[a],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+s.footerSpacing}}drawBackground(t,n,s,i){const{xAlign:o,yAlign:r}=this,{x:a,y:l}=t,{width:c,height:u}=s,{topLeft:f,topRight:h,bottomLeft:d,bottomRight:p}=zn(i.cornerRadius);n.fillStyle=i.backgroundColor,n.strokeStyle=i.borderColor,n.lineWidth=i.borderWidth,n.beginPath(),n.moveTo(a+f,l),r==="top"&&this.drawCaret(t,n,s,i),n.lineTo(a+c-h,l),n.quadraticCurveTo(a+c,l,a+c,l+h),r==="center"&&o==="right"&&this.drawCaret(t,n,s,i),n.lineTo(a+c,l+u-p),n.quadraticCurveTo(a+c,l+u,a+c-p,l+u),r==="bottom"&&this.drawCaret(t,n,s,i),n.lineTo(a+d,l+u),n.quadraticCurveTo(a,l+u,a,l+u-d),r==="center"&&o==="left"&&this.drawCaret(t,n,s,i),n.lineTo(a,l+f),n.quadraticCurveTo(a,l,a+f,l),n.closePath(),n.fill(),i.borderWidth>0&&n.stroke()}_updateAnimationTarget(t){const n=this.chart,s=this.$animations,i=s&&s.x,o=s&&s.y;if(i||o){const r=Fs[t.position].call(this,this._active,this._eventPosition);if(!r)return;const a=this._size=Bu(this,t),l=Object.assign({},r,this._size),c=zu(n,t,l),u=Hu(t,l,c,n);(i._to!==u.x||o._to!==u.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,u))}}_willRender(){return!!this.opacity}draw(t){const n=this.options.setContext(this.getContext());let s=this.opacity;if(!s)return;this._updateAnimationTarget(n);const i={width:this.width,height:this.height},o={x:this.x,y:this.y};s=Math.abs(s)<.001?0:s;const r=$t(n.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;n.enabled&&a&&(t.save(),t.globalAlpha=s,this.drawBackground(o,t,i,n),Ad(t,n.textDirection),o.y+=r.top,this.drawTitle(o,t,n),this.drawBody(o,t,n),this.drawFooter(o,t,n),Od(t,n.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,n){const s=this._active,i=t.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),o=!yo(s,i),r=this._positionChanged(i,n);(o||r)&&(this._active=i,this._eventPosition=n,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,n,s=!0){if(n&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const i=this.options,o=this._active||[],r=this._getActiveElements(t,o,n,s),a=this._positionChanged(r,t),l=n||!yo(r,o)||a;return l&&(this._active=r,(i.enabled||i.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,n))),l}_getActiveElements(t,n,s,i){const o=this.options;if(t.type==="mouseout")return[];if(!i)return n.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const r=this.chart.getElementsAtEventForMode(t,o.mode,o,s);return o.reverse&&r.reverse(),r}_positionChanged(t,n){const{caretX:s,caretY:i,options:o}=this,r=Fs[o.position].call(this,t,n);return r!==!1&&(s!==r.x||i!==r.y)}}N(da,"positioners",Fs);var iS={id:"tooltip",_element:da,positioners:Fs,afterInit(e,t,n){n&&(e.tooltip=new da({chart:e,options:n}))},beforeUpdate(e,t,n){e.tooltip&&e.tooltip.initialize(n)},reset(e,t,n){e.tooltip&&e.tooltip.initialize(n)},afterDraw(e){const t=e.tooltip;if(t&&t._willRender()){const n={tooltip:t};if(e.notifyPlugins("beforeTooltipDraw",{...n,cancelable:!0})===!1)return;t.draw(e.ctx),e.notifyPlugins("afterTooltipDraw",n)}},afterEvent(e,t){if(e.tooltip){const n=t.replay;e.tooltip.handleEvent(t.event,n,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(e,t)=>t.bodyFont.size,boxWidth:(e,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:np},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:e=>e!=="filter"&&e!=="itemSort"&&e!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]},oS=Object.freeze({__proto__:null,Colors:bw,Decimation:vw,Filler:Vw,Legend:qw,SubTitle:Gw,Title:Xw,Tooltip:iS});const rS=(e,t,n,s)=>(typeof t=="string"?(n=e.push(t)-1,s.unshift({index:n,label:t})):isNaN(t)&&(n=null),n);function aS(e,t,n,s){const i=e.indexOf(t);if(i===-1)return rS(e,t,n,s);const o=e.lastIndexOf(t);return i!==o?n:i}const lS=(e,t)=>e===null?null:It(Math.round(e),0,t);function Wu(e){const t=this.getLabels();return e>=0&&e<t.length?t[e]:e}class pa extends Un{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const n=this._addedLabels;if(n.length){const s=this.getLabels();for(const{index:i,label:o}of n)s[i]===o&&s.splice(i,1);this._addedLabels=[]}super.init(t)}parse(t,n){if(it(t))return null;const s=this.getLabels();return n=isFinite(n)&&s[n]===t?n:aS(s,t,st(n,t),this._addedLabels),lS(n,s.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:n}=this.getUserBounds();let{min:s,max:i}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(s=0),n||(i=this.getLabels().length-1)),this.min=s,this.max=i}buildTicks(){const t=this.min,n=this.max,s=this.options.offset,i=[];let o=this.getLabels();o=t===0&&n===o.length-1?o:o.slice(t,n+1),this._valueRange=Math.max(o.length-(s?0:1),1),this._startValue=this.min-(s?.5:0);for(let r=t;r<=n;r++)i.push({value:r});return i}getLabelForValue(t){return Wu.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const n=this.ticks;return t<0||t>n.length-1?null:this.getPixelForValue(n[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}N(pa,"id","category"),N(pa,"defaults",{ticks:{callback:Wu}});function cS(e,t){const n=[],{bounds:i,step:o,min:r,max:a,precision:l,count:c,maxTicks:u,maxDigits:f,includeBounds:h}=e,d=o||1,p=u-1,{min:g,max:m}=t,_=!it(r),b=!it(a),w=!it(c),v=(m-g)/(f+1);let S=Bc((m-g)/p/d)*d,A,k,E,C;if(S<1e-14&&!_&&!b)return[{value:g},{value:m}];C=Math.ceil(m/S)-Math.floor(g/S),C>p&&(S=Bc(C*S/p/d)*d),it(l)||(A=Math.pow(10,l),S=Math.ceil(S*A)/A),i==="ticks"?(k=Math.floor(g/S)*S,E=Math.ceil(m/S)*S):(k=g,E=m),_&&b&&o&&nx((a-r)/o,S/1e3)?(C=Math.round(Math.min((a-r)/S,u)),S=(a-r)/C,k=r,E=a):w?(k=_?r:k,E=b?a:E,C=c-1,S=(E-k)/C):(C=(E-k)/S,qs(C,Math.round(C),S/1e3)?C=Math.round(C):C=Math.ceil(C));const F=Math.max(zc(S),zc(k));A=Math.pow(10,it(l)?F:l),k=Math.round(k*A)/A,E=Math.round(E*A)/A;let B=0;for(_&&(h&&k!==r?(n.push({value:r}),k<r&&B++,qs(Math.round((k+B*S)*A)/A,r,$u(r,v,e))&&B++):k<r&&B++);B<C;++B){const L=Math.round((k+B*S)*A)/A;if(b&&L>a)break;n.push({value:L})}return b&&h&&E!==a?n.length&&qs(n[n.length-1].value,a,$u(a,v,e))?n[n.length-1].value=a:n.push({value:a}):(!b||E===a)&&n.push({value:E}),n}function $u(e,t,{horizontal:n,minRotation:s}){const i=me(s),o=(n?Math.sin(i):Math.cos(i))||.001,r=.75*t*(""+e).length;return Math.min(t/o,r)}class Eo extends Un{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,n){return it(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:n,maxDefined:s}=this.getUserBounds();let{min:i,max:o}=this;const r=l=>i=n?i:l,a=l=>o=s?o:l;if(t){const l=Fe(i),c=Fe(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(i===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(i-l)}this.min=i,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:n,stepSize:s}=t,i;return s?(i=Math.ceil(this.max/s)-Math.floor(this.min/s)+1,i>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${s} would result generating up to ${i} ticks. Limiting to 1000.`),i=1e3)):(i=this.computeTickLimit(),n=n||11),n&&(i=Math.min(n,i)),i}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,n=t.ticks;let s=this.getTickLimit();s=Math.max(2,s);const i={maxTicks:s,bounds:t.bounds,min:t.min,max:t.max,precision:n.precision,step:n.stepSize,count:n.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:n.minRotation||0,includeBounds:n.includeBounds!==!1},o=this._range||this,r=cS(i,o);return t.bounds==="ticks"&&hd(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const t=this.ticks;let n=this.min,s=this.max;if(super.configure(),this.options.offset&&t.length){const i=(s-n)/Math.max(t.length-1,1)/2;n-=i,s+=i}this._startValue=n,this._endValue=s,this._valueRange=s-n}getLabelForValue(t){return yi(t,this.chart.options.locale,this.options.ticks.format)}}class ga extends Eo{determineDataLimits(){const{min:t,max:n}=this.getMinMax(!0);this.min=Ct(t)?t:0,this.max=Ct(n)?n:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),n=t?this.width:this.height,s=me(this.options.ticks.minRotation),i=(t?Math.sin(s):Math.cos(s))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(n/Math.min(40,o.lineHeight/i))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}N(ga,"id","linear"),N(ga,"defaults",{ticks:{callback:Yo.formatters.numeric}});const fi=e=>Math.floor(ln(e)),Pn=(e,t)=>Math.pow(10,fi(e)+t);function Uu(e){return e/Math.pow(10,fi(e))===1}function Ku(e,t,n){const s=Math.pow(10,n),i=Math.floor(e/s);return Math.ceil(t/s)-i}function uS(e,t){const n=t-e;let s=fi(n);for(;Ku(e,t,s)>10;)s++;for(;Ku(e,t,s)<10;)s--;return Math.min(s,fi(e))}function fS(e,{min:t,max:n}){t=ie(e.min,t);const s=[],i=fi(t);let o=uS(t,n),r=o<0?Math.pow(10,Math.abs(o)):1;const a=Math.pow(10,o),l=i>o?Math.pow(10,i):0,c=Math.round((t-l)*r)/r,u=Math.floor((t-l)/a/10)*a*10;let f=Math.floor((c-u)/Math.pow(10,o)),h=ie(e.min,Math.round((l+u+f*Math.pow(10,o))*r)/r);for(;h<n;)s.push({value:h,major:Uu(h),significand:f}),f>=10?f=f<15?15:20:f++,f>=20&&(o++,f=2,r=o>=0?1:r),h=Math.round((l+u+f*Math.pow(10,o))*r)/r;const d=ie(e.max,h);return s.push({value:d,major:Uu(d),significand:f}),s}class ma extends Un{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,n){const s=Eo.prototype.parse.apply(this,[t,n]);if(s===0){this._zero=!0;return}return Ct(s)&&s>0?s:null}determineDataLimits(){const{min:t,max:n}=this.getMinMax(!0);this.min=Ct(t)?Math.max(0,t):null,this.max=Ct(n)?Math.max(0,n):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!Ct(this._userMin)&&(this.min=t===Pn(this.min,0)?Pn(this.min,-1):Pn(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:n}=this.getUserBounds();let s=this.min,i=this.max;const o=a=>s=t?s:a,r=a=>i=n?i:a;s===i&&(s<=0?(o(1),r(10)):(o(Pn(s,-1)),r(Pn(i,1)))),s<=0&&o(Pn(i,-1)),i<=0&&r(Pn(s,1)),this.min=s,this.max=i}buildTicks(){const t=this.options,n={min:this._userMin,max:this._userMax},s=fS(n,this);return t.bounds==="ticks"&&hd(s,this,"value"),t.reverse?(s.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),s}getLabelForValue(t){return t===void 0?"0":yi(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=ln(t),this._valueRange=ln(this.max)-ln(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(ln(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const n=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+n*this._valueRange)}}N(ma,"id","logarithmic"),N(ma,"defaults",{ticks:{callback:Yo.formatters.logarithmic,major:{enabled:!0}}});function ba(e){const t=e.ticks;if(t.display&&e.display){const n=$t(t.backdropPadding);return st(t.font&&t.font.size,St.font.size)+n.height}return 0}function hS(e,t,n){return n=wt(n)?n:[n],{w:yx(e,t.string,n),h:n.length*t.lineHeight}}function qu(e,t,n,s,i){return e===s||e===i?{start:t-n/2,end:t+n/2}:e<s||e>i?{start:t-n,end:t}:{start:t,end:t+n}}function dS(e){const t={l:e.left+e._padding.left,r:e.right-e._padding.right,t:e.top+e._padding.top,b:e.bottom-e._padding.bottom},n=Object.assign({},t),s=[],i=[],o=e._pointLabels.length,r=e.options.pointLabels,a=r.centerPointLabels?vt/o:0;for(let l=0;l<o;l++){const c=r.setContext(e.getPointLabelContext(l));i[l]=c.padding;const u=e.getPointPosition(l,e.drawingArea+i[l],a),f=Dt(c.font),h=hS(e.ctx,f,e._pointLabels[l]);s[l]=h;const d=oe(e.getIndexAngle(l)+a),p=Math.round(Qa(d)),g=qu(p,u.x,h.w,0,180),m=qu(p,u.y,h.h,90,270);pS(n,t,d,g,m)}e.setCenterPoint(t.l-n.l,n.r-t.r,t.t-n.t,n.b-t.b),e._pointLabelItems=bS(e,s,i)}function pS(e,t,n,s,i){const o=Math.abs(Math.sin(n)),r=Math.abs(Math.cos(n));let a=0,l=0;s.start<t.l?(a=(t.l-s.start)/o,e.l=Math.min(e.l,t.l-a)):s.end>t.r&&(a=(s.end-t.r)/o,e.r=Math.max(e.r,t.r+a)),i.start<t.t?(l=(t.t-i.start)/r,e.t=Math.min(e.t,t.t-l)):i.end>t.b&&(l=(i.end-t.b)/r,e.b=Math.max(e.b,t.b+l))}function gS(e,t,n){const s=e.drawingArea,{extra:i,additionalAngle:o,padding:r,size:a}=n,l=e.getPointPosition(t,s+i+r,o),c=Math.round(Qa(oe(l.angle+Pt))),u=xS(l.y,a.h,c),f=_S(c),h=yS(l.x,a.w,f);return{visible:!0,x:l.x,y:u,textAlign:f,left:h,top:u,right:h+a.w,bottom:u+a.h}}function mS(e,t){if(!t)return!0;const{left:n,top:s,right:i,bottom:o}=e;return!(Ye({x:n,y:s},t)||Ye({x:n,y:o},t)||Ye({x:i,y:s},t)||Ye({x:i,y:o},t))}function bS(e,t,n){const s=[],i=e._pointLabels.length,o=e.options,{centerPointLabels:r,display:a}=o.pointLabels,l={extra:ba(o)/2,additionalAngle:r?vt/i:0};let c;for(let u=0;u<i;u++){l.padding=n[u],l.size=t[u];const f=gS(e,u,l);s.push(f),a==="auto"&&(f.visible=mS(f,c),f.visible&&(c=f))}return s}function _S(e){return e===0||e===180?"center":e<180?"left":"right"}function yS(e,t,n){return n==="right"?e-=t:n==="center"&&(e-=t/2),e}function xS(e,t,n){return n===90||n===270?e-=t/2:(n>270||n<90)&&(e-=t),e}function vS(e,t,n){const{left:s,top:i,right:o,bottom:r}=n,{backdropColor:a}=t;if(!it(a)){const l=zn(t.borderRadius),c=$t(t.backdropPadding);e.fillStyle=a;const u=s-c.left,f=i-c.top,h=o-s+c.width,d=r-i+c.height;Object.values(l).some(p=>p!==0)?(e.beginPath(),ci(e,{x:u,y:f,w:h,h:d,radius:l}),e.fill()):e.fillRect(u,f,h,d)}}function wS(e,t){const{ctx:n,options:{pointLabels:s}}=e;for(let i=t-1;i>=0;i--){const o=e._pointLabelItems[i];if(!o.visible)continue;const r=s.setContext(e.getPointLabelContext(i));vS(n,r,o);const a=Dt(r.font),{x:l,y:c,textAlign:u}=o;$n(n,e._pointLabels[i],l,c+a.lineHeight/2,a,{color:r.color,textAlign:u,textBaseline:"middle"})}}function sp(e,t,n,s){const{ctx:i}=e;if(n)i.arc(e.xCenter,e.yCenter,t,0,xt);else{let o=e.getPointPosition(0,t);i.moveTo(o.x,o.y);for(let r=1;r<s;r++)o=e.getPointPosition(r,t),i.lineTo(o.x,o.y)}}function SS(e,t,n,s,i){const o=e.ctx,r=t.circular,{color:a,lineWidth:l}=t;!r&&!s||!a||!l||n<0||(o.save(),o.strokeStyle=a,o.lineWidth=l,o.setLineDash(i.dash||[]),o.lineDashOffset=i.dashOffset,o.beginPath(),sp(e,n,r,s),o.closePath(),o.stroke(),o.restore())}function MS(e,t,n){return xn(e,{label:n,index:t,type:"pointLabel"})}class Is extends Eo{constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=$t(ba(this.options)/2),n=this.width=this.maxWidth-t.width,s=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+n/2+t.left),this.yCenter=Math.floor(this.top+s/2+t.top),this.drawingArea=Math.floor(Math.min(n,s)/2)}determineDataLimits(){const{min:t,max:n}=this.getMinMax(!1);this.min=Ct(t)&&!isNaN(t)?t:0,this.max=Ct(n)&&!isNaN(n)?n:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/ba(this.options))}generateTickLabels(t){Eo.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((n,s)=>{const i=yt(this.options.pointLabels.callback,[n,s],this);return i||i===0?i:""}).filter((n,s)=>this.chart.getDataVisibility(s))}fit(){const t=this.options;t.display&&t.pointLabels.display?dS(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,n,s,i){this.xCenter+=Math.floor((t-n)/2),this.yCenter+=Math.floor((s-i)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,n,s,i))}getIndexAngle(t){const n=xt/(this._pointLabels.length||1),s=this.options.startAngle||0;return oe(t*n+me(s))}getDistanceFromCenterForValue(t){if(it(t))return NaN;const n=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*n:(t-this.min)*n}getValueForDistanceFromCenter(t){if(it(t))return NaN;const n=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-n:this.min+n}getPointLabelContext(t){const n=this._pointLabels||[];if(t>=0&&t<n.length){const s=n[t];return MS(this.getContext(),t,s)}}getPointPosition(t,n,s=0){const i=this.getIndexAngle(t)-Pt+s;return{x:Math.cos(i)*n+this.xCenter,y:Math.sin(i)*n+this.yCenter,angle:i}}getPointPositionForValue(t,n){return this.getPointPosition(t,this.getDistanceFromCenterForValue(n))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:n,top:s,right:i,bottom:o}=this._pointLabelItems[t];return{left:n,top:s,right:i,bottom:o}}drawBackground(){const{backgroundColor:t,grid:{circular:n}}=this.options;if(t){const s=this.ctx;s.save(),s.beginPath(),sp(this,this.getDistanceFromCenterForValue(this._endValue),n,this._pointLabels.length),s.closePath(),s.fillStyle=t,s.fill(),s.restore()}}drawGrid(){const t=this.ctx,n=this.options,{angleLines:s,grid:i,border:o}=n,r=this._pointLabels.length;let a,l,c;if(n.pointLabels.display&&wS(this,r),i.display&&this.ticks.forEach((u,f)=>{if(f!==0||f===0&&this.min<0){l=this.getDistanceFromCenterForValue(u.value);const h=this.getContext(f),d=i.setContext(h),p=o.setContext(h);SS(this,d,l,r,p)}}),s.display){for(t.save(),a=r-1;a>=0;a--){const u=s.setContext(this.getPointLabelContext(a)),{color:f,lineWidth:h}=u;!h||!f||(t.lineWidth=h,t.strokeStyle=f,t.setLineDash(u.borderDash),t.lineDashOffset=u.borderDashOffset,l=this.getDistanceFromCenterForValue(n.reverse?this.min:this.max),c=this.getPointPosition(a,l),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(c.x,c.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,n=this.options,s=n.ticks;if(!s.display)return;const i=this.getIndexAngle(0);let o,r;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(i),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&this.min>=0&&!n.reverse)return;const c=s.setContext(this.getContext(l)),u=Dt(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){t.font=u.string,r=t.measureText(a.label).width,t.fillStyle=c.backdropColor;const f=$t(c.backdropPadding);t.fillRect(-r/2-f.left,-o-u.size/2-f.top,r+f.width,u.size+f.height)}$n(t,a.label,0,-o,u,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),t.restore()}drawTitle(){}}N(Is,"id","radialLinear"),N(Is,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Yo.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}}),N(Is,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),N(Is,"descriptors",{angleLines:{_fallback:"grid"}});const Zo={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Zt=Object.keys(Zo);function Yu(e,t){return e-t}function Xu(e,t){if(it(t))return null;const n=e._adapter,{parser:s,round:i,isoWeekday:o}=e._parseOpts;let r=t;return typeof s=="function"&&(r=s(r)),Ct(r)||(r=typeof s=="string"?n.parse(r,s):n.parse(r)),r===null?null:(i&&(r=i==="week"&&(hs(o)||o===!0)?n.startOf(r,"isoWeek",o):n.startOf(r,i)),+r)}function Gu(e,t,n,s){const i=Zt.length;for(let o=Zt.indexOf(e);o<i-1;++o){const r=Zo[Zt[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((n-t)/(a*r.size))<=s)return Zt[o]}return Zt[i-1]}function CS(e,t,n,s,i){for(let o=Zt.length-1;o>=Zt.indexOf(n);o--){const r=Zt[o];if(Zo[r].common&&e._adapter.diff(i,s,r)>=t-1)return r}return Zt[n?Zt.indexOf(n):0]}function ES(e){for(let t=Zt.indexOf(e)+1,n=Zt.length;t<n;++t)if(Zo[Zt[t]].common)return Zt[t]}function Ju(e,t,n){if(!n)e[t]=!0;else if(n.length){const{lo:s,hi:i}=Za(n,t),o=n[s]>=t?n[s]:n[i];e[o]=!0}}function PS(e,t,n,s){const i=e._adapter,o=+i.startOf(t[0].value,s),r=t[t.length-1].value;let a,l;for(a=o;a<=r;a=+i.add(a,1,s))l=n[a],l>=0&&(t[l].major=!0);return t}function Qu(e,t,n){const s=[],i={},o=t.length;let r,a;for(r=0;r<o;++r)a=t[r],i[a]=r,s.push({value:a,major:!1});return o===0||!n?s:PS(e,s,i,n)}class hi extends Un{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,n={}){const s=t.time||(t.time={}),i=this._adapter=new I0._date(t.adapters.date);i.init(n),Ks(s.displayFormats,i.formats()),this._parseOpts={parser:s.parser,round:s.round,isoWeekday:s.isoWeekday},super.init(t),this._normalized=n.normalized}parse(t,n){return t===void 0?null:Xu(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,n=this._adapter,s=t.time.unit||"day";let{min:i,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(i=Math.min(i,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),i=Ct(i)&&!isNaN(i)?i:+n.startOf(Date.now(),s),o=Ct(o)&&!isNaN(o)?o:+n.endOf(Date.now(),s)+1,this.min=Math.min(i,o-1),this.max=Math.max(i+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let n=Number.POSITIVE_INFINITY,s=Number.NEGATIVE_INFINITY;return t.length&&(n=t[0],s=t[t.length-1]),{min:n,max:s}}buildTicks(){const t=this.options,n=t.time,s=t.ticks,i=s.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&i.length&&(this.min=this._userMin||i[0],this.max=this._userMax||i[i.length-1]);const o=this.min,r=this.max,a=rx(i,o,r);return this._unit=n.unit||(s.autoSkip?Gu(n.minUnit,this.min,this.max,this._getLabelCapacity(o)):CS(this,a.length,n.minUnit,this.min,this.max)),this._majorUnit=!s.major.enabled||this._unit==="year"?void 0:ES(this._unit),this.initOffsets(i),t.reverse&&a.reverse(),Qu(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let n=0,s=0,i,o;this.options.offset&&t.length&&(i=this.getDecimalForValue(t[0]),t.length===1?n=1-i:n=(this.getDecimalForValue(t[1])-i)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?s=o:s=(o-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;n=It(n,0,r),s=It(s,0,r),this._offsets={start:n,end:s,factor:1/(n+1+s)}}_generate(){const t=this._adapter,n=this.min,s=this.max,i=this.options,o=i.time,r=o.unit||Gu(o.minUnit,n,s,this._getLabelCapacity(n)),a=st(i.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=hs(l)||l===!0,u={};let f=n,h,d;if(c&&(f=+t.startOf(f,"isoWeek",l)),f=+t.startOf(f,c?"day":r),t.diff(s,n,r)>1e5*a)throw new Error(n+" and "+s+" are too far apart with stepSize of "+a+" "+r);const p=i.ticks.source==="data"&&this.getDataTimestamps();for(h=f,d=0;h<s;h=+t.add(h,a,r),d++)Ju(u,h,p);return(h===s||i.bounds==="ticks"||d===1)&&Ju(u,h,p),Object.keys(u).sort(Yu).map(g=>+g)}getLabelForValue(t){const n=this._adapter,s=this.options.time;return s.tooltipFormat?n.format(t,s.tooltipFormat):n.format(t,s.displayFormats.datetime)}format(t,n){const i=this.options.time.displayFormats,o=this._unit,r=n||i[o];return this._adapter.format(t,r)}_tickFormatFunction(t,n,s,i){const o=this.options,r=o.ticks.callback;if(r)return yt(r,[t,n,s],this);const a=o.time.displayFormats,l=this._unit,c=this._majorUnit,u=l&&a[l],f=c&&a[c],h=s[n],d=c&&f&&h&&h.major;return this._adapter.format(t,i||(d?f:u))}generateTickLabels(t){let n,s,i;for(n=0,s=t.length;n<s;++n)i=t[n],i.label=this._tickFormatFunction(i.value,n,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const n=this._offsets,s=this.getDecimalForValue(t);return this.getPixelForDecimal((n.start+s)*n.factor)}getValueForPixel(t){const n=this._offsets,s=this.getDecimalForPixel(t)/n.factor-n.end;return this.min+s*(this.max-this.min)}_getLabelSize(t){const n=this.options.ticks,s=this.ctx.measureText(t).width,i=me(this.isHorizontal()?n.maxRotation:n.minRotation),o=Math.cos(i),r=Math.sin(i),a=this._resolveTickFontOptions(0).size;return{w:s*o+a*r,h:s*r+a*o}}_getLabelCapacity(t){const n=this.options.time,s=n.displayFormats,i=s[n.unit]||s.millisecond,o=this._tickFormatFunction(t,0,Qu(this,[t],this._majorUnit),i),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],n,s;if(t.length)return t;const i=this.getMatchingVisibleMetas();if(this._normalized&&i.length)return this._cache.data=i[0].controller.getAllParsedValues(this);for(n=0,s=i.length;n<s;++n)t=t.concat(i[n].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let n,s;if(t.length)return t;const i=this.getLabels();for(n=0,s=i.length;n<s;++n)t.push(Xu(this,i[n]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return gd(t.sort(Yu))}}N(hi,"id","time"),N(hi,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function zi(e,t,n){let s=0,i=e.length-1,o,r,a,l;n?(t>=e[s].pos&&t<=e[i].pos&&({lo:s,hi:i}=qe(e,"pos",t)),{pos:o,time:a}=e[s],{pos:r,time:l}=e[i]):(t>=e[s].time&&t<=e[i].time&&({lo:s,hi:i}=qe(e,"time",t)),{time:o,pos:a}=e[s],{time:r,pos:l}=e[i]);const c=r-o;return c?a+(l-a)*(t-o)/c:a}class _a extends hi{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),n=this._table=this.buildLookupTable(t);this._minPos=zi(n,this.min),this._tableRange=zi(n,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:n,max:s}=this,i=[],o=[];let r,a,l,c,u;for(r=0,a=t.length;r<a;++r)c=t[r],c>=n&&c<=s&&i.push(c);if(i.length<2)return[{time:n,pos:0},{time:s,pos:1}];for(r=0,a=i.length;r<a;++r)u=i[r+1],l=i[r-1],c=i[r],Math.round((u+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_generate(){const t=this.min,n=this.max;let s=super.getDataTimestamps();return(!s.includes(t)||!s.length)&&s.splice(0,0,t),(!s.includes(n)||s.length===1)&&s.push(n),s.sort((i,o)=>i-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const n=this.getDataTimestamps(),s=this.getLabelTimestamps();return n.length&&s.length?t=this.normalize(n.concat(s)):t=n.length?n:s,t=this._cache.all=t,t}getDecimalForValue(t){return(zi(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const n=this._offsets,s=this.getDecimalForPixel(t)/n.factor-n.end;return zi(this._table,s*this._tableRange+this._minPos,!0)}}N(_a,"id","timeseries"),N(_a,"defaults",hi.defaults);var kS=Object.freeze({__proto__:null,CategoryScale:pa,LinearScale:ga,LogarithmicScale:ma,RadialLinearScale:Is,TimeScale:hi,TimeSeriesScale:_a});const yM=[F0,uw,oS,kS];export{Ia as A,IS as B,jS as C,Rn as D,yM as E,Qt as F,$S as G,BS as H,JS as I,GS as J,qS as K,YS as L,LS as M,di as N,TS as O,WS as P,Vf as Q,RS as R,HS as T,jt as a,DS as b,NS as c,Ot as d,XS as e,fe as f,zS as g,Vr as h,tg as i,ch as j,FS as k,QS as l,Yg as m,Ma as n,Hr as o,ns as p,Do as q,La as r,US as s,xp as t,ZS as u,OS as v,is as w,VS as x,KS as y,Sa as z};
