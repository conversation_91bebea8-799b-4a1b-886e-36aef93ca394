import{r as m,f as D,A as X,c as d,g as S,j as t,t as s,a as T,m as h,i as B,b as Z,v as V,G as $,F as j,k as M,p as x,o as n,z as tt,n as z}from"./vendor.js";import{u as et}from"./timesheet.js";import{f as b,d as at,b as st,c as rt}from"./timesheet2.js";import"./app.js";const ot={class:"space-y-6"},it={key:0,class:"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4"},lt={class:"flex"},dt={class:"ml-3"},nt={class:"text-sm text-red-800 dark:text-red-200"},ut={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ct={class:"flex justify-between items-center"},gt={class:"flex space-x-3"},xt=["disabled"],mt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},pt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},yt=["value"],ht=["value"],vt={class:"flex items-end"},bt=["disabled"],kt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},ft={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},wt={class:"flex items-center"},_t={class:"ml-5 w-0 flex-1"},jt={class:"text-lg font-medium text-gray-900 dark:text-white"},Mt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Pt={class:"flex items-center"},Ct={class:"ml-5 w-0 flex-1"},zt={class:"text-lg font-medium text-gray-900 dark:text-white"},St={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},At={class:"flex items-center"},Dt={class:"ml-5 w-0 flex-1"},Vt={class:"text-lg font-medium text-gray-900 dark:text-white"},$t={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ht={class:"flex items-center"},Nt={class:"ml-5 w-0 flex-1"},Tt={class:"text-lg font-medium text-gray-900 dark:text-white"},Bt={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Et={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Ot={class:"flex items-center justify-between"},Ft={class:"text-lg font-medium text-gray-900 dark:text-white"},Gt={class:"text-sm text-gray-500 dark:text-gray-400"},Yt={class:"overflow-x-auto"},Lt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},It={class:"bg-gray-50 dark:bg-gray-700"},Rt={key:0},Ut={key:1},Jt={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},qt={class:"px-6 py-4 whitespace-nowrap"},Kt={class:"flex items-center"},Qt={class:"text-sm font-medium text-gray-900 dark:text-white"},Wt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Xt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Zt={class:"text-xs text-gray-500 dark:text-gray-400 ml-1"},te={class:"px-6 py-4 whitespace-nowrap"},ee={class:"flex items-center"},ae={class:"flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2 max-w-[60px]"},se={class:"text-sm text-gray-900 dark:text-white"},re={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},oe={class:"flex flex-wrap gap-1"},ie={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"},le={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},de={class:"px-6 py-4 whitespace-nowrap"},ne={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},ue=["onClick"],ce=["onClick"],ge={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},xe={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},me={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},pe={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},ye={class:"px-6 py-4 whitespace-nowrap"},he={class:"px-6 py-4 whitespace-nowrap"},ve={class:"px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate"},be={key:0,class:"text-center py-12"},ke={class:"mt-1 text-sm text-gray-500 dark:text-gray-400"},fe={class:"mt-4"},we={key:1,class:"bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6 rounded-lg shadow"},_e={class:"flex-1 flex justify-between sm:hidden"},je=["disabled"],Me=["disabled"],Pe={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Ce={class:"text-sm text-gray-700 dark:text-gray-300"},ze={class:"font-medium"},Se={class:"font-medium"},Ae={class:"font-medium"},De={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px"},Ve=["onClick"],Ee={__name:"TimesheetHistory",setup($e){const p=et(),k=m(!1),f=m([]),P=m([]),c=m(new Date().getFullYear()),w=m(""),g=m("monthly"),H=m([]),l=m({currentPage:1,perPage:50,total:0,totalPages:0}),C=m({totalHours:0,billableHours:0,activeProjects:0,dailyAverage:0}),N=D(()=>p.error),E=D(()=>p.availableProjects),O=D(()=>{const r=[],e=Math.max(1,l.value.currentPage-2),u=Math.min(l.value.totalPages,l.value.currentPage+2);for(let a=e;a<=u;a++)r.push(a);return r}),F=()=>{const r=new Date().getFullYear(),e=[];for(let u=0;u<5;u++)e.push(r-u);H.value=e},v=async()=>{k.value=!0;try{g.value==="monthly"?await G():await Y(),L()}catch(r){console.error("Error loading data:",r)}finally{k.value=!1}},G=async()=>{const r=`${c.value}-01-01`,e=`${c.value}-12-31`,u={start_date:r,end_date:e,project_id:w.value},a=await p.loadTimesheetHistory(u),o={};a.forEach(i=>{const _=new Date(i.date),y=_.getMonth()+1;o[y]||(o[y]={month:y,total_hours:0,billable_hours:0,entries:[],projects:new Set,working_days:new Set}),o[y].total_hours+=i.hours||0,i.billable&&(o[y].billable_hours+=i.hours||0),o[y].entries.push(i),i.project&&o[y].projects.add(JSON.stringify({id:i.project.id,name:i.project.name})),o[y].working_days.add(_.getDate())}),f.value=Object.values(o).map(i=>({...i,projects:Array.from(i.projects).map(_=>JSON.parse(_)),daily_average:i.total_hours/Math.max(i.working_days.size,1),productivity:i.billable_hours/i.total_hours*100||0,status:i.total_hours>0?"active":"inactive"})).sort((i,_)=>i.month-_.month)},Y=async()=>{const r={start_date:`${c.value}-01-01`,end_date:`${c.value}-12-31`,project_id:w.value,page:l.value.currentPage,per_page:l.value.perPage};P.value=await p.loadTimesheetHistory(r)},L=()=>{let r=0,e=0,u=new Set;if(g.value==="monthly"){const a=Array.isArray(f.value)?f.value:[];r=a.reduce((o,i)=>o+(i.total_hours||0),0),e=a.reduce((o,i)=>o+(i.billable_hours||0),0),a.forEach(o=>{o.projects&&Array.isArray(o.projects)&&o.projects.forEach(i=>u.add(i.id))})}else{const a=Array.isArray(P.value)?P.value:[];r=a.reduce((o,i)=>o+(i.hours||0),0),e=a.reduce((o,i)=>o+(i.billable&&i.hours||0),0),a.forEach(o=>{o.project_id&&u.add(o.project_id)})}C.value={totalHours:r,billableHours:e,activeProjects:u.size,dailyAverage:r/365}},A=r=>{r>=1&&r<=l.value.totalPages&&(l.value.currentPage=r,v())},I=r=>["Gennaio","Febbraio","Marzo","Aprile","Maggio","Giugno","Luglio","Agosto","Settembre","Ottobre","Novembre","Dicembre"][r-1],R=r=>r>=80?"bg-green-500":r>=60?"bg-yellow-500":"bg-red-500",U=r=>{switch(r){case"active":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"inactive":return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300";default:return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"}},J=r=>{switch(r){case"active":return"Attivo";case"inactive":return"Inattivo";default:return"Parziale"}},q=r=>{g.value="detailed",v()},K=async r=>{const e={start_date:`${c.value}-${r.month.toString().padStart(2,"0")}-01`,end_date:`${c.value}-${r.month.toString().padStart(2,"0")}-31`};await p.exportTimesheetData(e)},Q=async()=>{const r={start_date:`${c.value}-01-01`,end_date:`${c.value}-12-31`,project_id:w.value};await p.exportTimesheetData(r)},W=()=>{p.clearError()};return X(async()=>{F(),await p.loadAvailableProjects(),await v()}),(r,e)=>{const u=Z("router-link");return n(),d("div",ot,[N.value?(n(),d("div",it,[t("div",lt,[e[6]||(e[6]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-5 w-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),t("div",dt,[t("p",nt,s(N.value),1)]),t("div",{class:"ml-auto pl-3"},[t("div",{class:"-mx-1.5 -my-1.5"},[t("button",{onClick:W,class:"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900"},e[5]||(e[5]=[t("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])])])])):S("",!0),t("div",ut,[t("div",ct,[e[9]||(e[9]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Storico Timesheet"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Visualizza i riepiloghi mensili delle tue ore lavorate ")],-1)),t("div",gt,[t("button",{onClick:Q,disabled:k.value,class:"bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},[e[7]||(e[7]=t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1)),h(" "+s(k.value?"Esportando...":"Esporta Report"),1)],8,xt),T(u,{to:"/app/timesheet/entry",class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},{default:B(()=>e[8]||(e[8]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),h(" Registra Ore ")])),_:1,__:[8]})])])]),t("div",mt,[t("div",pt,[t("div",null,[e[10]||(e[10]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Anno ",-1)),V(t("select",{"onUpdate:modelValue":e[0]||(e[0]=a=>c.value=a),onChange:v,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[(n(!0),d(j,null,M(H.value,a=>(n(),d("option",{key:a,value:a},s(a),9,yt))),128))],544),[[$,c.value]])]),t("div",null,[e[12]||(e[12]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Progetto ",-1)),V(t("select",{"onUpdate:modelValue":e[1]||(e[1]=a=>w.value=a),onChange:v,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[e[11]||(e[11]=t("option",{value:""},"Tutti i progetti",-1)),(n(!0),d(j,null,M(E.value,a=>(n(),d("option",{key:a.id,value:a.id},s(a.name),9,ht))),128))],544),[[$,w.value]])]),t("div",null,[e[14]||(e[14]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Visualizzazione ",-1)),V(t("select",{"onUpdate:modelValue":e[2]||(e[2]=a=>g.value=a),onChange:v,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},e[13]||(e[13]=[t("option",{value:"monthly"},"Riepilogo Mensile",-1),t("option",{value:"detailed"},"Dettaglio Giornaliero",-1)]),544),[[$,g.value]])]),t("div",vt,[t("button",{onClick:v,disabled:k.value,class:"w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium"},s(k.value?"Caricando...":"Aggiorna"),9,bt)])])]),t("div",kt,[t("div",ft,[t("div",wt,[e[16]||(e[16]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",_t,[t("dl",null,[e[15]||(e[15]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Totali ",-1)),t("dd",jt,s(x(b)(C.value.totalHours)),1)])])])]),t("div",Mt,[t("div",Pt,[e[18]||(e[18]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",Ct,[t("dl",null,[e[17]||(e[17]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Fatturabili ",-1)),t("dd",zt,s(x(b)(C.value.billableHours)),1)])])])]),t("div",St,[t("div",At,[e[20]||(e[20]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),t("div",Dt,[t("dl",null,[e[19]||(e[19]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Progetti Attivi ",-1)),t("dd",Vt,s(C.value.activeProjects),1)])])])]),t("div",$t,[t("div",Ht,[e[22]||(e[22]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",Nt,[t("dl",null,[e[21]||(e[21]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Media Giornaliera ",-1)),t("dd",Tt,s(x(b)(C.value.dailyAverage)),1)])])])])]),t("div",Bt,[t("div",Et,[t("div",Ot,[t("h3",Ft,s(g.value==="monthly"?"Riepiloghi Mensili":"Dettaglio Giornaliero")+" - "+s(c.value),1),t("div",Gt,s(f.value.length)+" "+s(g.value==="monthly"?"mesi":"giorni")+" trovati ",1)])]),t("div",Yt,[t("table",Lt,[t("thead",It,[g.value==="monthly"?(n(),d("tr",Rt,e[23]||(e[23]=[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Mese ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Totali ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Fatturabili ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Produttività ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Progetti ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Media Giornaliera ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato ",-1),t("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ",-1)]))):(n(),d("tr",Ut,e[24]||(e[24]=[t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Data ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Progetto ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Task ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Fatturabile ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato ",-1),t("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Descrizione ",-1)])))]),t("tbody",Jt,[g.value==="monthly"?(n(!0),d(j,{key:0},M(f.value,a=>(n(),d("tr",{key:a.month,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[t("td",qt,[t("div",Kt,[t("div",Qt,s(I(a.month))+" "+s(c.value),1)])]),t("td",Wt,s(x(b)(a.total_hours)),1),t("td",Xt,[h(s(x(b)(a.billable_hours))+" ",1),t("span",Zt," ("+s(Math.round(a.billable_hours/a.total_hours*100)||0)+"%) ",1)]),t("td",te,[t("div",ee,[t("div",ae,[t("div",{class:z(["h-2 rounded-full",R(a.productivity)]),style:tt({width:Math.min(a.productivity,100)+"%"})},null,6)]),t("span",se,s(Math.round(a.productivity))+"%",1)])]),t("td",re,[t("div",oe,[(n(!0),d(j,null,M(a.projects.slice(0,2),o=>(n(),d("span",{key:o.id,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"},s(o.name),1))),128)),a.projects.length>2?(n(),d("span",ie," +"+s(a.projects.length-2),1)):S("",!0)])]),t("td",le,s(x(b)(a.daily_average)),1),t("td",de,[t("span",{class:z(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",U(a.status)])},s(J(a.status)),3)]),t("td",ne,[t("button",{onClick:o=>q(),class:"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3"}," Dettagli ",8,ue),t("button",{onClick:o=>K(a),class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"}," Esporta ",8,ce)])]))),128)):(n(!0),d(j,{key:1},M(P.value,a=>{var o,i;return n(),d("tr",{key:a.id,class:"hover:bg-gray-50 dark:hover:bg-gray-700"},[t("td",ge,s(x(at)(a.date)),1),t("td",xe,s(((o=a.project)==null?void 0:o.name)||"N/A"),1),t("td",me,s(((i=a.task)==null?void 0:i.name)||"N/A"),1),t("td",pe,s(x(b)(a.hours)),1),t("td",ye,[t("span",{class:z(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",a.billable?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"])},s(a.billable?"Fatturabile":"Interno"),3)]),t("td",he,[t("span",{class:z(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",x(st)(a.status)])},s(x(rt)(a.status)),3)]),t("td",ve,s(a.description||"-"),1)])}),128))])]),(g.value==="monthly"?f.value:P.value).length===0?(n(),d("div",be,[e[26]||(e[26]=t("div",{class:"mx-auto h-12 w-12 text-gray-400"},[t("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1)),e[27]||(e[27]=t("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-white"},"Nessun dato disponibile",-1)),t("p",ke," Non ci sono registrazioni per l'anno "+s(c.value)+s(w.value?" e progetto selezionato":"")+". ",1),t("div",fe,[T(u,{to:"/app/timesheet/entry",class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900 dark:text-primary-300 dark:hover:bg-primary-800"},{default:B(()=>e[25]||(e[25]=[h(" Inizia a registrare ore ")])),_:1,__:[25]})])])):S("",!0)])]),l.value.totalPages>1?(n(),d("div",we,[t("div",_e,[t("button",{onClick:e[3]||(e[3]=a=>A(l.value.currentPage-1)),disabled:l.value.currentPage===1,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," Precedente ",8,je),t("button",{onClick:e[4]||(e[4]=a=>A(l.value.currentPage+1)),disabled:l.value.currentPage===l.value.totalPages,class:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"}," Successivo ",8,Me)]),t("div",Pe,[t("div",null,[t("p",Ce,[e[28]||(e[28]=h(" Mostrando ")),t("span",ze,s((l.value.currentPage-1)*l.value.perPage+1),1),e[29]||(e[29]=h(" a ")),t("span",Se,s(Math.min(l.value.currentPage*l.value.perPage,l.value.total)),1),e[30]||(e[30]=h(" di ")),t("span",Ae,s(l.value.total),1),e[31]||(e[31]=h(" risultati "))])]),t("div",null,[t("nav",De,[(n(!0),d(j,null,M(O.value,a=>(n(),d("button",{key:a,onClick:o=>A(a),class:z(["relative inline-flex items-center px-4 py-2 border text-sm font-medium",a===l.value.currentPage?"z-10 bg-primary-50 border-primary-500 text-primary-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"])},s(a),11,Ve))),128))])])])])):S("",!0)])}}};export{Ee as default};
