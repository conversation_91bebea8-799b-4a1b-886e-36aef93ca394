import{r as d,f as U,A as ce,c as l,g as v,j as e,t as a,m as X,F as T,k as j,v as y,G as z,x as Z,n as F,p as f,l as ge,o,C as me}from"./vendor.js";import{u as pe}from"./timesheet.js";import{b as ee,c as te,d as ye}from"./timesheet2.js";import"./app.js";const ke={class:"space-y-6"},be={key:0,class:"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4"},he={class:"flex"},fe={class:"ml-3"},we={class:"text-sm text-red-800 dark:text-red-200"},_e={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ce={class:"flex items-center justify-between"},Ae={class:"flex items-center space-x-3"},Me=["disabled"],Te={key:0,class:"relative"},je={key:0,class:"absolute right-0 mt-2 w-48 bg-white dark:bg-gray-700 rounded-md shadow-lg z-10"},ze={key:0,class:"mt-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4"},De={class:"flex items-start"},Be={class:"ml-3"},Re={class:"text-sm font-medium text-red-800 dark:text-red-200"},Se={class:"mt-2 space-y-1"},Le=["onClick"],Ve={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},$e={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Oe={class:"flex items-center"},Ue={class:"ml-4"},Fe={class:"text-2xl font-semibold text-gray-900 dark:text-white"},Ge={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ne={class:"flex items-center"},Pe={class:"ml-4"},Ee={class:"text-2xl font-semibold text-gray-900 dark:text-white"},Ie={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},He={class:"flex items-center"},Qe={class:"ml-4"},Ye={class:"text-2xl font-semibold text-gray-900 dark:text-white"},qe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Je={class:"flex items-center"},Ke={class:"ml-4"},We={class:"text-2xl font-semibold text-gray-900 dark:text-white"},Xe={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-4"},Ze={class:"grid grid-cols-1 md:grid-cols-5 gap-4"},et=["value"],tt=["value"],st={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},rt={class:"overflow-x-auto"},at={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},lt={class:"bg-gray-50 dark:bg-gray-700"},ot={class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"},dt=["checked"],it={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},nt={key:0},ut={key:1},xt={class:"px-6 py-4 whitespace-nowrap"},vt=["value"],ct={class:"px-6 py-4 whitespace-nowrap"},gt={class:"flex items-center"},mt={class:"flex-shrink-0 h-10 w-10"},pt={class:"h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center"},yt={class:"text-sm font-medium text-gray-700 dark:text-gray-300"},kt={class:"ml-4"},bt={class:"text-sm font-medium text-gray-900 dark:text-white"},ht={class:"text-sm text-gray-500 dark:text-gray-400"},ft={key:0,class:"ml-2"},wt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},_t={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},Ct={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white"},At={class:"px-6 py-4 whitespace-nowrap"},Mt={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400"},Tt={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},jt={class:"flex items-center justify-end space-x-2"},zt=["onClick"],Dt=["onClick"],Bt=["onClick"],Rt={key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},St={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},Lt={class:"mt-3"},Vt={class:"flex items-center justify-between mb-4"},$t={class:"text-lg font-medium text-gray-900 dark:text-white"},Ot={class:"space-y-4"},Ut={class:"grid grid-cols-2 gap-4"},Ft={class:"text-sm text-gray-900 dark:text-white"},Gt={class:"text-sm text-gray-900 dark:text-white"},Nt={class:"text-sm text-gray-900 dark:text-white"},Pt={key:0,class:"flex space-x-3"},Et={key:2,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},It={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},Ht={class:"mt-3"},Qt={class:"space-y-4"},Yt={class:"flex space-x-3"},qt=["disabled"],es={__name:"TimesheetApprovals",setup(Jt){ge();const u=pe(),c=d([]),G=d([]),k=d([]),n=d([]),i=d(null),D=d(!1),w=d(!1),b=d(!1),h=d(!1),_=d(!1),g=d(""),B=d(new Date().getMonth()+1),R=d("submitted"),S=d(""),L=d(!1),V=d(""),N=U(()=>u.error),$=U(()=>{const r=Array.isArray(c.value)?c.value:[],t=r.filter(m=>m.status==="submitted").length,p=r.filter(m=>m.status==="approved").length,C=r.reduce((m,A)=>m+(A.total_hours||0),0);return{pending:t,approved:p,totalHours:C}}),se=U(()=>[{value:1,label:"Gennaio"},{value:2,label:"Febbraio"},{value:3,label:"Marzo"},{value:4,label:"Aprile"},{value:5,label:"Maggio"},{value:6,label:"Giugno"},{value:7,label:"Luglio"},{value:8,label:"Agosto"},{value:9,label:"Settembre"},{value:10,label:"Ottobre"},{value:11,label:"Novembre"},{value:12,label:"Dicembre"}]),x=async()=>{D.value=!0;try{const r={month:B.value,year:new Date().getFullYear(),status:R.value,user_id:S.value,search:V.value,anomalies_only:L.value};c.value=await u.loadPendingTimesheets(r)}finally{D.value=!1}},re=async()=>{G.value=await u.loadTeamMembers()},ae=async()=>{w.value=!0;try{await new Promise(r=>setTimeout(r,2e3)),k.value=[{id:1,user_name:"Mario Rossi",description:"Ore eccessive nel weekend (16h sabato)",confidence:95,type:"weekend_overtime"},{id:2,user_name:"Giulia Bianchi",description:"Pattern insolito: 12h consecutive senza pause",confidence:87,type:"unusual_pattern"}]}catch(r){console.error("Error running anomaly detection:",r)}finally{w.value=!1}},le=r=>{alert(`Anomalia: ${r.description}
Confidenza: ${r.confidence}%`)},oe=async()=>{(await u.bulkApproveTimesheets(n.value)).successful>0&&(await x(),n.value=[]),b.value=!1},de=async()=>{if(!g.value.trim()){u.error="Motivo del rifiuto richiesto per operazioni multiple";return}(await u.bulkRejectTimesheets(n.value,g.value)).successful>0&&(await x(),n.value=[]),b.value=!1},ie=()=>{n.value.length===c.value.length?n.value=[]:n.value=c.value.map(r=>r.id)},P=r=>k.value.some(t=>{var p;return t.user_name===((p=r.user)==null?void 0:p.full_name)}),ne=r=>r?r.split(" ").map(t=>t[0]).join("").toUpperCase():"??",ue=r=>{i.value=r,h.value=!0},E=async r=>{await u.approveTimesheet(r.id)&&(await x(),h.value=!1)},I=r=>{i.value=r,g.value="",h.value=!1,_.value=!0},xe=async()=>{if(!g.value.trim()){u.error="Motivo del rifiuto richiesto";return}await u.rejectTimesheet(i.value.id,g.value)&&(await x(),_.value=!1,i.value=null,g.value="")},ve=()=>{u.clearError()};return ce(()=>{x(),re()}),(r,t)=>{var p,C,m,A,H,Q,Y,q,J;return o(),l("div",ke,[N.value?(o(),l("div",be,[e("div",he,[t[13]||(t[13]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),e("div",fe,[e("p",we,a(N.value),1)]),e("div",{class:"ml-auto pl-3"},[e("div",{class:"-mx-1.5 -my-1.5"},[e("button",{onClick:ve,class:"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900"},t[12]||(t[12]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])])])])):v("",!0),e("div",_e,[e("div",Ce,[t[15]||(t[15]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Approvazioni Timesheet"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Gestisci approvazioni con assistenza AI e operazioni bulk ")],-1)),e("div",Ae,[e("button",{onClick:ae,disabled:w.value,class:"bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},[t[14]||(t[14]=e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})],-1)),X(" "+a(w.value?"Analizzando...":"Rileva Anomalie AI"),1)],8,Me),n.value.length?(o(),l("div",Te,[e("button",{onClick:t[0]||(t[0]=s=>b.value=!b.value),class:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Azioni Multiple ("+a(n.value.length)+") ",1),b.value?(o(),l("div",je,[e("div",{class:"py-1"},[e("button",{onClick:oe,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600"}," Approva Tutti "),e("button",{onClick:de,class:"block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600"}," Rifiuta Tutti ")])])):v("",!0)])):v("",!0)])]),k.value.length?(o(),l("div",ze,[e("div",De,[t[16]||(t[16]=e("svg",{class:"w-5 h-5 text-red-400 mt-0.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"})],-1)),e("div",Be,[e("h3",Re," Anomalie Rilevate ("+a(k.value.length)+") ",1),e("div",Se,[(o(!0),l(T,null,j(k.value,s=>(o(),l("div",{key:s.id,class:"text-sm text-red-700 dark:text-red-300"},[X(" • "+a(s.user_name)+": "+a(s.description)+" ",1),e("button",{onClick:O=>le(s),class:"ml-2 text-red-600 dark:text-red-400 underline"}," Dettagli ",8,Le)]))),128))])])])])):v("",!0)]),e("div",Ve,[e("div",$e,[e("div",Oe,[t[18]||(t[18]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",Ue,[t[17]||(t[17]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Da Approvare",-1)),e("p",Fe,a($.value.pending),1)])])]),e("div",Ge,[e("div",Ne,[t[20]||(t[20]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])])],-1)),e("div",Pe,[t[19]||(t[19]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Approvati",-1)),e("p",Ee,a($.value.approved),1)])])]),e("div",Ie,[e("div",He,[t[22]||(t[22]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"})])])],-1)),e("div",Qe,[t[21]||(t[21]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Con Anomalie",-1)),e("p",Ye,a(k.value.length),1)])])]),e("div",qe,[e("div",Je,[t[24]||(t[24]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",Ke,[t[23]||(t[23]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-gray-400"},"Ore Totali",-1)),e("p",We,a($.value.totalHours)+"h",1)])])])]),e("div",Xe,[e("div",Ze,[e("div",null,[t[25]||(t[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Mese",-1)),y(e("select",{"onUpdate:modelValue":t[1]||(t[1]=s=>B.value=s),onChange:x,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[(o(!0),l(T,null,j(se.value,s=>(o(),l("option",{key:s.value,value:s.value},a(s.label),9,et))),128))],544),[[z,B.value]])]),e("div",null,[t[27]||(t[27]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Stato",-1)),y(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>R.value=s),onChange:x,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},t[26]||(t[26]=[e("option",{value:""},"Tutti",-1),e("option",{value:"submitted"},"Da Approvare",-1),e("option",{value:"approved"},"Approvati",-1),e("option",{value:"rejected"},"Rifiutati",-1)]),544),[[z,R.value]])]),e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Membro Team",-1)),y(e("select",{"onUpdate:modelValue":t[3]||(t[3]=s=>S.value=s),onChange:x,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},[t[28]||(t[28]=e("option",{value:""},"Tutti",-1)),(o(!0),l(T,null,j(G.value,s=>(o(),l("option",{key:s.id,value:s.id},a(s.full_name),9,tt))),128))],544),[[z,S.value]])]),e("div",null,[t[31]||(t[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Anomalie",-1)),y(e("select",{"onUpdate:modelValue":t[4]||(t[4]=s=>L.value=s),onChange:x,class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},t[30]||(t[30]=[e("option",{value:!1},"Tutti",-1),e("option",{value:!0},"Solo con Anomalie",-1)]),544),[[z,L.value]])]),e("div",null,[t[32]||(t[32]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Cerca",-1)),y(e("input",{"onUpdate:modelValue":t[5]||(t[5]=s=>V.value=s),onInput:x,type:"text",placeholder:"Nome utente...",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"},null,544),[[Z,V.value]])])])]),e("div",st,[t[43]||(t[43]=e("div",{class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},[e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"},"Timesheet da Approvare")],-1)),e("div",rt,[e("table",at,[e("thead",lt,[e("tr",null,[e("th",ot,[e("input",{type:"checkbox",onChange:ie,checked:n.value.length===c.value.length&&c.value.length>0,class:"rounded border-gray-300 dark:border-gray-600"},null,40,dt)]),t[33]||(t[33]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Dipendente ",-1)),t[34]||(t[34]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Periodo ",-1)),t[35]||(t[35]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Totali ",-1)),t[36]||(t[36]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Ore Fatturabili ",-1)),t[37]||(t[37]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Stato ",-1)),t[38]||(t[38]=e("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Sottomesso ",-1)),t[39]||(t[39]=e("th",{class:"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"}," Azioni ",-1))])]),e("tbody",it,[D.value?(o(),l("tr",nt,t[40]||(t[40]=[e("td",{colspan:"8",class:"px-6 py-4 text-center text-gray-500 dark:text-gray-400"}," Caricamento... ",-1)]))):c.value.length===0?(o(),l("tr",ut,t[41]||(t[41]=[e("td",{colspan:"8",class:"px-6 py-4 text-center text-gray-500 dark:text-gray-400"}," Nessun timesheet trovato ",-1)]))):(o(!0),l(T,{key:2},j(c.value,s=>{var O,K,W;return o(),l("tr",{key:s.id,class:F({"bg-red-50 dark:bg-red-900/10":P(s)})},[e("td",xt,[y(e("input",{type:"checkbox",value:s.id,"onUpdate:modelValue":t[6]||(t[6]=M=>n.value=M),class:"rounded border-gray-300 dark:border-gray-600"},null,8,vt),[[me,n.value]])]),e("td",ct,[e("div",gt,[e("div",mt,[e("div",pt,[e("span",yt,a(ne((O=s.user)==null?void 0:O.full_name)),1)])]),e("div",kt,[e("div",bt,a((K=s.user)==null?void 0:K.full_name),1),e("div",ht,a((W=s.user)==null?void 0:W.email),1)]),P(s)?(o(),l("div",ft,t[42]||(t[42]=[e("svg",{class:"w-5 h-5 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"})],-1)]))):v("",!0)])]),e("td",wt,a(s.month)+"/"+a(s.year),1),e("td",_t,a(s.total_hours)+"h ",1),e("td",Ct,a(s.billable_hours)+"h ",1),e("td",At,[e("span",{class:F([f(ee)(s.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},a(f(te)(s.status)),3)]),e("td",Mt,a(f(ye)(s.submission_date)),1),e("td",Tt,[e("div",jt,[e("button",{onClick:M=>ue(s),class:"text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"}," Dettagli ",8,zt),s.status==="submitted"?(o(),l("button",{key:0,onClick:M=>E(s),class:"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"}," Approva ",8,Dt)):v("",!0),s.status==="submitted"?(o(),l("button",{key:1,onClick:M=>I(s),class:"text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"}," Rifiuta ",8,Bt)):v("",!0)])])],2)}),128))])])])]),h.value?(o(),l("div",Rt,[e("div",St,[e("div",Lt,[e("div",Vt,[e("h3",$t," Dettagli Timesheet - "+a((C=(p=i.value)==null?void 0:p.user)==null?void 0:C.full_name),1),e("button",{onClick:t[7]||(t[7]=s=>h.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[44]||(t[44]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",Ot,[e("div",Ut,[e("div",null,[t[45]||(t[45]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Periodo",-1)),e("p",Ft,a((m=i.value)==null?void 0:m.month)+"/"+a((A=i.value)==null?void 0:A.year),1)]),e("div",null,[t[46]||(t[46]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Stato",-1)),e("span",{class:F([f(ee)((H=i.value)==null?void 0:H.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},a(f(te)((Q=i.value)==null?void 0:Q.status)),3)]),e("div",null,[t[47]||(t[47]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Ore Totali",-1)),e("p",Gt,a((Y=i.value)==null?void 0:Y.total_hours)+"h",1)]),e("div",null,[t[48]||(t[48]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300"},"Ore Fatturabili",-1)),e("p",Nt,a((q=i.value)==null?void 0:q.billable_hours)+"h",1)])]),((J=i.value)==null?void 0:J.status)==="submitted"?(o(),l("div",Pt,[e("button",{onClick:t[8]||(t[8]=s=>E(i.value)),class:"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Approva "),e("button",{onClick:t[9]||(t[9]=s=>I(i.value)),class:"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Rifiuta ")])):v("",!0)])])])])):v("",!0),_.value?(o(),l("div",Et,[e("div",It,[e("div",Ht,[t[50]||(t[50]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Rifiuta Timesheet ",-1)),e("div",Qt,[e("div",null,[t[49]||(t[49]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Motivo del rifiuto * ",-1)),y(e("textarea",{"onUpdate:modelValue":t[10]||(t[10]=s=>g.value=s),rows:"4",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white",placeholder:"Specifica il motivo del rifiuto..."},null,512),[[Z,g.value]])]),e("div",Yt,[e("button",{onClick:xe,disabled:!g.value.trim(),class:"bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-4 py-2 rounded-md text-sm font-medium"}," Conferma Rifiuto ",8,qt),e("button",{onClick:t[11]||(t[11]=s=>_.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"}," Annulla ")])])])])])):v("",!0)])}}};export{es as default};
