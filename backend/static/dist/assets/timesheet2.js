const s=["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>iu<PERSON>","<PERSON>gli<PERSON>","Agosto","Settembre","Ottobre","Novembre","Dicembre"],o=["<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","Sab"],d=e=>!e||e===0?"0h":e%1===0?`${e}h`:`${e.toFixed(1)}h`,g=e=>new Date(e).toLocaleDateString("it-IT"),c=e=>{switch(e){case"approved":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"submitted":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";case"rejected":return"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";default:return"bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"}},u=e=>{switch(e){case"approved":return"Approvato";case"submitted":return"In Attesa";case"rejected":return"Rifiutato";default:return"Bozza"}},i=(e,t,r)=>{const a=new Date;return a.getDate()===e&&a.getMonth()+1===t&&a.getFullYear()===r},b=(e,t,r)=>{const n=new Date(r,t-1,e).getDay();return n===0||n===6},l=(e,t,r)=>{const a=new Date(r,t-1,e);return o[a.getDay()]};export{i as a,c as b,u as c,g as d,d as f,l as g,b as i,s as m};
