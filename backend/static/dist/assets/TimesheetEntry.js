import{r as A,f as d,A as Y,c as a,g as y,j as t,t as r,p as n,F as b,k,v as z,G as q,C as W,o as l,n as B}from"./vendor.js";import{u as J}from"./timesheet.js";import{m as N,f as h,i as S,a as T,g as Q}from"./timesheet2.js";import"./app.js";const X={class:"space-y-6"},Z={key:0,class:"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4"},K={class:"flex"},R={class:"ml-3"},tt={class:"text-sm text-red-800 dark:text-red-200"},et={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},st={class:"flex justify-between items-center"},rt={class:"flex items-center space-x-4"},ot={class:"flex items-center space-x-2"},at={class:"text-center"},lt={class:"text-lg font-semibold text-gray-900 dark:text-white"},dt={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},it={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},nt={class:"flex items-center justify-between"},ut={class:"text-lg font-medium text-gray-900 dark:text-white"},gt={class:"text-sm text-gray-500 dark:text-gray-400"},ct={class:"overflow-x-auto"},xt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},vt={class:"bg-gray-50 dark:bg-gray-700"},mt={class:"text-xs text-gray-400"},ht={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},yt={key:0},bt=["colspan"],kt={class:"space-y-2"},pt={class:"sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600"},ft={class:"flex items-center justify-between"},wt={class:"min-w-0 flex-1"},_t={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},jt={key:0,class:"text-xs text-gray-500 dark:text-gray-400 truncate"},Mt={class:"text-xs text-gray-500 dark:text-gray-400"},Ct={key:0,class:"text-sm font-medium text-gray-900 dark:text-white"},Pt={key:1,class:"text-gray-300 dark:text-gray-600"},At={class:"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 border-l border-gray-200 dark:border-gray-600"},zt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Bt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Nt={class:"flex items-center"},St={class:"ml-5 w-0 flex-1"},Tt={class:"text-lg font-medium text-gray-900 dark:text-white"},Ft={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ot={class:"flex items-center"},Dt={class:"ml-5 w-0 flex-1"},Et={class:"text-lg font-medium text-gray-900 dark:text-white"},Gt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ht={class:"flex items-center"},Lt={class:"ml-5 w-0 flex-1"},$t={class:"text-lg font-medium text-gray-900 dark:text-white"},Vt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},It={class:"flex items-center"},Ut={class:"ml-5 w-0 flex-1"},Yt={class:"text-lg font-medium text-gray-900 dark:text-white"},qt={key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50"},Wt={class:"relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800"},Jt={class:"mt-3"},Qt={class:"flex items-center justify-between mb-4"},Xt={class:"space-y-4"},Zt=["value"],Kt={class:"flex items-center"},Rt={class:"flex justify-end space-x-3"},te=["disabled"],le={__name:"TimesheetEntry",setup(ee){const o=J(),v=A(!1),g=A({project_id:"",task_id:"",billable:!0}),c=d(()=>o.currentYear),x=d(()=>o.currentMonth),F=d(()=>o.projectTasks),O=d(()=>o.monthlyEntries),D=d(()=>o.availableProjects),f=d(()=>o.loading.monthlyData||o.loading.saving),w=d(()=>o.error),p=d(()=>o.daysInMonth),_=d(()=>o.totalHours),j=d(()=>o.billableHours),E=d(()=>o.pendingHours),G=d(()=>o.activeProjects),M=d(()=>{const u={};return Object.entries(O.value).forEach(([e,s])=>{Object.entries(s).forEach(([i,P])=>{if(!u[i]){const m=F.value.find(U=>U.id===i);u[i]={taskId:i,taskName:(m==null?void 0:m.task_name)||"Attività Generica",projectName:(m==null?void 0:m.project_name)||"Progetto Sconosciuto",assignees:"Tu",hours:{},total:0}}u[i].hours[e]=parseFloat(P||0).toFixed(1),u[i].total+=parseFloat(P||0)})}),Object.values(u).map(e=>({...e,total:e.total.toFixed(1)}))}),H=()=>{o.navigateMonth("previous")},L=()=>{o.navigateMonth("next")},$=()=>{o.clearError()},V=async()=>{if(!g.value.project_id)return;await o.addProjectToTimesheet(g.value.project_id,g.value.task_id)&&(v.value=!1,g.value={project_id:"",task_id:"",billable:!0})},C=u=>`${c.value}-${x.value.toString().padStart(2,"0")}-${u.toString().padStart(2,"0")}`,I=u=>["Gennaio","Febbraio","Marzo","Aprile","Maggio","Giugno","Luglio","Agosto","Settembre","Ottobre","Novembre","Dicembre"][u-1];return Y(async()=>{await Promise.all([o.loadAvailableProjects(),o.loadMonthlyData()])}),(u,e)=>(l(),a("div",X,[w.value?(l(),a("div",Z,[t("div",K,[e[7]||(e[7]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-5 w-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),t("div",R,[t("p",tt,r(w.value),1)]),t("div",{class:"ml-auto pl-3"},[t("div",{class:"-mx-1.5 -my-1.5"},[t("button",{onClick:$,class:"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900"},e[6]||(e[6]=[t("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])])])])):y("",!0),t("div",et,[t("div",st,[e[10]||(e[10]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Le Mie Ore"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Registra le tue ore di lavoro con la griglia mensile ")],-1)),t("div",rt,[t("div",ot,[t("button",{onClick:H,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[8]||(e[8]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),t("div",at,[t("h2",lt,r(n(N)[x.value-1])+" "+r(c.value),1)]),t("button",{onClick:L,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[9]||(e[9]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))]),t("button",{onClick:e[0]||(e[0]=s=>v.value=!0),class:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"}," Aggiungi Progetto ")])])]),t("div",dt,[t("div",it,[t("div",nt,[t("h3",ut," Griglia Ore - "+r(n(N)[x.value-1])+" "+r(c.value),1),t("div",gt," Totale: "+r(n(h)(_.value))+" | Fatturabili: "+r(n(h)(j.value)),1)])]),t("div",ct,[t("table",xt,[t("thead",vt,[t("tr",null,[e[11]||(e[11]=t("th",{class:"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600"}," Progetto/Task ",-1)),(l(!0),a(b,null,k(p.value,s=>(l(),a("th",{key:s,class:B(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]",{"bg-blue-50 dark:bg-blue-900/20":n(T)(s,x.value,c.value),"bg-red-50 dark:bg-red-900/20":n(S)(s,x.value,c.value)}])},[t("div",null,r(s),1),t("div",mt,r(n(Q)(s,x.value,c.value)),1)],2))),128)),e[12]||(e[12]=t("th",{class:"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600"}," Totale ",-1))])]),t("tbody",ht,[M.value.length===0?(l(),a("tr",yt,[t("td",{colspan:p.value.length+2,class:"px-6 py-8 text-center text-gray-500 dark:text-gray-400"},[t("div",kt,[t("p",null,"Nessun timesheet registrato per "+r(I(x.value))+" "+r(c.value),1),t("button",{onClick:e[1]||(e[1]=s=>v.value=!0),class:"text-primary-600 hover:text-primary-700 dark:text-primary-400"}," Registra le tue prime ore ")])],8,bt)])):y("",!0),(l(!0),a(b,null,k(M.value,s=>(l(),a("tr",{key:s.taskId},[t("td",pt,[t("div",ft,[t("div",wt,[t("p",_t,r(s.projectName),1),s.taskName?(l(),a("p",jt,r(s.taskName),1)):y("",!0),t("p",Mt,r(s.assignees),1)])])]),(l(!0),a(b,null,k(p.value,i=>(l(),a("td",{key:i,class:B(["px-1 py-3 text-center",{"bg-blue-50 dark:bg-blue-900/20":n(T)(i,x.value,c.value),"bg-red-50 dark:bg-red-900/20":n(S)(i,x.value,c.value)}])},[s.hours[C(i)]?(l(),a("div",Ct,r(s.hours[C(i)]),1)):(l(),a("div",Pt,"-"))],2))),128)),t("td",At,r(s.total),1)]))),128))])])])]),t("div",zt,[t("div",Bt,[t("div",Nt,[e[14]||(e[14]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",St,[t("dl",null,[e[13]||(e[13]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Totali ",-1)),t("dd",Tt,r(n(h)(_.value)),1)])])])]),t("div",Ft,[t("div",Ot,[e[16]||(e[16]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",Dt,[t("dl",null,[e[15]||(e[15]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Fatturabili ",-1)),t("dd",Et,r(n(h)(j.value)),1)])])])]),t("div",Gt,[t("div",Ht,[e[18]||(e[18]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",Lt,[t("dl",null,[e[17]||(e[17]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," In Attesa ",-1)),t("dd",$t,r(n(h)(E.value)),1)])])])]),t("div",Vt,[t("div",It,[e[20]||(e[20]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),t("div",Ut,[t("dl",null,[e[19]||(e[19]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Progetti Attivi ",-1)),t("dd",Yt,r(G.value),1)])])])])]),v.value?(l(),a("div",qt,[t("div",Wt,[t("div",Jt,[t("div",Qt,[e[22]||(e[22]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white"}," Aggiungi Progetto alla Griglia ",-1)),t("button",{onClick:e[2]||(e[2]=s=>v.value=!1),class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[21]||(e[21]=[t("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),t("div",Xt,[t("div",null,[e[24]||(e[24]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"}," Progetto * ",-1)),z(t("select",{"onUpdate:modelValue":e[3]||(e[3]=s=>g.value.project_id=s),class:"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:""},[e[23]||(e[23]=t("option",{value:""},"Seleziona progetto...",-1)),(l(!0),a(b,null,k(D.value,s=>(l(),a("option",{key:s.id,value:s.id},r(s.name),9,Zt))),128))],512),[[q,g.value.project_id]])]),t("div",null,[t("label",Kt,[z(t("input",{type:"checkbox","onUpdate:modelValue":e[4]||(e[4]=s=>g.value.billable=s),class:"rounded border-gray-300 text-primary-600 focus:ring-primary-500"},null,512),[[W,g.value.billable]]),e[25]||(e[25]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Ore fatturabili",-1))])]),t("div",Rt,[t("button",{onClick:V,disabled:!g.value.project_id||f.value,class:"bg-primary-600 hover:bg-primary-700 disabled:opacity-50 text-white px-4 py-2 rounded-md text-sm font-medium"},r(f.value?"Aggiunta...":"Aggiungi"),9,te),t("button",{onClick:e[5]||(e[5]=s=>v.value=!1),class:"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"}," Annulla ")])])])])])):y("",!0)]))}};export{le as default};
