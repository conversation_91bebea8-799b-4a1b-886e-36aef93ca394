import{r as w,f as d,A as ve,c as l,g as y,j as e,t as o,p as c,m as xe,F as _,k as j,H as be,s as U,v as x,G as D,x as F,P as G,o as i,n as T}from"./vendor.js";import{u as pe}from"./timesheet.js";import{a as ye,u as he,c as E}from"./app.js";import{m as L,f as M,i as $,a as q,g as fe}from"./timesheet2.js";const ke={class:"space-y-6"},we={key:0,class:"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4"},_e={class:"flex"},je={class:"ml-3"},Me={class:"text-sm text-red-800 dark:text-red-200"},Se={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Ce={class:"flex justify-between items-center"},Ne={class:"flex items-center space-x-4"},Fe={class:"flex items-center space-x-2"},Pe={class:"text-center"},ze={class:"text-lg font-semibold text-gray-900 dark:text-white"},Ae={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Te={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},Ee={class:"flex items-center justify-between"},Ve={class:"text-lg font-medium text-gray-900 dark:text-white"},Be={class:"text-sm text-gray-500 dark:text-gray-400"},He={class:"overflow-x-auto"},Oe={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Ie={class:"bg-gray-50 dark:bg-gray-700"},Ue={class:"text-xs text-gray-400"},De={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Ge={key:0},Le=["colspan"],$e={class:"space-y-2"},qe={class:"sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600"},Ye={class:"flex items-center justify-between"},We={class:"min-w-0 flex-1"},Je={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},Qe={key:0,class:"text-xs text-gray-500 dark:text-gray-400 truncate"},Xe={class:"text-xs text-gray-500 dark:text-gray-400"},Ze={key:0,class:"flex flex-col items-center"},Ke={class:"text-sm font-medium text-gray-900 dark:text-white"},Re={class:"flex space-x-1 mt-1"},et=["title"],tt={key:1,class:"text-gray-300 dark:text-gray-600"},rt={class:"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 border-l border-gray-200 dark:border-gray-600"},st={key:0,class:"mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-md"},at={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},ot={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},lt={class:"flex items-center"},it={class:"ml-5 w-0 flex-1"},dt={class:"text-lg font-medium text-gray-900 dark:text-white"},nt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ut={class:"flex items-center"},gt={class:"ml-5 w-0 flex-1"},ct={class:"text-lg font-medium text-gray-900 dark:text-white"},mt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},vt={class:"flex items-center"},xt={class:"ml-5 w-0 flex-1"},bt={class:"text-lg font-medium text-gray-900 dark:text-white"},pt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},yt={class:"flex items-center"},ht={class:"ml-5 w-0 flex-1"},ft={class:"text-lg font-medium text-gray-900 dark:text-white"},kt={class:"mt-3"},wt={class:"grid grid-cols-1 gap-4"},_t=["value"],jt=["value"],Mt={key:0,class:"border-t border-gray-200 dark:border-gray-600 pt-4"},St={class:"space-y-3"},Ct={class:"flex items-center justify-between"},Nt={class:"flex items-center"},Ft={class:"flex items-center"},Pt={key:0},zt={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},At={class:"flex justify-end space-x-3 mt-6"},Tt=["disabled"],It={__name:"TimesheetEntry",setup(Et){const n=pe(),{isManager:Y,isAdmin:W}=ye(),J=he(),S=w(!1),k=w([]),b=w([]),C=w(!1),a=w({project_id:"",task_id:"",date:new Date().toISOString().split("T")[0],hours:0,description:"",billable:!0,billing_rate:null}),m=d(()=>n.currentYear),v=d(()=>n.currentMonth),Q=d(()=>n.projectTasks),X=d(()=>n.monthlyEntries);d(()=>n.availableProjects),d(()=>n.loading.monthlyData||n.loading.saving);const V=d(()=>n.error),B=d(()=>Y.value||W.value),H=d(()=>b.value.find(s=>s.id===parseInt(a.value.project_id))),P=d(()=>n.daysInMonth),Z=d(()=>h.value.reduce((s,t)=>s+parseFloat(t.total||0),0)),K=d(()=>h.value.reduce((s,t)=>s+Object.entries(t.billing||{}).reduce((p,[u,r])=>r&&t.hours[u]?p+parseFloat(t.hours[u]||0):p,0),0)),R=d(()=>0),ee=d(()=>{const s=new Set;return h.value.forEach(t=>{t.projectName&&t.projectName!=="Progetto Sconosciuto"&&s.add(t.projectName)}),s.size}),O=d(()=>Z.value),I=d(()=>K.value),te=d(()=>R.value),re=d(()=>ee.value),h=d(()=>{const s={};return Object.entries(X.value).forEach(([t,p])=>{Object.entries(p).forEach(([u,r])=>{if(!s[u]){const f=Q.value.find(me=>me.id===u);s[u]={taskId:u,taskName:(f==null?void 0:f.task_name)||"Attività Generica",projectName:(f==null?void 0:f.project_name)||"Progetto Sconosciuto",assignees:"Tu",hours:{},billing:{},total:0}}let g=0,A=!1;typeof r=="object"&&r!==null?(g=parseFloat(r.hours||0),A=r.billable||!1):(g=parseFloat(r||0),A=!1),s[u].hours[t]=g.toFixed(1),s[u].billing[t]=A,s[u].total+=g})}),Object.values(s).map(t=>({...t,total:t.total.toFixed(1)}))}),se=()=>{n.navigateMonth("previous")},ae=()=>{n.navigateMonth("next")},oe=()=>{n.clearError()},z=()=>{S.value=!1,le(),k.value=[]},le=()=>{a.value={project_id:"",task_id:"",date:new Date().toISOString().split("T")[0],hours:0,description:"",billable:!0,billing_rate:null}},ie=async()=>{var s;if(a.value.task_id="",k.value=[],a.value.project_id){await de(a.value.project_id);const t=H.value;(s=t==null?void 0:t.contract)!=null&&s.hourly_rate&&B.value&&(a.value.billing_rate=t.contract.hourly_rate)}},de=async s=>{try{const t=await E.get(`/api/tasks?project_id=${s}&status=open`);t.data.success&&(k.value=t.data.data.tasks||[])}catch(t){console.error("Error loading tasks:",t),k.value=[]}},ne=async()=>{C.value=!0;try{const s={user_id:J.user.id,project_id:parseInt(a.value.project_id),task_id:a.value.task_id?parseInt(a.value.task_id):null,date:a.value.date,hours:parseFloat(a.value.hours),description:a.value.description,billable:a.value.billable,billing_rate:a.value.billable&&a.value.billing_rate?parseFloat(a.value.billing_rate):null},t=await E.post("/api/timesheets",s);if(t.data.success)z(),await n.loadMonthlyData();else throw new Error(t.data.message||"Errore durante il salvataggio")}catch(s){console.error("Error saving hours:",s)}finally{C.value=!1}},N=s=>`${m.value}-${v.value.toString().padStart(2,"0")}-${s.toString().padStart(2,"0")}`,ue=s=>["Gennaio","Febbraio","Marzo","Aprile","Maggio","Giugno","Luglio","Agosto","Settembre","Ottobre","Novembre","Dicembre"][s-1],ge=s=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(s||0),ce=async()=>{try{const s=await E.get("/api/projects");s.data.success&&(s.data.data.items?b.value=s.data.data.items:s.data.data.projects?b.value=s.data.data.projects:Array.isArray(s.data.data)?b.value=s.data.data:b.value=[])}catch(s){console.error("Error loading user projects:",s),b.value=[]}};return ve(async()=>{await Promise.all([n.loadAvailableProjects(),n.loadMonthlyData(),ce()])}),(s,t)=>{var p,u;return i(),l("div",ke,[V.value?(i(),l("div",we,[e("div",_e,[t[12]||(t[12]=e("div",{class:"flex-shrink-0"},[e("svg",{class:"h-5 w-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),e("div",je,[e("p",Me,o(V.value),1)]),e("div",{class:"ml-auto pl-3"},[e("div",{class:"-mx-1.5 -my-1.5"},[e("button",{onClick:oe,class:"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900"},t[11]||(t[11]=[e("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])])])])):y("",!0),e("div",Se,[e("div",Ce,[t[16]||(t[16]=e("div",null,[e("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Le Mie Ore"),e("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Registra le tue ore di lavoro con la griglia mensile ")],-1)),e("div",Ne,[e("div",Fe,[e("button",{onClick:se,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[13]||(t[13]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),e("div",Pe,[e("h2",ze,o(c(L)[v.value-1])+" "+o(m.value),1)]),e("button",{onClick:ae,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},t[14]||(t[14]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))]),e("button",{onClick:t[0]||(t[0]=r=>S.value=!0),class:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},t[15]||(t[15]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),xe(" Aggiungi Ore ")]))])])]),e("div",Ae,[e("div",Te,[e("div",Ee,[e("h3",Ve," Griglia Ore - "+o(c(L)[v.value-1])+" "+o(m.value),1),e("div",Be," Totale: "+o(c(M)(O.value))+" | Fatturabili: "+o(c(M)(I.value)),1)])]),e("div",He,[e("table",Oe,[e("thead",Ie,[e("tr",null,[t[17]||(t[17]=e("th",{class:"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600"}," Progetto/Task ",-1)),(i(!0),l(_,null,j(P.value,r=>(i(),l("th",{key:r,class:T(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]",{"bg-blue-50 dark:bg-blue-900/20":c(q)(r,v.value,m.value),"bg-red-50 dark:bg-red-900/20":c($)(r,v.value,m.value)}])},[e("div",null,o(r),1),e("div",Ue,o(c(fe)(r,v.value,m.value)),1)],2))),128)),t[18]||(t[18]=e("th",{class:"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600"}," Totale ",-1))])]),e("tbody",De,[h.value.length===0?(i(),l("tr",Ge,[e("td",{colspan:P.value.length+2,class:"px-6 py-8 text-center text-gray-500 dark:text-gray-400"},[e("div",$e,[e("p",null,"Nessun timesheet registrato per "+o(ue(v.value))+" "+o(m.value),1),e("button",{onClick:t[1]||(t[1]=r=>S.value=!0),class:"text-primary-600 hover:text-primary-700 dark:text-primary-400"}," Registra le tue prime ore ")])],8,Le)])):y("",!0),(i(!0),l(_,null,j(h.value,r=>(i(),l("tr",{key:r.taskId},[e("td",qe,[e("div",Ye,[e("div",We,[e("p",Je,o(r.projectName),1),r.taskName?(i(),l("p",Qe,o(r.taskName),1)):y("",!0),e("p",Xe,o(r.assignees),1)])])]),(i(!0),l(_,null,j(P.value,g=>(i(),l("td",{key:g,class:T(["px-1 py-3 text-center",{"bg-blue-50 dark:bg-blue-900/20":c(q)(g,v.value,m.value),"bg-red-50 dark:bg-red-900/20":c($)(g,v.value,m.value)}])},[r.hours[N(g)]?(i(),l("div",Ze,[e("span",Ke,o(r.hours[N(g)]),1),e("div",Re,[e("div",{class:T(["w-1.5 h-1.5 rounded-full",r.billing&&r.billing[N(g)]?"bg-green-500":"bg-gray-300"]),title:r.billing&&r.billing[N(g)]?"Fatturabile":"Non fatturabile"},null,10,et)])])):(i(),l("div",tt,"-"))],2))),128)),e("td",rt,o(r.total),1)]))),128))])])]),h.value.length>0?(i(),l("div",st,t[19]||(t[19]=[be('<div class="flex items-center space-x-4 text-xs text-gray-600 dark:text-gray-300"><span class="font-medium">Legenda:</span><div class="flex items-center space-x-1"><div class="w-1.5 h-1.5 rounded-full bg-green-500"></div><span>Fatturabile</span></div><div class="flex items-center space-x-1"><div class="w-1.5 h-1.5 rounded-full bg-gray-300"></div><span>Non fatturabile</span></div></div>',1)]))):y("",!0)]),e("div",at,[e("div",ot,[e("div",lt,[t[21]||(t[21]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",it,[e("dl",null,[t[20]||(t[20]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Totali ",-1)),e("dd",dt,o(c(M)(O.value)),1)])])])]),e("div",nt,[e("div",ut,[t[23]||(t[23]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",gt,[e("dl",null,[t[22]||(t[22]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Fatturabili ",-1)),e("dd",ct,o(c(M)(I.value)),1)])])])]),e("div",mt,[e("div",vt,[t[25]||(t[25]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",xt,[e("dl",null,[t[24]||(t[24]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," In Attesa ",-1)),e("dd",bt,o(c(M)(te.value)),1)])])])]),e("div",pt,[e("div",yt,[t[27]||(t[27]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),e("div",ht,[e("dl",null,[t[26]||(t[26]=e("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Progetti Attivi ",-1)),e("dd",ft,o(re.value),1)])])])])]),S.value?(i(),l("div",{key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:z},[e("div",{class:"relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:t[10]||(t[10]=U(()=>{},["stop"]))},[e("div",kt,[t[39]||(t[39]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Aggiungi Ore ",-1)),e("form",{onSubmit:U(ne,["prevent"])},[e("div",wt,[e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Progetto",-1)),x(e("select",{"onUpdate:modelValue":t[2]||(t[2]=r=>a.value.project_id=r),onChange:ie,required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[t[28]||(t[28]=e("option",{value:""},"Seleziona progetto",-1)),(i(!0),l(_,null,j(b.value,r=>(i(),l("option",{key:r.id,value:r.id},o(r.name),9,_t))),128))],544),[[D,a.value.project_id]])]),e("div",null,[t[31]||(t[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Task",-1)),x(e("select",{"onUpdate:modelValue":t[3]||(t[3]=r=>a.value.task_id=r),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[t[30]||(t[30]=e("option",{value:""},"Seleziona task",-1)),(i(!0),l(_,null,j(k.value,r=>(i(),l("option",{key:r.id,value:r.id},o(r.name),9,jt))),128))],512),[[D,a.value.task_id]])]),e("div",null,[t[32]||(t[32]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Data",-1)),x(e("input",{"onUpdate:modelValue":t[4]||(t[4]=r=>a.value.date=r),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[F,a.value.date]])]),e("div",null,[t[33]||(t[33]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore",-1)),x(e("input",{"onUpdate:modelValue":t[5]||(t[5]=r=>a.value.hours=r),type:"number",step:"0.25",min:"0",max:"24",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[F,a.value.hours]])]),e("div",null,[t[34]||(t[34]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),x(e("textarea",{"onUpdate:modelValue":t[6]||(t[6]=r=>a.value.description=r),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[F,a.value.description]])]),B.value?(i(),l("div",Mt,[t[38]||(t[38]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-3"},"Informazioni Fatturazione",-1)),e("div",St,[e("div",Ct,[e("label",Nt,[x(e("input",{type:"radio","onUpdate:modelValue":t[7]||(t[7]=r=>a.value.billable=r),value:!0,class:"text-primary-600 focus:ring-primary-500"},null,512),[[G,a.value.billable]]),t[35]||(t[35]=e("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Sì - Fatturabile",-1))]),e("label",Ft,[x(e("input",{type:"radio","onUpdate:modelValue":t[8]||(t[8]=r=>a.value.billable=r),value:!1,class:"text-primary-600 focus:ring-primary-500"},null,512),[[G,a.value.billable]]),t[36]||(t[36]=e("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"No - Non fatturabile",-1))])]),a.value.billable?(i(),l("div",Pt,[t[37]||(t[37]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tariffa (€/h) ",-1)),x(e("input",{"onUpdate:modelValue":t[9]||(t[9]=r=>a.value.billing_rate=r),type:"number",step:"0.01",min:"0",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[F,a.value.billing_rate]]),e("p",zt," Tariffa contrattuale: "+o(ge(((u=(p=H.value)==null?void 0:p.contract)==null?void 0:u.hourly_rate)||0)),1)])):y("",!0)])])):y("",!0)]),e("div",At,[e("button",{type:"button",onClick:z,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),e("button",{type:"submit",disabled:C.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(C.value?"Salvataggio...":"Aggiungi"),9,Tt)])],32)])])])):y("",!0)])}}};export{It as default};
