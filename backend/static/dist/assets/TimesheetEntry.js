import{r as p,f as n,A as dt,c as i,g as y,j as t,t as o,p as g,m as nt,F as k,k as h,s as D,v as x,G as H,x as C,P as I,o as d,n as O}from"./vendor.js";import{u as ut}from"./timesheet.js";import{a as gt,u as ct,c as T}from"./app.js";import{m as U,f,i as G,a as L,g as mt}from"./timesheet2.js";const vt={class:"space-y-6"},xt={key:0,class:"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4"},yt={class:"flex"},bt={class:"ml-3"},pt={class:"text-sm text-red-800 dark:text-red-200"},kt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ht={class:"flex justify-between items-center"},ft={class:"flex items-center space-x-4"},wt={class:"flex items-center space-x-2"},_t={class:"text-center"},jt={class:"text-lg font-semibold text-gray-900 dark:text-white"},Mt={class:"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden"},Ct={class:"px-6 py-4 border-b border-gray-200 dark:border-gray-700"},St={class:"flex items-center justify-between"},Ft={class:"text-lg font-medium text-gray-900 dark:text-white"},Tt={class:"text-sm text-gray-500 dark:text-gray-400"},zt={class:"overflow-x-auto"},Nt={class:"min-w-full divide-y divide-gray-200 dark:divide-gray-700"},Pt={class:"bg-gray-50 dark:bg-gray-700"},At={class:"text-xs text-gray-400"},Et={class:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"},Vt={key:0},Bt=["colspan"],Dt={class:"space-y-2"},Ht={class:"sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600"},It={class:"flex items-center justify-between"},Ot={class:"min-w-0 flex-1"},Ut={class:"text-sm font-medium text-gray-900 dark:text-white truncate"},Gt={key:0,class:"text-xs text-gray-500 dark:text-gray-400 truncate"},Lt={class:"text-xs text-gray-500 dark:text-gray-400"},$t={key:0,class:"text-sm font-medium text-gray-900 dark:text-white"},qt={key:1,class:"text-gray-300 dark:text-gray-600"},Yt={class:"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 border-l border-gray-200 dark:border-gray-600"},Wt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Jt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Qt={class:"flex items-center"},Xt={class:"ml-5 w-0 flex-1"},Zt={class:"text-lg font-medium text-gray-900 dark:text-white"},Rt={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},Kt={class:"flex items-center"},te={class:"ml-5 w-0 flex-1"},ee={class:"text-lg font-medium text-gray-900 dark:text-white"},re={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},se={class:"flex items-center"},ae={class:"ml-5 w-0 flex-1"},oe={class:"text-lg font-medium text-gray-900 dark:text-white"},le={class:"bg-white dark:bg-gray-800 shadow rounded-lg p-6"},ie={class:"flex items-center"},de={class:"ml-5 w-0 flex-1"},ne={class:"text-lg font-medium text-gray-900 dark:text-white"},ue={class:"mt-3"},ge={class:"grid grid-cols-1 gap-4"},ce=["value"],me=["value"],ve={key:0,class:"border-t border-gray-200 dark:border-gray-600 pt-4"},xe={class:"space-y-3"},ye={class:"flex items-center justify-between"},be={class:"flex items-center"},pe={class:"flex items-center"},ke={key:0},he={class:"text-xs text-gray-500 dark:text-gray-400 mt-1"},fe={class:"flex justify-end space-x-3 mt-6"},we=["disabled"],Fe={__name:"TimesheetEntry",setup(_e){const l=ut(),{isManager:$,isAdmin:q}=gt(),Y=ct(),w=p(!1),b=p([]),_=p([]),j=p(!1),s=p({project_id:"",task_id:"",date:new Date().toISOString().split("T")[0],hours:0,description:"",billable:!0,billing_rate:null}),c=n(()=>l.currentYear),m=n(()=>l.currentMonth),W=n(()=>l.projectTasks),J=n(()=>l.monthlyEntries);n(()=>l.availableProjects),n(()=>l.loading.monthlyData||l.loading.saving);const z=n(()=>l.error),N=n(()=>$.value||q.value),P=n(()=>_.value.find(a=>a.id===parseInt(s.value.project_id))),S=n(()=>l.daysInMonth),A=n(()=>l.totalHours),E=n(()=>l.billableHours),Q=n(()=>l.pendingHours),X=n(()=>l.activeProjects),V=n(()=>{const a={};return Object.entries(J.value).forEach(([e,M])=>{Object.entries(M).forEach(([v,r])=>{if(!a[v]){const u=W.value.find(it=>it.id===v);a[v]={taskId:v,taskName:(u==null?void 0:u.task_name)||"Attività Generica",projectName:(u==null?void 0:u.project_name)||"Progetto Sconosciuto",assignees:"Tu",hours:{},total:0}}a[v].hours[e]=parseFloat(r||0).toFixed(1),a[v].total+=parseFloat(r||0)})}),Object.values(a).map(e=>({...e,total:e.total.toFixed(1)}))}),Z=()=>{l.navigateMonth("previous")},R=()=>{l.navigateMonth("next")},K=()=>{l.clearError()},F=()=>{w.value=!1,tt(),b.value=[]},tt=()=>{s.value={project_id:"",task_id:"",date:new Date().toISOString().split("T")[0],hours:0,description:"",billable:!0,billing_rate:null}},et=async()=>{var a;if(s.value.task_id="",b.value=[],s.value.project_id){await rt(s.value.project_id);const e=P.value;(a=e==null?void 0:e.contract)!=null&&a.hourly_rate&&N.value&&(s.value.billing_rate=e.contract.hourly_rate)}},rt=async a=>{try{const e=await T.get(`/api/tasks?project_id=${a}&status=open`);e.data.success&&(b.value=e.data.data.tasks||[])}catch(e){console.error("Error loading tasks:",e),b.value=[]}},st=async()=>{j.value=!0;try{const a={user_id:Y.user.id,project_id:parseInt(s.value.project_id),task_id:s.value.task_id?parseInt(s.value.task_id):null,date:s.value.date,hours:parseFloat(s.value.hours),description:s.value.description,billable:s.value.billable,billing_rate:s.value.billable&&s.value.billing_rate?parseFloat(s.value.billing_rate):null},e=await T.post("/api/timesheets",a);if(e.data.success)F(),await l.loadMonthlyData();else throw new Error(e.data.message||"Errore durante il salvataggio")}catch(a){console.error("Error saving hours:",a)}finally{j.value=!1}},B=a=>`${c.value}-${m.value.toString().padStart(2,"0")}-${a.toString().padStart(2,"0")}`,at=a=>["Gennaio","Febbraio","Marzo","Aprile","Maggio","Giugno","Luglio","Agosto","Settembre","Ottobre","Novembre","Dicembre"][a-1],ot=a=>new Intl.NumberFormat("it-IT",{style:"currency",currency:"EUR"}).format(a||0),lt=async()=>{try{const a=await T.get("/api/projects/user");a.data.success&&(_.value=a.data.data.projects||[])}catch(a){console.error("Error loading user projects:",a),_.value=[]}};return dt(async()=>{await Promise.all([l.loadAvailableProjects(),l.loadMonthlyData(),lt()])}),(a,e)=>{var M,v;return d(),i("div",vt,[z.value?(d(),i("div",xt,[t("div",yt,[e[12]||(e[12]=t("div",{class:"flex-shrink-0"},[t("svg",{class:"h-5 w-5 text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"})])],-1)),t("div",bt,[t("p",pt,o(z.value),1)]),t("div",{class:"ml-auto pl-3"},[t("div",{class:"-mx-1.5 -my-1.5"},[t("button",{onClick:K,class:"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900"},e[11]||(e[11]=[t("svg",{class:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])])])])):y("",!0),t("div",kt,[t("div",ht,[e[16]||(e[16]=t("div",null,[t("h1",{class:"text-2xl font-bold text-gray-900 dark:text-white"},"Le Mie Ore"),t("p",{class:"mt-1 text-sm text-gray-600 dark:text-gray-400"}," Registra le tue ore di lavoro con la griglia mensile ")],-1)),t("div",ft,[t("div",wt,[t("button",{onClick:Z,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[13]||(e[13]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),t("div",_t,[t("h2",jt,o(g(U)[m.value-1])+" "+o(c.value),1)]),t("button",{onClick:R,class:"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"},e[14]||(e[14]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))]),t("button",{onClick:e[0]||(e[0]=r=>w.value=!0),class:"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"},e[15]||(e[15]=[t("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),nt(" Aggiungi Ore ")]))])])]),t("div",Mt,[t("div",Ct,[t("div",St,[t("h3",Ft," Griglia Ore - "+o(g(U)[m.value-1])+" "+o(c.value),1),t("div",Tt," Totale: "+o(g(f)(A.value))+" | Fatturabili: "+o(g(f)(E.value)),1)])]),t("div",zt,[t("table",Nt,[t("thead",Pt,[t("tr",null,[e[17]||(e[17]=t("th",{class:"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600"}," Progetto/Task ",-1)),(d(!0),i(k,null,h(S.value,r=>(d(),i("th",{key:r,class:O(["px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]",{"bg-blue-50 dark:bg-blue-900/20":g(L)(r,m.value,c.value),"bg-red-50 dark:bg-red-900/20":g(G)(r,m.value,c.value)}])},[t("div",null,o(r),1),t("div",At,o(g(mt)(r,m.value,c.value)),1)],2))),128)),e[18]||(e[18]=t("th",{class:"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600"}," Totale ",-1))])]),t("tbody",Et,[V.value.length===0?(d(),i("tr",Vt,[t("td",{colspan:S.value.length+2,class:"px-6 py-8 text-center text-gray-500 dark:text-gray-400"},[t("div",Dt,[t("p",null,"Nessun timesheet registrato per "+o(at(m.value))+" "+o(c.value),1),t("button",{onClick:e[1]||(e[1]=r=>w.value=!0),class:"text-primary-600 hover:text-primary-700 dark:text-primary-400"}," Registra le tue prime ore ")])],8,Bt)])):y("",!0),(d(!0),i(k,null,h(V.value,r=>(d(),i("tr",{key:r.taskId},[t("td",Ht,[t("div",It,[t("div",Ot,[t("p",Ut,o(r.projectName),1),r.taskName?(d(),i("p",Gt,o(r.taskName),1)):y("",!0),t("p",Lt,o(r.assignees),1)])])]),(d(!0),i(k,null,h(S.value,u=>(d(),i("td",{key:u,class:O(["px-1 py-3 text-center",{"bg-blue-50 dark:bg-blue-900/20":g(L)(u,m.value,c.value),"bg-red-50 dark:bg-red-900/20":g(G)(u,m.value,c.value)}])},[r.hours[B(u)]?(d(),i("div",$t,o(r.hours[B(u)]),1)):(d(),i("div",qt,"-"))],2))),128)),t("td",Yt,o(r.total),1)]))),128))])])])]),t("div",Wt,[t("div",Jt,[t("div",Qt,[e[20]||(e[20]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",Xt,[t("dl",null,[e[19]||(e[19]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Totali ",-1)),t("dd",Zt,o(g(f)(A.value)),1)])])])]),t("div",Rt,[t("div",Kt,[e[22]||(e[22]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",te,[t("dl",null,[e[21]||(e[21]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Ore Fatturabili ",-1)),t("dd",ee,o(g(f)(E.value)),1)])])])]),t("div",re,[t("div",se,[e[24]||(e[24]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),t("div",ae,[t("dl",null,[e[23]||(e[23]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," In Attesa ",-1)),t("dd",oe,o(g(f)(Q.value)),1)])])])]),t("div",le,[t("div",ie,[e[26]||(e[26]=t("div",{class:"flex-shrink-0"},[t("div",{class:"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center"},[t("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])])],-1)),t("div",de,[t("dl",null,[e[25]||(e[25]=t("dt",{class:"text-sm font-medium text-gray-500 dark:text-gray-400 truncate"}," Progetti Attivi ",-1)),t("dd",ne,o(X.value),1)])])])])]),w.value?(d(),i("div",{key:1,class:"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50",onClick:F},[t("div",{class:"relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800",onClick:e[10]||(e[10]=D(()=>{},["stop"]))},[t("div",ue,[e[38]||(e[38]=t("h3",{class:"text-lg font-medium text-gray-900 dark:text-white mb-4"}," Aggiungi Ore ",-1)),t("form",{onSubmit:D(st,["prevent"])},[t("div",ge,[t("div",null,[e[28]||(e[28]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Progetto",-1)),x(t("select",{"onUpdate:modelValue":e[2]||(e[2]=r=>s.value.project_id=r),onChange:et,required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[e[27]||(e[27]=t("option",{value:""},"Seleziona progetto",-1)),(d(!0),i(k,null,h(_.value,r=>(d(),i("option",{key:r.id,value:r.id},o(r.name),9,ce))),128))],544),[[H,s.value.project_id]])]),t("div",null,[e[30]||(e[30]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Task",-1)),x(t("select",{"onUpdate:modelValue":e[3]||(e[3]=r=>s.value.task_id=r),required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},[e[29]||(e[29]=t("option",{value:""},"Seleziona task",-1)),(d(!0),i(k,null,h(b.value,r=>(d(),i("option",{key:r.id,value:r.id},o(r.name),9,me))),128))],512),[[H,s.value.task_id]])]),t("div",null,[e[31]||(e[31]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Data",-1)),x(t("input",{"onUpdate:modelValue":e[4]||(e[4]=r=>s.value.date=r),type:"date",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[C,s.value.date]])]),t("div",null,[e[32]||(e[32]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Ore",-1)),x(t("input",{"onUpdate:modelValue":e[5]||(e[5]=r=>s.value.hours=r),type:"number",step:"0.25",min:"0",max:"24",required:"",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[C,s.value.hours]])]),t("div",null,[e[33]||(e[33]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"},"Descrizione",-1)),x(t("textarea",{"onUpdate:modelValue":e[6]||(e[6]=r=>s.value.description=r),rows:"3",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[C,s.value.description]])]),N.value?(d(),i("div",ve,[e[37]||(e[37]=t("h4",{class:"text-sm font-medium text-gray-900 dark:text-white mb-3"},"Informazioni Fatturazione",-1)),t("div",xe,[t("div",ye,[t("label",be,[x(t("input",{type:"radio","onUpdate:modelValue":e[7]||(e[7]=r=>s.value.billable=r),value:!0,class:"text-primary-600 focus:ring-primary-500"},null,512),[[I,s.value.billable]]),e[34]||(e[34]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"Sì - Fatturabile",-1))]),t("label",pe,[x(t("input",{type:"radio","onUpdate:modelValue":e[8]||(e[8]=r=>s.value.billable=r),value:!1,class:"text-primary-600 focus:ring-primary-500"},null,512),[[I,s.value.billable]]),e[35]||(e[35]=t("span",{class:"ml-2 text-sm text-gray-700 dark:text-gray-300"},"No - Non fatturabile",-1))])]),s.value.billable?(d(),i("div",ke,[e[36]||(e[36]=t("label",{class:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"}," Tariffa (€/h) ",-1)),x(t("input",{"onUpdate:modelValue":e[9]||(e[9]=r=>s.value.billing_rate=r),type:"number",step:"0.01",min:"0",class:"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"},null,512),[[C,s.value.billing_rate]]),t("p",he," Tariffa contrattuale: "+o(ot(((v=(M=P.value)==null?void 0:M.contract)==null?void 0:v.hourly_rate)||0)),1)])):y("",!0)])])):y("",!0)]),t("div",fe,[t("button",{type:"button",onClick:F,class:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"}," Annulla "),t("button",{type:"submit",disabled:j.value,class:"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50"},o(j.value?"Salvataggio...":"Aggiungi"),9,we)])],32)])])])):y("",!0)])}}};export{Fe as default};
