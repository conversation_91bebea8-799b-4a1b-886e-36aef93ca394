*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[17:37:55] 




[17:37:55] Extension host agent started.
[17:37:56] [<unknown>][b7e42662][ManagementConnection] New connection established.
[17:37:56] [<unknown>][effbe7f4][ExtensionHostConnection] New connection established.
[17:37:56] Deleted marked for removal extension from disk stagewise.stagewise-vscode-extension /home/<USER>/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.2.2
[17:37:56] Deleted marked for removal extension from disk augment.vscode-augment /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.467.1
[17:37:56] [<unknown>][effbe7f4][ExtensionHostConnection] <456> Launched Extension Host Process.
[17:37:56] ComputeTargetPlatform: linux-x64
[17:37:58] ComputeTargetPlatform: linux-x64
[17:38:00] Getting Manifest... stagewise.stagewise-vscode-extension
[17:38:00] Installing extension: stagewise.stagewise-vscode-extension {
  productVersion: { version: '1.100.2', date: '2025-05-14T21:47:40.416Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[17:38:02] Extension signature verification result for stagewise.stagewise-vscode-extension: Success. Internal Code: 0. Executed: true. Duration: 1612ms.
[17:38:02] Extracted extension to file:///home/<USER>/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.4.1: stagewise.stagewise-vscode-extension
[17:38:03] Renamed to /home/<USER>/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.4.1
[17:38:03] Marked extension as removed stagewise.stagewise-vscode-extension-0.4.0
[17:38:03] Extension installed successfully: stagewise.stagewise-vscode-extension file:///home/<USER>/.vscode-server/extensions/extensions.json
New EH opened, aborting shutdown
[17:42:55] New EH opened, aborting shutdown
