{"name": "stagewise-vscode-extension", "displayName": "stagewise", "private": true, "description": "Eyesight for your AI-powered Code Editor.", "version": "0.4.1", "publisher": "stagewise", "icon": "icon.png", "engines": {"vscode": "^1.85.0"}, "license": "AGPL-3.0-only", "categories": ["AI", "Debuggers", "Machine Learning", "Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "stagewise.setupToolbar", "title": "Auto-setup the stagewise toolbar (AI-Assisted)", "category": "stagewise"}], "configuration": {"title": "stagewise", "type": "object", "properties": {"stagewise.telemetry.enabled": {"type": "boolean", "default": true, "description": "Enable anonymous usage data collection to help improve the product. No personal data is collected.", "tags": ["telemetry", "usesOnlineServices"], "markdownDescription": "Enable anonymous usage data collection to help improve the product. No personal data is collected.\n\nRead more about our [telemetry data collection](https://github.com/stagewise-io/stagewise/blob/main/apps/vscode-extension/TELEMETRY.md)."}}}}, "homepage": "https://stagewise.io", "repository": {"type": "git", "url": "https://github.com/stagewise-io/stagewise.git"}, "bugs": {"url": "https://github.com/stagewise-io/stagewise/issues"}, "keywords": ["stagewise", "vscode", "extension", "ai", "code", "agent", "debugging"], "scripts": {"vscode:prepublish": "pnpm run build", "run-server": "node ./out/server.js", "build": "webpack --mode production --config webpack.config.js"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "22.15.2", "@types/vscode": "^1.85.0", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "@vscode/test-cli": "^0.0.10", "@vscode/test-electron": "^2.5.2", "@vscode/vsce": "^3.3.2", "eslint": "^9.26.0", "ovsx": "^0.10.2", "ts-loader": "^9.5.2", "tsconfig-paths-webpack-plugin": "^4.2.0", "tsx": "^4.19.4", "typescript": "^5.8.3", "vitest": "3.1.2", "webpack": "^5.99.8", "webpack-cli": "^6.0.1"}, "dependencies": {"@modelcontextprotocol/sdk": "1.10.2", "@stagewise/extension-toolbar-srpc-contract": "workspace:*", "@types/cors": "^2.8.18", "@types/express": "^5.0.1", "cors": "^2.8.5", "express": "^5.1.0", "posthog-node": "^4.17.1", "ws": "^8.18.2", "zod": "^3.24.4"}, "turbo": {"tasks": {"build": {"dependsOn": ["^build"], "outputs": ["out/**"]}}}, "__metadata": {"installedTimestamp": 1748972282965, "targetPlatform": "undefined", "size": 1182240}}