[["8077383b-4fb7-48ae-be14-c7b905759d18", {"value": {"selectedCode": "", "prefix": "from flask import Blueprint, jsonify, request\n", "suffix": "from flask_login import login_required, current_user\nfrom models import Notification\nfrom app import db\n\napi_bp = Blueprint('api', __name__, url_prefix='/api')\n\n@api_bp.route('/notifications')\n@login_required\ndef get_notifications():\n    \"\"\"Endpoint per ottenere le notifiche dell'utente corrente.\"\"\"\n    # Ottieni le ultime 10 notifiche per l'utente\n    notifications = Notification.query.filter_by(\n        user_id=current_user.id, \n        is_read=False\n    ).order_by(Notification.created_at.desc()).limit(10).all()\n    \n    # Calcola il numero totale di notifiche non lette\n    unread_count = Notification.query.filter_by(\n        user_id=current_user.id, \n        is_read=False\n    ).count()\n    \n    # Formatta le notifiche per il frontend\n    formatted_notifications = []\n    for notification in notifications:\n        formatted_notifications.append({\n            'id': notification.id,\n            'text': notification.message,\n            'created_at': notification.created_at.isoformat(),\n            'link': notification.link or '#',\n            'type': notification.type,\n            'is_read': notification.is_read\n        })\n    \n    return jsonify({\n        'notifications': formatted_notifications,\n        'unread_count': unread_count\n    })\n\n@api_bp.route('/notifications/mark-read/<int:notification_id>', methods=['POST'])\n@login_required\ndef mark_notification_read(notification_id):\n    \"\"\"Segna una notifica come letta.\"\"\"\n    notification = Notification.query.get_or_404(notification_id)\n    \n    # Verifica che la notifica appartenga all'utente corrente\n    if notification.user_id != current_user.id:\n        return jsonify({'error': 'Non autorizzato'}), 403\n    \n    notification.is_read = True\n    db.session.commit()\n    \n    return jsonify({'success': True})\n\n@api_bp.route('/notifications/mark-all-read', methods=['POST'])\n@login_required\ndef mark_all_notifications_read():\n    \"\"\"Segna tutte le notifiche dell'utente come lette.\"\"\"\n    Notification.query.filter_by(\n        user_id=current_user.id,\n        is_read=False\n    ).update({'is_read': True})\n    \n    db.session.commit()\n    \n    return jsonify({'success': True}) ", "path": "blueprints/api.py", "language": "python", "prefixBegin": 0, "suffixEnd": 38}}], ["3cf405ef-1c64-4827-a849-151c72bf7be8", {"value": {"selectedCode": "# API Keys\nOPENAI_API_KEY = os.environ.get(\"OPENAI_API_KEY\")\nPERPLEXITY_API_KEY = os.environ.get(\"PERPLEXITY_API_KEY\")", "prefix": "import os\nimport json\nimport requests\nimport logging\nfrom openai import OpenAI\n\nlogger = logging.getLogger(__name__)\n\n", "suffix": "\n\n# Initialize OpenAI client\nopenai_client = OpenAI(api_key=OPENAI_API_KEY)\n\ndef analyze_text_with_openai(text, prompt=\"\", model=\"gpt-4o\"):\n    \"\"\"\n    Analyze text using OpenAI GPT models\n    \"\"\"\n    try:\n        # the newest OpenAI model is \"gpt-4o\" which was released May 13, 2024.\n        # do not change this unless explicitly requested by the user\n        if not prompt:\n            prompt = \"Analyze the following text and provide insights:\"\n\n        complete_prompt = f\"{prompt}\\n\\n{text}\"\n\n        response = openai_client.chat.completions.create(\n            model=model,\n            messages=[{\"role\": \"user\", \"content\": complete_prompt}],\n            temperature=0.2,\n        )\n\n        return response.choices[0].message.content\n    except Exception as e:\n        logger.error(f\"OpenAI API error: {str(e)}\")\n        return f\"Error analyzing text: {str(e)}\"\n\ndef generate_summary_with_openai(text, max_length=200):\n    \"\"\"\n    Generate a concise summary of the provided text\n    \"\"\"\n    try:\n        prompt = f\"Summarize the following text in about {max_length} words or less:\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\n{text}\"}],\n            temperature=0.3,\n        )\n\n        return response.choices[0].message.content\n    except Exception as e:\n        logger.error(f\"OpenAI API error: {str(e)}\")\n        return f\"Error generating summary: {str(e)}\"\n\ndef extract_insights_with_openai(text, context=\"business\"):\n    \"\"\"\n    Extract key insights from text with a specific business context\n    \"\"\"\n    try:\n        prompt = f\"Extract key {context} insights and actionable points from the following text:\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\n{text}\"}],\n            temperature=0.2,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"OpenAI API error: {str(e)}\")\n        return {\"error\": str(e), \"insights\": []}\n\ndef analyze_with_perplexity(text, question=\"\", model=\"llama-3.1-sonar-small-128k-online\"):\n    \"\"\"\n    Analyze text using Perplexity API\n    \"\"\"\n    if not PERPLEXITY_API_KEY:\n        logger.error(\"Perplexity API key not found\")\n        return \"Error: Perplexity API key not configured\"\n\n    try:\n        if not question:\n            question = \"Analyze this text and provide insights:\"\n\n        headers = {\n            \"Authorization\": f\"Bearer {PERPLEXITY_API_KEY}\",\n            \"Content-Type\": \"application/json\"\n        }\n\n        data = {\n            \"model\": model,\n            \"messages\": [\n                {\n                    \"role\": \"system\",\n                    \"content\": \"You are an AI assistant for business analytics. Be precise and concise.\"\n                },\n                {\n                    \"role\": \"user\",\n                    \"content\": f\"{question}\\n\\n{text}\"\n                }\n            ],\n            \"temperature\": 0.2,\n            \"max_tokens\": 500,\n            \"stream\": False\n        }\n\n        response = requests.post(\n            \"https://api.perplexity.ai/chat/completions\",\n            headers=headers,\n            json=data\n        )\n\n        if response.status_code == 200:\n            response_data = response.json()\n            return {\n                \"content\": response_data[\"choices\"][0][\"message\"][\"content\"],\n                \"citations\": response_data.get(\"citations\", [])\n            }\n        else:\n            logger.error(f\"Perplexity API error: {response.status_code} - {response.text}\")\n            return {\"error\": f\"API Error: {response.status_code}\", \"content\": \"\"}\n    except Exception as e:\n        logger.error(f\"Perplexity API call error: {str(e)}\")\n        return {\"error\": str(e), \"content\": \"\"}\n\ndef analyze_project_requirements(requirements_text):\n    \"\"\"\n    Analyze project requirements and extract key components\n    \"\"\"\n    try:\n        prompt = \"\"\"\n        Analyze these project requirements and extract the following information in JSON format:\n        1. Key deliverables\n        2. Potential risks\n        3. Resources needed\n        4. Estimated timeline\n        5. Success criteria\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\n{requirements_text}\"}],\n            temperature=0.2,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"Project requirements analysis error: {str(e)}\")\n        return {\n            \"error\": str(e),\n            \"key_deliverables\": [],\n            \"potential_risks\": [],\n            \"resources_needed\": [],\n            \"estimated_timeline\": \"Unknown\",\n            \"success_criteria\": []\n        }\n\ndef generate_funding_recommendations(company_profile):\n    \"\"\"\n    Generate funding recommendations based on company profile\n    \"\"\"\n    try:\n        prompt = \"\"\"\n        Based on this company profile, provide recommendations for:\n        1. Suitable funding opportunities\n        2. Grant programs to consider\n        3. Application strategy suggestions\n        4. Key points to highlight\n\n        Respond in JSON format.\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\n{company_profile}\"}],\n            temperature=0.3,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"Funding recommendations error: {str(e)}\")\n        return {\n            \"error\": str(e),\n            \"funding_opportunities\": [],\n            \"grant_programs\": [],\n            \"application_strategy\": [],\n            \"key_highlights\": []\n        }\n\ndef extract_skills_from_cv(cv_text):\n    \"\"\"\n    Estrae competenze da un CV usando OpenAI\n    \"\"\"\n    try:\n        prompt = \"\"\"\n        Analizza questo CV e estrai tutte le competenze tecniche e professionali.\n        Categorizza le competenze in:\n        - Linguaggi di programmazione\n        - Framework e librerie\n        - Database\n        - Cloud e DevOps\n        - Soft skills\n        - Certificazioni\n        - Strumenti e software\n        - Metodologie\n\n        Restituisci il risultato in formato JSON con questa struttura:\n        {\n            \"skills\": [\n                {\n                    \"name\": \"nome competenza\",\n                    \"category\": \"categoria\",\n                    \"level\": \"beginner|intermediate|advanced|expert\"\n                }\n            ],\n            \"summary\": \"breve riassunto del profilo professionale\",\n            \"experience_years\": numero_totale_anni_esperienza\n        }\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": f\"{prompt}\\n\\nCV:\\n{cv_text}\"}],\n            temperature=0.2,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"OpenAI CV analysis error: {str(e)}\")\n        return {\"error\": str(e), \"skills\": [], \"summary\": \"\", \"experience_years\": 0}\n\ndef generate_cv_html(user_data, skills_data):\n    \"\"\"\n    Genera HTML per CV usando OpenAI\n    \"\"\"\n    try:\n        prompt = f\"\"\"\n        Genera un CV professionale in HTML per questo profilo:\n\n        Dati utente: {json.dumps(user_data, indent=2)}\n        Competenze: {json.dumps(skills_data, indent=2)}\n\n        Crea un CV completo con:\n        - Intestazione con dati personali\n        - Profilo professionale (summary)\n        - Competenze tecniche organizzate per categoria\n        - Layout professionale con CSS inline\n        - Stile moderno e pulito\n\n        Restituisci solo l'HTML completo del CV.\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[{\"role\": \"user\", \"content\": prompt}],\n            temperature=0.3,\n        )\n\n        return response.choices[0].message.content\n    except Exception as e:\n        logger.error(f\"OpenAI CV generation error: {str(e)}\")\n        return f\"<p>Errore nella generazione del CV: {str(e)}</p>\"\n\n# ============================================================================\n# RESOURCE ALLOCATION AI SERVICES\n# ============================================================================\n\ndef analyze_resource_allocation(project_data, available_resources, current_allocations=None):\n    \"\"\"\n    Analizza l'allocazione delle risorse per un progetto e fornisce suggerimenti AI\n    \"\"\"\n    try:\n        # Prepara i dati per l'analisi\n        context = {\n            \"project\": {\n                \"name\": project_data.get(\"name\", \"\"),\n                \"description\": project_data.get(\"description\", \"\"),\n                \"project_type\": project_data.get(\"project_type\", \"\"),\n                \"start_date\": project_data.get(\"start_date\", \"\"),\n                \"end_date\": project_data.get(\"end_date\", \"\"),\n                \"budget\": project_data.get(\"budget\", 0),\n                \"estimated_hours\": project_data.get(\"estimated_hours\", 0),\n                \"required_skills\": project_data.get(\"required_skills\", [])\n            },\n            \"available_resources\": available_resources,\n            \"current_allocations\": current_allocations or []\n        }\n\n        prompt = \"\"\"\n        Analizza questa situazione di allocazione risorse e fornisci suggerimenti intelligenti.\n\n        IMPORTANTE: Usa SOLO i nomi reali degli utenti forniti nei dati available_resources.\n        NON usare placeholder generici come \"employee\" o \"admin\".\n\n        Considera:\n        1. Competenze richieste vs competenze disponibili\n        2. Carico di lavoro attuale delle risorse\n        3. Disponibilità temporale\n        4. Costi e budget\n        5. Efficienza del team\n\n        Fornisci suggerimenti in formato JSON con:\n        - recommended_allocations: array di allocazioni suggerite con user_id, user_name (nome reale), role, allocation (percentuale numerica)\n        - optimization_insights: insights per ottimizzazione usando i nomi reali\n        - potential_conflicts: conflitti potenziali identificati\n        - efficiency_score: punteggio efficienza (0-100)\n        - cost_analysis: analisi costi\n\n        Esempio formato recommended_allocations:\n        [\n          {\n            \"user_id\": 1,\n            \"user_name\": \"Mario Rossi\",\n            \"role\": \"Senior Developer\",\n            \"allocation\": 80\n          }\n        ]\n        \"\"\"\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[\n                {\"role\": \"system\", \"content\": \"Sei un esperto AI in resource management e project planning. Analizza i dati e fornisci suggerimenti pratici e attuabili.\"},\n                {\"role\": \"user\", \"content\": f\"{prompt}\\n\\nDati:\\n{json.dumps(context, indent=2)}\"}\n            ],\n            temperature=0.2,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"Resource allocation analysis error: {str(e)}\")\n        return {\n            \"error\": str(e),\n            \"recommended_allocations\": [],\n            \"optimization_insights\": [],\n            \"potential_conflicts\": [],\n            \"efficiency_score\": 0,\n            \"cost_analysis\": {}\n        }\n\ndef predict_resource_conflicts(allocations_data, timeline_data):\n    \"\"\"\n    Predice conflitti di risorse basandosi su dati storici e allocazioni attuali\n    \"\"\"\n    try:\n        prompt = \"\"\"\n        Analizza queste allocazioni di risorse e predici potenziali conflitti.\n\n        Identifica:\n        1. Sovrallocazioni (>100% capacità)\n        2. Conflitti temporali\n        3. Competenze mancanti\n        4. Rischi di burnout\n        5. Dipendenze critiche\n\n        Fornisci risultato in JSON con:\n        - conflicts: array di conflitti identificati\n        - risk_level: livello rischio (low/medium/high)\n        - recommendations: raccomandazioni per risoluzione\n        - timeline_impact: impatto su timeline progetto\n        \"\"\"\n\n        context = {\n            \"allocations\": allocations_data,\n            \"timeline\": timeline_data\n        }\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[\n                {\"role\": \"system\", \"content\": \"Sei un AI specialist in project risk management. Identifica proattivamente i rischi nelle allocazioni di risorse.\"},\n                {\"role\": \"user\", \"content\": f\"{prompt}\\n\\nDati:\\n{json.dumps(context, indent=2)}\"}\n            ],\n            temperature=0.1,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"Resource conflict prediction error: {str(e)}\")\n        return {\n            \"error\": str(e),\n            \"conflicts\": [],\n            \"risk_level\": \"unknown\",\n            \"recommendations\": [],\n            \"timeline_impact\": \"unknown\"\n        }\n\ndef optimize_team_composition(project_requirements, candidate_resources):\n    \"\"\"\n    Ottimizza la composizione del team per un progetto specifico\n    \"\"\"\n    try:\n        prompt = \"\"\"\n        Ottimizza la composizione del team per questo progetto.\n\n        Considera:\n        1. Skill match con requisiti progetto\n        2. Sinergie tra membri del team\n        3. Bilanciamento senior/junior\n        4. Copertura competenze critiche\n        5. Dinamiche di team\n\n        Fornisci in JSON:\n        - optimal_team: composizione team ottimale\n        - skill_coverage: copertura competenze (%)\n        - team_synergy_score: punteggio sinergia (0-100)\n        - alternative_options: opzioni alternative\n        - training_needs: necessità formazione\n        \"\"\"\n\n        context = {\n            \"project_requirements\": project_requirements,\n            \"candidate_resources\": candidate_resources\n        }\n\n        response = openai_client.chat.completions.create(\n            model=\"gpt-4o\",\n            messages=[\n                {\"role\": \"system\", \"content\": \"Sei un AI expert in team building e resource optimization. Crea team ad alta performance.\"},\n                {\"role\": \"user\", \"content\": f\"{prompt}\\n\\nDati:\\n{json.dumps(context, indent=2)}\"}\n            ],\n            temperature=0.3,\n            response_format={\"type\": \"json_object\"},\n        )\n\n        return json.loads(response.choices[0].message.content)\n    except Exception as e:\n        logger.error(f\"Team composition optimization error: {str(e)}\")\n        return {\n            \"error\": str(e),\n            \"optimal_team\": [],\n            \"skill_coverage\": 0,\n            \"team_synergy_score\": 0,\n            \"alternative_options\": [],\n            \"training_needs\": []\n        }\n", "path": "backend/ai_services.py", "language": "python", "prefixBegin": 0, "suffixEnd": 0}}], ["5bbcc239-4efc-4fd1-8b02-ee82bcc80dbd", {"value": {"selectedCode": "", "prefix": "import os\nimport logging\nfrom flask import Flask, session, redirect, url_for, request, flash\nfrom flask_login import logout_user, current_user\nfrom flask_cors import CORS\nfrom werkzeug.middleware.proxy_fix import ProxyFix\nfrom datetime import datetime, timedelta\nfrom config import Config\nimport time\nfrom extensions import db, login_manager, migrate, csrf\n\n# Configure logging\nlogging.basicConfig(level=logging.INFO) # Modificato INFO per produzione, DEBUG per sviluppo\nlogger = logging.getLogger(__name__)\n\nPUBLIC_ENDPOINTS = [\n    'static',\n    'public_api.get_public_config', 'public_api.get_featured_services',\n    'public_api.get_services', 'public_api.get_service_detail',\n    'tenants_api.api_tenant_config',  # API per configurazione tenant\n    'api_auth.login', 'api_auth.logout', 'api_auth.debug',  # API di autenticazione Vue.js\n    'swagger_json.swagger_json',  # Swagger JSON\n    'swagger_ui.show',  # Swagger UI\n    'spa'  # SPA catch-all route - Vue.js gestisce tutto\n]\n\n# Fix MIME types for ES6 modules GLOBALLY\nimport mimetypes\nmimetypes.add_type('application/javascript', '.js')\nmimetypes.add_type('application/javascript', '.mjs')\nmimetypes.add_type('application/javascript', '.vue')\n\ndef create_app(config_object='config.Config', config_overrides=None):\n    \"\"\"Factory function to create and configure the Flask app.\"\"\"\n    app = Flask(__name__)\n    app.secret_key = os.environ.get(\"SESSION_SECRET\", os.urandom(24))\n    app.wsgi_app = ProxyFix(app.wsgi_app, x_proto=1, x_host=1)\n\n    # Load configuration\n    app.config.from_object(config_object)\n    if config_overrides:\n        app.config.from_mapping(config_overrides)\n\n    # Initialize extensions with app\n    db.init_app(app)\n    login_manager.init_app(app)\n    # login_manager.login_view = None  # Non serve più con Vue.js SPA\n    migrate.init_app(app, db)\n\n    # Configure CORS to allow credentials (cookies) for Vue.js SPA\n    CORS(app,\n         origins=['http://localhost:3000', 'http://localhost:5000', 'http://127.0.0.1:5000', 'http://127.0.0.1:3000'],\n", "suffix": "         supports_credentials=True,\n         allow_headers=['Content-Type', 'X-CSRFToken', 'Authorization'],\n         methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])\n\n    # Configure CSRF protection\n    csrf.init_app(app)\n    # Make csrf_token available in templates without function call\n    app.jinja_env.globals['csrf_token'] = lambda: csrf.generate_csrf()\n\n    with app.app_context():\n        # Import models first to ensure they're registered\n        from models import User # Spostato import qui per evitare importazioni circolari\n\n        # Add datetime utility for templates\n        @app.context_processor\n        def utility_processor():\n            return {'current_year': datetime.now().year}\n\n        # Import blueprints for Vue.js SPA\n        from blueprints.api.public import public_api_bp\n        from blueprints.swagger import register_swagger_blueprints\n\n        # Register blueprints - SOLO API per Vue.js SPA\n        app.register_blueprint(public_api_bp)\n\n        # Auth API\n        from blueprints.api.auth import api_auth\n        app.register_blueprint(api_auth, url_prefix='/api/auth')\n\n        # Dashboard API\n        from blueprints.api.dashboard import api_dashboard\n        app.register_blueprint(api_dashboard, url_prefix='/api/dashboard')\n\n        # Projects API\n        from blueprints.api.projects import api_projects\n        app.register_blueprint(api_projects, url_prefix='/api/projects')\n\n        # Tasks API\n        from blueprints.api.tasks import api_tasks\n        app.register_blueprint(api_tasks, url_prefix='/api/tasks')\n\n        # Timesheets API\n        from blueprints.api.timesheets import api_timesheets\n        app.register_blueprint(api_timesheets, url_prefix='/api/timesheets')\n\n        # Personnel API\n        from blueprints.api.personnel import api_personnel\n        app.register_blueprint(api_personnel, url_prefix='/api/personnel')\n\n        # Personnel Allocation API (registrato prima per evitare conflitti)\n        from blueprints.api.personnel_allocation import api_personnel_allocation\n        app.register_blueprint(api_personnel_allocation, url_prefix='/api')\n\n        # Expenses API\n        from blueprints.api.expenses import api_expenses\n        app.register_blueprint(api_expenses)\n\n        # KPIs API\n        from blueprints.api.kpis import api_kpis\n        app.register_blueprint(api_kpis, url_prefix='/api/kpis')\n\n        # Project KPIs API\n        from blueprints.api.project_kpis import api_project_kpis\n        app.register_blueprint(api_project_kpis, url_prefix='/api/project-kpis')\n\n        # Resources API\n        from blueprints.api.resources import api_resources\n        app.register_blueprint(api_resources, url_prefix='/api/resources')\n\n        # Task Dependencies API\n        from blueprints.api.task_dependencies import api_task_dependencies\n        app.register_blueprint(api_task_dependencies, url_prefix='/api/task-dependencies')\n\n        # Tenant API (senza prefix per mantenere /api/config/tenant)\n        from blueprints.api.tenants import tenants_api\n        app.register_blueprint(tenants_api, url_prefix='/api')\n\n        # Admin API\n        from blueprints.api.admin import api_admin\n        app.register_blueprint(api_admin, url_prefix='/api/admin')\n\n        # AI Resources API\n        from blueprints.api.ai_resources import api_ai_resources\n        app.register_blueprint(api_ai_resources, url_prefix='/api/ai-resources')\n\n\n\n        # Register Swagger blueprints\n        register_swagger_blueprints(app)\n\n        # Configure static file serving with correct MIME types\n        @app.after_request\n        def after_request(response):\n            # Fix MIME type for JavaScript modules and Vue files\n            if request.path.endswith('.js') or request.path.endswith('.mjs'):\n                response.content_type = 'application/javascript'\n                response.headers['Cache-Control'] = 'public, max-age=********'\n            elif request.path.endswith('.vue'):\n                response.content_type = 'application/javascript'\n            elif request.path.endswith('.css'):\n                response.content_type = 'text/css'\n                response.headers['Cache-Control'] = 'public, max-age=********'\n            return response\n\n\n\n        # SPA Route - Catch-all for Vue.js routing\n        @app.route('/')\n        @app.route('/<path:path>')\n        def spa(path=''):\n            \"\"\"\n            Serve the Vue.js SPA for all routes except API and auth routes.\n            This allows Vue Router to handle client-side routing.\n            \"\"\"\n            # Don't serve SPA for API routes\n            if path.startswith('api/'):\n                from flask import abort\n                abort(404)\n\n            # Don't serve SPA for auth routes (keep traditional auth)\n            if path.startswith('auth/'):\n                from flask import abort\n                abort(404)\n\n            # Don't serve SPA for static files - let Flask serve them directly\n            if path.startswith('static/'):\n                from flask import abort\n                abort(404)\n\n            # Don't serve SPA for swagger routes\n            if path.startswith('swagger') or path.startswith('docs') or path.startswith('api/swagger'):\n                from flask import abort\n                abort(404)\n\n            # Serve the Vue.js SPA template for all other routes\n            from flask import render_template\n            return render_template('vue_app.html')\n\n        # Setup user loader for Flask-Login\n        @login_manager.user_loader\n        def load_user(user_id):\n            return User.query.get(int(user_id))\n\n        # Create database tables if they don't exist\n        db.create_all()\n\n        logger.info(\"Flask app created and configured.\")\n\n        @app.before_request\n        def session_management():\n            # Simplified session management for Vue.js SPA\n            if current_user.is_authenticated:\n                session['last_activity'] = time.time()\n\n        @app.before_request\n        def global_auth_enforcement():\n            endpoint = request.endpoint\n            # Log ogni accesso\n            user = getattr(current_user, 'username', 'anonymous')\n            logger.info(f\"Accesso: user={user}, endpoint={endpoint}, ip={request.remote_addr}\")\n\n            # Skip enforcement per endpoint pubblici\n            if not endpoint or endpoint.startswith('static') or endpoint in PUBLIC_ENDPOINTS:\n                return\n\n            # Enforcement autenticazione globale solo per endpoint protetti\n            if not current_user.is_authenticated:\n                logger.warning(f\"Tentativo accesso non autenticato a {endpoint} da IP {request.remote_addr}\")\n\n                # Per le API, restituisci JSON 401\n                if endpoint and (endpoint.startswith('api_') or endpoint.startswith('api.')):\n                    from flask import jsonify\n                    return jsonify({\n                        'success': False,\n                        'message': 'Autenticazione richiesta'\n                    }), 401\n\n                # Per le pagine web/SPA, restituisci JSON 401 (Vue.js gestirà il redirect)\n                from flask import jsonify\n                return jsonify({'error': 'Authentication required'}), 401\n\n        @app.errorhandler(403)\n        def forbidden(e):\n            user = getattr(current_user, 'username', 'anonymous')\n            logger.warning(f\"403 Forbidden: user={user}, endpoint={request.endpoint}, ip={request.remote_addr}\")\n            flash('Accesso negato: non hai i permessi necessari.', 'danger')\n            return redirect('/')\n\n        # Registra i filtri personalizzati\n        from utils.filters import register_filters\n        register_filters(app)\n\n    return app\n", "path": "backend/app.py", "language": "python", "prefixBegin": 0, "suffixEnd": 0}}], ["3fa427f6-3a0e-4920-b900-98bfa4c73dbd", {"value": {"selectedCode": "", "prefix": "<template>\n  <div class=\"space-y-6\">\n    <!-- <PERSON>rro<PERSON> -->\n    <div v-if=\"error\" class=\"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4\">\n      <div class=\"flex\">\n        <div class=\"flex-shrink-0\">\n          <svg class=\"h-5 w-5 text-red-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z\"></path>\n          </svg>\n        </div>\n        <div class=\"ml-3\">\n          <p class=\"text-sm text-red-800 dark:text-red-200\">{{ error }}</p>\n        </div>\n        <div class=\"ml-auto pl-3\">\n          <div class=\"-mx-1.5 -my-1.5\">\n            <button @click=\"clearError\" class=\"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900\">\n              <svg class=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Le Mie Ore</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Registra le tue ore di lavoro con la griglia mensile\n          </p>\n        </div>\n\n        <!-- Controlli periodo -->\n        <div class=\"flex items-center space-x-4\">\n          <div class=\"flex items-center space-x-2\">\n            <button\n              @click=\"previousMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\"></path>\n              </svg>\n            </button>\n\n            <div class=\"text-center\">\n              <h2 class=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n              </h2>\n            </div>\n\n            <button\n              @click=\"nextMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n              </svg>\n            </button>\n          </div>\n\n          <button\n            @click=\"showAddHoursModal = true\"\n", "suffix": "            class=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n            </svg>\n            Aggiungi Ore\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Griglia Timesheet Mensile -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Griglia Ore - {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n          </h3>\n          <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n            Totale: {{ formatHours(totalHours) }} | Fatturabili: {{ formatHours(billableHours) }}\n          </div>\n        </div>\n      </div>\n\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <!-- Header con giorni del mese -->\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600\">\n                Progetto/Task\n              </th>\n              <th\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day, currentMonth, currentYear),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day, currentMonth, currentYear)\n                }\"\n              >\n                <div>{{ day }}</div>\n                <div class=\"text-xs text-gray-400\">{{ getDayName(day, currentMonth, currentYear) }}</div>\n              </th>\n              <th class=\"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600\">\n                Totale\n              </th>\n            </tr>\n          </thead>\n\n          <!-- Righe progetti/task -->\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-if=\"timesheetGridData.length === 0\">\n              <td :colspan=\"daysInMonth.length + 2\" class=\"px-6 py-8 text-center text-gray-500 dark:text-gray-400\">\n                <div class=\"space-y-2\">\n                  <p>Nessun timesheet registrato per {{ getMonthName(currentMonth) }} {{ currentYear }}</p>\n                  <button\n                    @click=\"showAddHoursModal = true\"\n                    class=\"text-primary-600 hover:text-primary-700 dark:text-primary-400\"\n                  >\n                    Registra le tue prime ore\n                  </button>\n                </div>\n              </td>\n            </tr>\n            \n            <tr v-for=\"taskRow in timesheetGridData\" :key=\"taskRow.taskId\">\n              <!-- Colonna progetto/task -->\n              <td class=\"sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600\">\n                <div class=\"flex items-center justify-between\">\n                  <div class=\"min-w-0 flex-1\">\n                    <p class=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                      {{ taskRow.projectName }}\n                    </p>\n                    <p v-if=\"taskRow.taskName\" class=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\n                      {{ taskRow.taskName }}\n                    </p>\n                    <p class=\"text-xs text-gray-500 dark:text-gray-400\">\n                      {{ taskRow.assignees }}\n                    </p>\n                  </div>\n                </div>\n              </td>\n              \n              <!-- Colonne giorni -->\n              <td\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-1 py-3 text-center\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day, currentMonth, currentYear),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day, currentMonth, currentYear)\n                }\"\n              >\n                <div v-if=\"taskRow.hours[getDateString(day)]\" class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ taskRow.hours[getDateString(day)] }}\n                </div>\n                <div v-else class=\"text-gray-300 dark:text-gray-600\">-</div>\n              </td>\n              \n              <!-- Colonna totale riga -->\n              <td class=\"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 border-l border-gray-200 dark:border-gray-600\">\n                {{ taskRow.total }}\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Quick Stats -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Totali\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(totalHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Fatturabili\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(billableHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                In Attesa\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(pendingHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Progetti Attivi\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ activeProjects }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal Aggiungi Ore -->\n    <div v-if=\"showAddHoursModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"closeHoursModal\">\n      <div class=\"relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            Aggiungi Ore\n          </h3>\n\n          <form @submit.prevent=\"saveHours\">\n            <div class=\"grid grid-cols-1 gap-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Progetto</label>\n                <select\n                  v-model=\"hoursForm.project_id\"\n                  @change=\"onProjectChange\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n                  <option value=\"\">Seleziona progetto</option>\n                  <option\n                    v-for=\"project in userProjects\"\n                    :key=\"project.id\"\n                    :value=\"project.id\"\n                  >\n                    {{ project.name }}\n                  </option>\n                </select>\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Task</label>\n                <select\n                  v-model=\"hoursForm.task_id\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n                  <option value=\"\">Seleziona task</option>\n                  <option\n                    v-for=\"task in availableTasks\"\n                    :key=\"task.id\"\n                    :value=\"task.id\"\n                  >\n                    {{ task.name }}\n                  </option>\n                </select>\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Data</label>\n                <input\n                  v-model=\"hoursForm.date\"\n                  type=\"date\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Ore</label>\n                <input\n                  v-model=\"hoursForm.hours\"\n                  type=\"number\"\n                  step=\"0.25\"\n                  min=\"0\"\n                  max=\"24\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Descrizione</label>\n                <textarea\n                  v-model=\"hoursForm.description\"\n                  rows=\"3\"\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                ></textarea>\n              </div>\n\n              <!-- Sezione Billing (solo per manager/admin) -->\n              <div v-if=\"canViewBilling\" class=\"border-t border-gray-200 dark:border-gray-600 pt-4\">\n                <h4 class=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">Informazioni Fatturazione</h4>\n\n                <div class=\"space-y-3\">\n                  <div class=\"flex items-center justify-between\">\n                    <label class=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        v-model=\"hoursForm.billable\"\n                        :value=\"true\"\n                        class=\"text-primary-600 focus:ring-primary-500\"\n                      />\n                      <span class=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">Sì - Fatturabile</span>\n                    </label>\n                    <label class=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        v-model=\"hoursForm.billable\"\n                        :value=\"false\"\n                        class=\"text-primary-600 focus:ring-primary-500\"\n                      />\n                      <span class=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">No - Non fatturabile</span>\n                    </label>\n                  </div>\n\n                  <div v-if=\"hoursForm.billable\">\n                    <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      Tariffa (€/h)\n                    </label>\n                    <input\n                      v-model=\"hoursForm.billing_rate\"\n                      type=\"number\"\n                      step=\"0.01\"\n                      min=\"0\"\n                      class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                    >\n                    <p class=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                      Tariffa contrattuale: {{ formatCurrency(selectedProject?.contract?.hourly_rate || 0) }}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"flex justify-end space-x-3 mt-6\">\n              <button\n                type=\"button\"\n                @click=\"closeHoursModal\"\n                class=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md\"\n              >\n                Annulla\n              </button>\n              <button\n                type=\"submit\"\n                :disabled=\"saving\"\n                class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50\"\n              >\n                {{ saving ? 'Salvataggio...' : 'Aggiungi' }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useTimesheetStore } from '@/stores/timesheet'\nimport { usePermissions } from '@/composables/usePermissions'\nimport { useAuthStore } from '@/stores/auth'\nimport {\n  monthNames,\n  formatHours,\n  isToday,\n  isWeekend,\n  getDayName\n} from '@/utils/timesheet'\nimport api from '@/utils/api'\n\nconst timesheetStore = useTimesheetStore()\nconst { hasPermission, isManager, isAdmin } = usePermissions()\nconst authStore = useAuthStore()\n\n// Local state\nconst showAddHoursModal = ref(false)\nconst availableTasks = ref([])\nconst userProjects = ref([])\nconst saving = ref(false)\n\n// Form data\nconst hoursForm = ref({\n  project_id: '',\n  task_id: '',\n  date: new Date().toISOString().split('T')[0],\n  hours: 0,\n  description: '',\n  billable: true,\n  billing_rate: null\n})\n\n// Computed properties from store\nconst currentYear = computed(() => timesheetStore.currentYear)\nconst currentMonth = computed(() => timesheetStore.currentMonth)\nconst projectTasks = computed(() => timesheetStore.projectTasks)\nconst monthlyEntries = computed(() => timesheetStore.monthlyEntries)\nconst availableProjects = computed(() => timesheetStore.availableProjects)\nconst loading = computed(() => timesheetStore.loading.monthlyData || timesheetStore.loading.saving)\nconst error = computed(() => timesheetStore.error)\n\n// New computed properties for hours modal\nconst canViewBilling = computed(() => isManager.value || isAdmin.value)\n\nconst selectedProject = computed(() => {\n  return userProjects.value.find(p => p.id === parseInt(hoursForm.value.project_id))\n})\n\n// Computed calculations\nconst daysInMonth = computed(() => timesheetStore.daysInMonth)\nconst totalHours = computed(() => timesheetStore.totalHours)\nconst billableHours = computed(() => timesheetStore.billableHours)\nconst pendingHours = computed(() => timesheetStore.pendingHours)\nconst activeProjects = computed(() => timesheetStore.activeProjects)\n\n// Build timesheet grid from existing entries like PersonnelProfile.vue\nconst timesheetGridData = computed(() => {\n  const taskGroups = {}\n  \n  // Process timesheet entries from the store\n  Object.entries(monthlyEntries.value).forEach(([date, dayEntries]) => {\n    Object.entries(dayEntries).forEach(([taskKey, hours]) => {\n      if (!taskGroups[taskKey]) {\n        // Find project task info\n        const projectTask = projectTasks.value.find(pt => pt.id === taskKey)\n        taskGroups[taskKey] = {\n          taskId: taskKey,\n          taskName: projectTask?.task_name || 'Attività Generica',\n          projectName: projectTask?.project_name || 'Progetto Sconosciuto', \n          assignees: 'Tu',\n          hours: {},\n          total: 0\n        }\n      }\n      \n      taskGroups[taskKey].hours[date] = parseFloat(hours || 0).toFixed(1)\n      taskGroups[taskKey].total += parseFloat(hours || 0)\n    })\n  })\n  \n  // Format totals\n  return Object.values(taskGroups).map(task => ({\n    ...task,\n    total: task.total.toFixed(1)\n  }))\n})\n\n// Methods\nconst previousMonth = () => {\n  timesheetStore.navigateMonth('previous')\n}\n\nconst nextMonth = () => {\n  timesheetStore.navigateMonth('next')\n}\n\nconst clearError = () => {\n  timesheetStore.clearError()\n}\n\n\n\n// Hours modal functions\nconst closeHoursModal = () => {\n  showAddHoursModal.value = false\n  resetHoursForm()\n  availableTasks.value = []\n}\n\nconst resetHoursForm = () => {\n  hoursForm.value = {\n    project_id: '',\n    task_id: '',\n    date: new Date().toISOString().split('T')[0],\n    hours: 0,\n    description: '',\n    billable: true,\n    billing_rate: null\n  }\n}\n\nconst onProjectChange = async () => {\n  hoursForm.value.task_id = ''\n  availableTasks.value = []\n\n  if (hoursForm.value.project_id) {\n    await loadTasksForProject(hoursForm.value.project_id)\n\n    // Set default billing rate from project contract\n    const project = selectedProject.value\n    if (project?.contract?.hourly_rate && canViewBilling.value) {\n      hoursForm.value.billing_rate = project.contract.hourly_rate\n    }\n  }\n}\n\nconst loadTasksForProject = async (projectId) => {\n  try {\n    const response = await api.get(`/api/tasks?project_id=${projectId}&status=open`)\n    if (response.data.success) {\n      availableTasks.value = response.data.data.tasks || []\n    }\n  } catch (error) {\n    console.error('Error loading tasks:', error)\n    availableTasks.value = []\n  }\n}\n\nconst saveHours = async () => {\n  saving.value = true\n\n  try {\n    const payload = {\n      user_id: authStore.user.id,\n      project_id: parseInt(hoursForm.value.project_id),\n      task_id: hoursForm.value.task_id ? parseInt(hoursForm.value.task_id) : null,\n      date: hoursForm.value.date,\n      hours: parseFloat(hoursForm.value.hours),\n      description: hoursForm.value.description,\n      billable: hoursForm.value.billable,\n      billing_rate: hoursForm.value.billable && hoursForm.value.billing_rate ?\n                   parseFloat(hoursForm.value.billing_rate) : null\n    }\n\n    const response = await api.post('/api/timesheets', payload)\n\n    if (response.data.success) {\n      closeHoursModal()\n      // Refresh timesheet data\n      await timesheetStore.loadMonthlyData()\n    } else {\n      throw new Error(response.data.message || 'Errore durante il salvataggio')\n    }\n  } catch (error) {\n    console.error('Error saving hours:', error)\n    // You might want to show an error message to the user\n  } finally {\n    saving.value = false\n  }\n}\n\nconst getEntryValue = (projectTaskId, day) => {\n  const entryKey = `${currentYear.value}-${currentMonth.value}-${day}`\n  return monthlyEntries.value[entryKey]?.[projectTaskId] || 0\n}\n\nconst getDateString = (day) => {\n  return `${currentYear.value}-${currentMonth.value.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`\n}\n\nconst getMonthName = (month) => {\n  const names = [\n    'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',\n    'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'\n  ]\n  return names[month - 1]\n}\n\n// Utility functions\nconst formatCurrency = (amount) => {\n  return new Intl.NumberFormat('it-IT', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(amount || 0)\n}\n\nconst loadUserProjects = async () => {\n  try {\n    const response = await api.get('/api/projects')\n    if (response.data.success) {\n      // Handle different response structures like in the projects store\n      if (response.data.data.items) {\n        userProjects.value = response.data.data.items\n      } else if (response.data.data.projects) {\n        userProjects.value = response.data.data.projects\n      } else if (Array.isArray(response.data.data)) {\n        userProjects.value = response.data.data\n      } else {\n        userProjects.value = []\n      }\n    }\n  } catch (error) {\n    console.error('Error loading user projects:', error)\n    userProjects.value = []\n  }\n}\n\n// Lifecycle\nonMounted(async () => {\n  // Load data from store with caching\n  await Promise.all([\n    timesheetStore.loadAvailableProjects(),\n    timesheetStore.loadMonthlyData(),\n    loadUserProjects()\n  ])\n})\n</script>", "path": "frontend/src/views/timesheet/TimesheetEntry.vue", "language": "vue", "prefixBegin": 0, "suffixEnd": 9}}]]