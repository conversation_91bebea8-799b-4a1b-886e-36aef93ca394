# Project Structure and Technologies
- The project uses Flask blueprints.
- DAO/Repository pattern is used for data access.
- Flask-WTF is used for validation.
- Alpine.js is used for client-side interactions.
- Tests should be developed in the /tests directory using pytest scripts.

# Access Control and API Authentication
- Implements RBAC for access control.
- APIs should be accessible via Swagger and well-documented.
- API authentication: Hybrid approach preferred, supporting both web interface (with cookies) and potential mobile clients.

# Personnel Allocation Analysis
- Personnel allocation analysis should include filters for department and project allocation percentage to make large datasets more manageable.
- Statistics boxes should update based on selected month, and billable hours should be indicated with green dots like in the projects view.