{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetEntry.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- <PERSON>rro<PERSON> -->\n    <div v-if=\"error\" class=\"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4\">\n      <div class=\"flex\">\n        <div class=\"flex-shrink-0\">\n          <svg class=\"h-5 w-5 text-red-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z\"></path>\n          </svg>\n        </div>\n        <div class=\"ml-3\">\n          <p class=\"text-sm text-red-800 dark:text-red-200\">{{ error }}</p>\n        </div>\n        <div class=\"ml-auto pl-3\">\n          <div class=\"-mx-1.5 -my-1.5\">\n            <button @click=\"clearError\" class=\"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900\">\n              <svg class=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Le Mie Ore</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Registra le tue ore di lavoro con la griglia mensile\n          </p>\n        </div>\n\n        <!-- Controlli periodo -->\n        <div class=\"flex items-center space-x-4\">\n          <div class=\"flex items-center space-x-2\">\n            <button\n              @click=\"previousMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\"></path>\n              </svg>\n            </button>\n\n            <div class=\"text-center\">\n              <h2 class=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n              </h2>\n            </div>\n\n            <button\n              @click=\"nextMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n              </svg>\n            </button>\n          </div>\n\n          <button\n            @click=\"showAddHoursModal = true\"\n            class=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n            </svg>\n            Aggiungi Ore\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Griglia Timesheet Mensile -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Griglia Ore - {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n          </h3>\n          <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n            Totale: {{ formatHours(totalHours) }} | Fatturabili: {{ formatHours(billableHours) }}\n          </div>\n        </div>\n      </div>\n\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <!-- Header con giorni del mese -->\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600\">\n                Progetto/Task\n              </th>\n              <th\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day, currentMonth, currentYear),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day, currentMonth, currentYear)\n                }\"\n              >\n                <div>{{ day }}</div>\n                <div class=\"text-xs text-gray-400\">{{ getDayName(day, currentMonth, currentYear) }}</div>\n              </th>\n              <th class=\"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600\">\n                Totale\n              </th>\n            </tr>\n          </thead>\n\n          <!-- Righe progetti/task -->\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-if=\"timesheetGridData.length === 0\">\n              <td :colspan=\"daysInMonth.length + 2\" class=\"px-6 py-8 text-center text-gray-500 dark:text-gray-400\">\n                <div class=\"space-y-2\">\n                  <p>Nessun timesheet registrato per {{ getMonthName(currentMonth) }} {{ currentYear }}</p>\n                  <button\n                    @click=\"showAddHoursModal = true\"\n                    class=\"text-primary-600 hover:text-primary-700 dark:text-primary-400\"\n                  >\n                    Registra le tue prime ore\n                  </button>\n                </div>\n              </td>\n            </tr>\n            \n            <tr v-for=\"taskRow in timesheetGridData\" :key=\"taskRow.taskId\">\n              <!-- Colonna progetto/task -->\n              <td class=\"sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600\">\n                <div class=\"flex items-center justify-between\">\n                  <div class=\"min-w-0 flex-1\">\n                    <p class=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                      {{ taskRow.projectName }}\n                    </p>\n                    <p v-if=\"taskRow.taskName\" class=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\n                      {{ taskRow.taskName }}\n                    </p>\n                    <p class=\"text-xs text-gray-500 dark:text-gray-400\">\n                      {{ taskRow.assignees }}\n                    </p>\n                  </div>\n                </div>\n              </td>\n              \n              <!-- Colonne giorni -->\n              <td\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-1 py-3 text-center\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day, currentMonth, currentYear),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day, currentMonth, currentYear)\n                }\"\n              >\n                <div v-if=\"taskRow.hours[getDateString(day)]\" class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ taskRow.hours[getDateString(day)] }}\n                </div>\n                <div v-else class=\"text-gray-300 dark:text-gray-600\">-</div>\n              </td>\n              \n              <!-- Colonna totale riga -->\n              <td class=\"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 border-l border-gray-200 dark:border-gray-600\">\n                {{ taskRow.total }}\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Quick Stats -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Totali\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(totalHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Fatturabili\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(billableHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                In Attesa\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(pendingHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Progetti Attivi\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ activeProjects }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal Aggiungi Ore -->\n    <div v-if=\"showAddHoursModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"closeHoursModal\">\n      <div class=\"relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            Aggiungi Ore\n          </h3>\n\n          <form @submit.prevent=\"saveHours\">\n            <div class=\"grid grid-cols-1 gap-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Progetto</label>\n                <select\n                  v-model=\"hoursForm.project_id\"\n                  @change=\"onProjectChange\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n                  <option value=\"\">Seleziona progetto</option>\n                  <option\n                    v-for=\"project in userProjects\"\n                    :key=\"project.id\"\n                    :value=\"project.id\"\n                  >\n                    {{ project.name }}\n                  </option>\n                </select>\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Task</label>\n                <select\n                  v-model=\"hoursForm.task_id\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n                  <option value=\"\">Seleziona task</option>\n                  <option\n                    v-for=\"task in availableTasks\"\n                    :key=\"task.id\"\n                    :value=\"task.id\"\n                  >\n                    {{ task.name }}\n                  </option>\n                </select>\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Data</label>\n                <input\n                  v-model=\"hoursForm.date\"\n                  type=\"date\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Ore</label>\n                <input\n                  v-model=\"hoursForm.hours\"\n                  type=\"number\"\n                  step=\"0.25\"\n                  min=\"0\"\n                  max=\"24\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Descrizione</label>\n                <textarea\n                  v-model=\"hoursForm.description\"\n                  rows=\"3\"\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                ></textarea>\n              </div>\n\n              <!-- Sezione Billing (solo per manager/admin) -->\n              <div v-if=\"canViewBilling\" class=\"border-t border-gray-200 dark:border-gray-600 pt-4\">\n                <h4 class=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">Informazioni Fatturazione</h4>\n\n                <div class=\"space-y-3\">\n                  <div class=\"flex items-center justify-between\">\n                    <label class=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        v-model=\"hoursForm.billable\"\n                        :value=\"true\"\n                        class=\"text-primary-600 focus:ring-primary-500\"\n                      />\n                      <span class=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">Sì - Fatturabile</span>\n                    </label>\n                    <label class=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        v-model=\"hoursForm.billable\"\n                        :value=\"false\"\n                        class=\"text-primary-600 focus:ring-primary-500\"\n                      />\n                      <span class=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">No - Non fatturabile</span>\n                    </label>\n                  </div>\n\n                  <div v-if=\"hoursForm.billable\">\n                    <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      Tariffa (€/h)\n                    </label>\n                    <input\n                      v-model=\"hoursForm.billing_rate\"\n                      type=\"number\"\n                      step=\"0.01\"\n                      min=\"0\"\n                      class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                    >\n                    <p class=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                      Tariffa contrattuale: {{ formatCurrency(selectedProject?.contract?.hourly_rate || 0) }}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"flex justify-end space-x-3 mt-6\">\n              <button\n                type=\"button\"\n                @click=\"closeHoursModal\"\n                class=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md\"\n              >\n                Annulla\n              </button>\n              <button\n                type=\"submit\"\n                :disabled=\"saving\"\n                class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50\"\n              >\n                {{ saving ? 'Salvataggio...' : 'Aggiungi' }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useTimesheetStore } from '@/stores/timesheet'\nimport { usePermissions } from '@/composables/usePermissions'\nimport { useAuthStore } from '@/stores/auth'\nimport {\n  monthNames,\n  formatHours,\n  isToday,\n  isWeekend,\n  getDayName,\n  validateHours\n} from '@/utils/timesheet'\nimport api from '@/utils/api'\n\nconst timesheetStore = useTimesheetStore()\nconst { hasPermission, isManager, isAdmin } = usePermissions()\nconst authStore = useAuthStore()\n\n// Local state\nconst showAddHoursModal = ref(false)\nconst availableTasks = ref([])\nconst userProjects = ref([])\nconst saving = ref(false)\n\n// Form data\nconst hoursForm = ref({\n  project_id: '',\n  task_id: '',\n  date: new Date().toISOString().split('T')[0],\n  hours: 0,\n  description: '',\n  billable: true,\n  billing_rate: null\n})\n\n// Computed properties from store\nconst currentYear = computed(() => timesheetStore.currentYear)\nconst currentMonth = computed(() => timesheetStore.currentMonth)\nconst projectTasks = computed(() => timesheetStore.projectTasks)\nconst monthlyEntries = computed(() => timesheetStore.monthlyEntries)\nconst availableProjects = computed(() => timesheetStore.availableProjects)\nconst loading = computed(() => timesheetStore.loading.monthlyData || timesheetStore.loading.saving)\nconst error = computed(() => timesheetStore.error)\n\n// New computed properties for hours modal\nconst canViewBilling = computed(() => isManager.value || isAdmin.value)\n\nconst selectedProject = computed(() => {\n  return userProjects.value.find(p => p.id === parseInt(hoursForm.value.project_id))\n})\n\n// Computed calculations\nconst daysInMonth = computed(() => timesheetStore.daysInMonth)\nconst totalHours = computed(() => timesheetStore.totalHours)\nconst billableHours = computed(() => timesheetStore.billableHours)\nconst pendingHours = computed(() => timesheetStore.pendingHours)\nconst activeProjects = computed(() => timesheetStore.activeProjects)\n\n// Build timesheet grid from existing entries like PersonnelProfile.vue\nconst timesheetGridData = computed(() => {\n  const taskGroups = {}\n  \n  // Process timesheet entries from the store\n  Object.entries(monthlyEntries.value).forEach(([date, dayEntries]) => {\n    Object.entries(dayEntries).forEach(([taskKey, hours]) => {\n      if (!taskGroups[taskKey]) {\n        // Find project task info\n        const projectTask = projectTasks.value.find(pt => pt.id === taskKey)\n        taskGroups[taskKey] = {\n          taskId: taskKey,\n          taskName: projectTask?.task_name || 'Attività Generica',\n          projectName: projectTask?.project_name || 'Progetto Sconosciuto', \n          assignees: 'Tu',\n          hours: {},\n          total: 0\n        }\n      }\n      \n      taskGroups[taskKey].hours[date] = parseFloat(hours || 0).toFixed(1)\n      taskGroups[taskKey].total += parseFloat(hours || 0)\n    })\n  })\n  \n  // Format totals\n  return Object.values(taskGroups).map(task => ({\n    ...task,\n    total: task.total.toFixed(1)\n  }))\n})\n\n// Methods\nconst previousMonth = () => {\n  timesheetStore.navigateMonth('previous')\n}\n\nconst nextMonth = () => {\n  timesheetStore.navigateMonth('next')\n}\n\nconst clearError = () => {\n  timesheetStore.clearError()\n}\n\n\n\n// Hours modal functions\nconst closeHoursModal = () => {\n  showAddHoursModal.value = false\n  resetHoursForm()\n  availableTasks.value = []\n}\n\nconst resetHoursForm = () => {\n  hoursForm.value = {\n    project_id: '',\n    task_id: '',\n    date: new Date().toISOString().split('T')[0],\n    hours: 0,\n    description: '',\n    billable: true,\n    billing_rate: null\n  }\n}\n\nconst onProjectChange = async () => {\n  hoursForm.value.task_id = ''\n  availableTasks.value = []\n\n  if (hoursForm.value.project_id) {\n    await loadTasksForProject(hoursForm.value.project_id)\n\n    // Set default billing rate from project contract\n    const project = selectedProject.value\n    if (project?.contract?.hourly_rate && canViewBilling.value) {\n      hoursForm.value.billing_rate = project.contract.hourly_rate\n    }\n  }\n}\n\nconst loadTasksForProject = async (projectId) => {\n  try {\n    const response = await api.get(`/api/tasks?project_id=${projectId}&status=open`)\n    if (response.data.success) {\n      availableTasks.value = response.data.data.tasks || []\n    }\n  } catch (error) {\n    console.error('Error loading tasks:', error)\n    availableTasks.value = []\n  }\n}\n\nconst saveHours = async () => {\n  saving.value = true\n\n  try {\n    const payload = {\n      user_id: authStore.user.id,\n      project_id: parseInt(hoursForm.value.project_id),\n      task_id: hoursForm.value.task_id ? parseInt(hoursForm.value.task_id) : null,\n      date: hoursForm.value.date,\n      hours: parseFloat(hoursForm.value.hours),\n      description: hoursForm.value.description,\n      billable: hoursForm.value.billable,\n      billing_rate: hoursForm.value.billable && hoursForm.value.billing_rate ?\n                   parseFloat(hoursForm.value.billing_rate) : null\n    }\n\n    const response = await api.post('/api/timesheets', payload)\n\n    if (response.data.success) {\n      closeHoursModal()\n      // Refresh timesheet data\n      await timesheetStore.loadMonthlyData()\n    } else {\n      throw new Error(response.data.message || 'Errore durante il salvataggio')\n    }\n  } catch (error) {\n    console.error('Error saving hours:', error)\n    // You might want to show an error message to the user\n  } finally {\n    saving.value = false\n  }\n}\n\nconst removeProjectTask = (projectTask) => {\n  if (confirm('Rimuovere questo progetto dalla griglia? Le ore registrate non verranno eliminate.')) {\n    // Remove from local projectTasks - this should be handled by the store\n    const index = projectTasks.value.findIndex(pt => \n      pt.project_id === projectTask.project_id && pt.task_id === projectTask.task_id\n    )\n    if (index !== -1) {\n      projectTasks.value.splice(index, 1)\n    }\n  }\n}\n\nconst getEntryValue = (projectTaskId, day) => {\n  const entryKey = `${currentYear.value}-${currentMonth.value}-${day}`\n  return monthlyEntries.value[entryKey]?.[projectTaskId] || 0\n}\n\nconst getRowTotal = (projectTask) => {\n  return daysInMonth.value.reduce((sum, day) => {\n    return sum + (getEntryValue(projectTask.id, day) || 0)\n  }, 0)\n}\n\nconst getDateString = (day) => {\n  return `${currentYear.value}-${currentMonth.value.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`\n}\n\nconst getMonthName = (month) => {\n  const names = [\n    'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',\n    'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'\n  ]\n  return names[month - 1]\n}\n\n// Utility functions\nconst formatCurrency = (amount) => {\n  return new Intl.NumberFormat('it-IT', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(amount || 0)\n}\n\nconst loadUserProjects = async () => {\n  try {\n    const response = await api.get('/api/projects/user')\n    if (response.data.success) {\n      userProjects.value = response.data.data.projects || []\n    }\n  } catch (error) {\n    console.error('Error loading user projects:', error)\n    userProjects.value = []\n  }\n}\n\n// Lifecycle\nonMounted(async () => {\n  // Load data from store with caching\n  await Promise.all([\n    timesheetStore.loadAvailableProjects(),\n    timesheetStore.loadMonthlyData(),\n    loadUserProjects()\n  ])\n})\n</script>", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- <PERSON>rro<PERSON> -->\n    <div v-if=\"error\" class=\"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4\">\n      <div class=\"flex\">\n        <div class=\"flex-shrink-0\">\n          <svg class=\"h-5 w-5 text-red-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z\"></path>\n          </svg>\n        </div>\n        <div class=\"ml-3\">\n          <p class=\"text-sm text-red-800 dark:text-red-200\">{{ error }}</p>\n        </div>\n        <div class=\"ml-auto pl-3\">\n          <div class=\"-mx-1.5 -my-1.5\">\n            <button @click=\"clearError\" class=\"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900\">\n              <svg class=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Le Mie Ore</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Registra le tue ore di lavoro con la griglia mensile\n          </p>\n        </div>\n\n        <!-- Controlli periodo -->\n        <div class=\"flex items-center space-x-4\">\n          <div class=\"flex items-center space-x-2\">\n            <button\n              @click=\"previousMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\"></path>\n              </svg>\n            </button>\n\n            <div class=\"text-center\">\n              <h2 class=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n              </h2>\n            </div>\n\n            <button\n              @click=\"nextMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n              </svg>\n            </button>\n          </div>\n\n          <button\n            @click=\"showAddHoursModal = true\"\n            class=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n            </svg>\n            Aggiungi Ore\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Griglia Timesheet Mensile -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Griglia Ore - {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n          </h3>\n          <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n            Totale: {{ formatHours(totalHours) }} | Fatturabili: {{ formatHours(billableHours) }}\n          </div>\n        </div>\n      </div>\n\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <!-- Header con giorni del mese -->\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600\">\n                Progetto/Task\n              </th>\n              <th\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day, currentMonth, currentYear),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day, currentMonth, currentYear)\n                }\"\n              >\n                <div>{{ day }}</div>\n                <div class=\"text-xs text-gray-400\">{{ getDayName(day, currentMonth, currentYear) }}</div>\n              </th>\n              <th class=\"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600\">\n                Totale\n              </th>\n            </tr>\n          </thead>\n\n          <!-- Righe progetti/task -->\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-if=\"timesheetGridData.length === 0\">\n              <td :colspan=\"daysInMonth.length + 2\" class=\"px-6 py-8 text-center text-gray-500 dark:text-gray-400\">\n                <div class=\"space-y-2\">\n                  <p>Nessun timesheet registrato per {{ getMonthName(currentMonth) }} {{ currentYear }}</p>\n                  <button\n                    @click=\"showAddHoursModal = true\"\n                    class=\"text-primary-600 hover:text-primary-700 dark:text-primary-400\"\n                  >\n                    Registra le tue prime ore\n                  </button>\n                </div>\n              </td>\n            </tr>\n            \n            <tr v-for=\"taskRow in timesheetGridData\" :key=\"taskRow.taskId\">\n              <!-- Colonna progetto/task -->\n              <td class=\"sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600\">\n                <div class=\"flex items-center justify-between\">\n                  <div class=\"min-w-0 flex-1\">\n                    <p class=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                      {{ taskRow.projectName }}\n                    </p>\n                    <p v-if=\"taskRow.taskName\" class=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\n                      {{ taskRow.taskName }}\n                    </p>\n                    <p class=\"text-xs text-gray-500 dark:text-gray-400\">\n                      {{ taskRow.assignees }}\n                    </p>\n                  </div>\n                </div>\n              </td>\n              \n              <!-- Colonne giorni -->\n              <td\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-1 py-3 text-center\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day, currentMonth, currentYear),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day, currentMonth, currentYear)\n                }\"\n              >\n                <div v-if=\"taskRow.hours[getDateString(day)]\" class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ taskRow.hours[getDateString(day)] }}\n                </div>\n                <div v-else class=\"text-gray-300 dark:text-gray-600\">-</div>\n              </td>\n              \n              <!-- Colonna totale riga -->\n              <td class=\"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 border-l border-gray-200 dark:border-gray-600\">\n                {{ taskRow.total }}\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Quick Stats -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Totali\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(totalHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Fatturabili\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(billableHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                In Attesa\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(pendingHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Progetti Attivi\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ activeProjects }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal Aggiungi Ore -->\n    <div v-if=\"showAddHoursModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\" @click=\"closeHoursModal\">\n      <div class=\"relative top-20 mx-auto p-5 border w-[400px] shadow-lg rounded-md bg-white dark:bg-gray-800\" @click.stop>\n        <div class=\"mt-3\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n            Aggiungi Ore\n          </h3>\n\n          <form @submit.prevent=\"saveHours\">\n            <div class=\"grid grid-cols-1 gap-4\">\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Progetto</label>\n                <select\n                  v-model=\"hoursForm.project_id\"\n                  @change=\"onProjectChange\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n                  <option value=\"\">Seleziona progetto</option>\n                  <option\n                    v-for=\"project in userProjects\"\n                    :key=\"project.id\"\n                    :value=\"project.id\"\n                  >\n                    {{ project.name }}\n                  </option>\n                </select>\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Task</label>\n                <select\n                  v-model=\"hoursForm.task_id\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n                  <option value=\"\">Seleziona task</option>\n                  <option\n                    v-for=\"task in availableTasks\"\n                    :key=\"task.id\"\n                    :value=\"task.id\"\n                  >\n                    {{ task.name }}\n                  </option>\n                </select>\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Data</label>\n                <input\n                  v-model=\"hoursForm.date\"\n                  type=\"date\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Ore</label>\n                <input\n                  v-model=\"hoursForm.hours\"\n                  type=\"number\"\n                  step=\"0.25\"\n                  min=\"0\"\n                  max=\"24\"\n                  required\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                >\n              </div>\n\n              <div>\n                <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">Descrizione</label>\n                <textarea\n                  v-model=\"hoursForm.description\"\n                  rows=\"3\"\n                  class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                ></textarea>\n              </div>\n\n              <!-- Sezione Billing (solo per manager/admin) -->\n              <div v-if=\"canViewBilling\" class=\"border-t border-gray-200 dark:border-gray-600 pt-4\">\n                <h4 class=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">Informazioni Fatturazione</h4>\n\n                <div class=\"space-y-3\">\n                  <div class=\"flex items-center justify-between\">\n                    <label class=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        v-model=\"hoursForm.billable\"\n                        :value=\"true\"\n                        class=\"text-primary-600 focus:ring-primary-500\"\n                      />\n                      <span class=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">Sì - Fatturabile</span>\n                    </label>\n                    <label class=\"flex items-center\">\n                      <input\n                        type=\"radio\"\n                        v-model=\"hoursForm.billable\"\n                        :value=\"false\"\n                        class=\"text-primary-600 focus:ring-primary-500\"\n                      />\n                      <span class=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">No - Non fatturabile</span>\n                    </label>\n                  </div>\n\n                  <div v-if=\"hoursForm.billable\">\n                    <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                      Tariffa (€/h)\n                    </label>\n                    <input\n                      v-model=\"hoursForm.billing_rate\"\n                      type=\"number\"\n                      step=\"0.01\"\n                      min=\"0\"\n                      class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n                    >\n                    <p class=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                      Tariffa contrattuale: {{ formatCurrency(selectedProject?.contract?.hourly_rate || 0) }}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"flex justify-end space-x-3 mt-6\">\n              <button\n                type=\"button\"\n                @click=\"closeHoursModal\"\n                class=\"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md\"\n              >\n                Annulla\n              </button>\n              <button\n                type=\"submit\"\n                :disabled=\"saving\"\n                class=\"px-4 py-2 text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 rounded-md disabled:opacity-50\"\n              >\n                {{ saving ? 'Salvataggio...' : 'Aggiungi' }}\n              </button>\n            </div>\n          </form>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useTimesheetStore } from '@/stores/timesheet'\nimport { usePermissions } from '@/composables/usePermissions'\nimport { useAuthStore } from '@/stores/auth'\nimport {\n  monthNames,\n  formatHours,\n  isToday,\n  isWeekend,\n  getDayName,\n  validateHours\n} from '@/utils/timesheet'\nimport api from '@/utils/api'\n\nconst timesheetStore = useTimesheetStore()\nconst { hasPermission, isManager, isAdmin } = usePermissions()\nconst authStore = useAuthStore()\n\n// Local state\nconst showAddHoursModal = ref(false)\nconst availableTasks = ref([])\nconst userProjects = ref([])\nconst saving = ref(false)\n\n// Form data\nconst hoursForm = ref({\n  project_id: '',\n  task_id: '',\n  date: new Date().toISOString().split('T')[0],\n  hours: 0,\n  description: '',\n  billable: true,\n  billing_rate: null\n})\n\n// Computed properties from store\nconst currentYear = computed(() => timesheetStore.currentYear)\nconst currentMonth = computed(() => timesheetStore.currentMonth)\nconst projectTasks = computed(() => timesheetStore.projectTasks)\nconst monthlyEntries = computed(() => timesheetStore.monthlyEntries)\nconst availableProjects = computed(() => timesheetStore.availableProjects)\nconst loading = computed(() => timesheetStore.loading.monthlyData || timesheetStore.loading.saving)\nconst error = computed(() => timesheetStore.error)\n\n// New computed properties for hours modal\nconst canViewBilling = computed(() => isManager.value || isAdmin.value)\n\nconst selectedProject = computed(() => {\n  return userProjects.value.find(p => p.id === parseInt(hoursForm.value.project_id))\n})\n\n// Computed calculations\nconst daysInMonth = computed(() => timesheetStore.daysInMonth)\nconst totalHours = computed(() => timesheetStore.totalHours)\nconst billableHours = computed(() => timesheetStore.billableHours)\nconst pendingHours = computed(() => timesheetStore.pendingHours)\nconst activeProjects = computed(() => timesheetStore.activeProjects)\n\n// Build timesheet grid from existing entries like PersonnelProfile.vue\nconst timesheetGridData = computed(() => {\n  const taskGroups = {}\n  \n  // Process timesheet entries from the store\n  Object.entries(monthlyEntries.value).forEach(([date, dayEntries]) => {\n    Object.entries(dayEntries).forEach(([taskKey, hours]) => {\n      if (!taskGroups[taskKey]) {\n        // Find project task info\n        const projectTask = projectTasks.value.find(pt => pt.id === taskKey)\n        taskGroups[taskKey] = {\n          taskId: taskKey,\n          taskName: projectTask?.task_name || 'Attività Generica',\n          projectName: projectTask?.project_name || 'Progetto Sconosciuto', \n          assignees: 'Tu',\n          hours: {},\n          total: 0\n        }\n      }\n      \n      taskGroups[taskKey].hours[date] = parseFloat(hours || 0).toFixed(1)\n      taskGroups[taskKey].total += parseFloat(hours || 0)\n    })\n  })\n  \n  // Format totals\n  return Object.values(taskGroups).map(task => ({\n    ...task,\n    total: task.total.toFixed(1)\n  }))\n})\n\n// Methods\nconst previousMonth = () => {\n  timesheetStore.navigateMonth('previous')\n}\n\nconst nextMonth = () => {\n  timesheetStore.navigateMonth('next')\n}\n\nconst clearError = () => {\n  timesheetStore.clearError()\n}\n\n\n\n// Hours modal functions\nconst closeHoursModal = () => {\n  showAddHoursModal.value = false\n  resetHoursForm()\n  availableTasks.value = []\n}\n\nconst resetHoursForm = () => {\n  hoursForm.value = {\n    project_id: '',\n    task_id: '',\n    date: new Date().toISOString().split('T')[0],\n    hours: 0,\n    description: '',\n    billable: true,\n    billing_rate: null\n  }\n}\n\nconst onProjectChange = async () => {\n  hoursForm.value.task_id = ''\n  availableTasks.value = []\n\n  if (hoursForm.value.project_id) {\n    await loadTasksForProject(hoursForm.value.project_id)\n\n    // Set default billing rate from project contract\n    const project = selectedProject.value\n    if (project?.contract?.hourly_rate && canViewBilling.value) {\n      hoursForm.value.billing_rate = project.contract.hourly_rate\n    }\n  }\n}\n\nconst loadTasksForProject = async (projectId) => {\n  try {\n    const response = await api.get(`/api/tasks?project_id=${projectId}&status=open`)\n    if (response.data.success) {\n      availableTasks.value = response.data.data.tasks || []\n    }\n  } catch (error) {\n    console.error('Error loading tasks:', error)\n    availableTasks.value = []\n  }\n}\n\nconst saveHours = async () => {\n  saving.value = true\n\n  try {\n    const payload = {\n      user_id: authStore.user.id,\n      project_id: parseInt(hoursForm.value.project_id),\n      task_id: hoursForm.value.task_id ? parseInt(hoursForm.value.task_id) : null,\n      date: hoursForm.value.date,\n      hours: parseFloat(hoursForm.value.hours),\n      description: hoursForm.value.description,\n      billable: hoursForm.value.billable,\n      billing_rate: hoursForm.value.billable && hoursForm.value.billing_rate ?\n                   parseFloat(hoursForm.value.billing_rate) : null\n    }\n\n    const response = await api.post('/api/timesheets', payload)\n\n    if (response.data.success) {\n      closeHoursModal()\n      // Refresh timesheet data\n      await timesheetStore.loadMonthlyData()\n    } else {\n      throw new Error(response.data.message || 'Errore durante il salvataggio')\n    }\n  } catch (error) {\n    console.error('Error saving hours:', error)\n    // You might want to show an error message to the user\n  } finally {\n    saving.value = false\n  }\n}\n\nconst getEntryValue = (projectTaskId, day) => {\n  const entryKey = `${currentYear.value}-${currentMonth.value}-${day}`\n  return monthlyEntries.value[entryKey]?.[projectTaskId] || 0\n}\n\nconst getDateString = (day) => {\n  return `${currentYear.value}-${currentMonth.value.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`\n}\n\nconst getMonthName = (month) => {\n  const names = [\n    'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',\n    'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'\n  ]\n  return names[month - 1]\n}\n\n// Utility functions\nconst formatCurrency = (amount) => {\n  return new Intl.NumberFormat('it-IT', {\n    style: 'currency',\n    currency: 'EUR'\n  }).format(amount || 0)\n}\n\nconst loadUserProjects = async () => {\n  try {\n    const response = await api.get('/api/projects/user')\n    if (response.data.success) {\n      userProjects.value = response.data.data.projects || []\n    }\n  } catch (error) {\n    console.error('Error loading user projects:', error)\n    userProjects.value = []\n  }\n}\n\n// Lifecycle\nonMounted(async () => {\n  // Load data from store with caching\n  await Promise.all([\n    timesheetStore.loadAvailableProjects(),\n    timesheetStore.loadMonthlyData(),\n    loadUserProjects()\n  ])\n})\n</script>"}