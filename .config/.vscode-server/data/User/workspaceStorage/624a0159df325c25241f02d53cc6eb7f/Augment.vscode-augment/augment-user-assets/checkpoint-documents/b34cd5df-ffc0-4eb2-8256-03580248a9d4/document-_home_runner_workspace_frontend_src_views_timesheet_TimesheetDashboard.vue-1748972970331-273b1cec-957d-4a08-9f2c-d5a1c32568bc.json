{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetDashboard.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Dashboard Timesheet</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Panoramica ore e approvazioni del team\n          </p>\n        </div>\n        \n        <div class=\"flex space-x-3\">\n          <button \n            @click=\"refreshData\"\n            :disabled=\"loading\"\n            class=\"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50\"\n          >\n            <svg v-if=\"loading\" class=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n              <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            {{ loading ? 'Aggiornamento...' : 'Aggiorna' }}\n          </button>\n          \n          <router-link \n            to=\"/app/timesheet/entry\"\n            class=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Registra Ore\n          </router-link>\n        </div>\n      </div>\n    </div>\n\n    <!-- Quick Stats -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Questa Settimana\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(stats.weeklyHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Questo Mese\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(stats.monthlyHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\" v-if=\"canApprove\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Da Approvare\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ stats.pendingApprovals }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Efficienza\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ stats.efficiency }}%\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Recent Activity & Quick Actions -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n      <!-- Recent Timesheets -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n        <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Attività Recenti\n          </h3>\n        </div>\n        <div class=\"p-6\">\n          <div class=\"space-y-4\">\n            <div v-for=\"activity in recentActivities\" :key=\"activity.id\" class=\"flex items-center space-x-3\">\n              <div class=\"flex-shrink-0\">\n                <div class=\"w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center\">\n                  <svg class=\"w-4 h-4 text-gray-600 dark:text-gray-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                  </svg>\n                </div>\n              </div>\n              <div class=\"flex-1 min-w-0\">\n                <p class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ activity.description }}\n                </p>\n                <p class=\"text-sm text-gray-500 dark:text-gray-400\">\n                  {{ formatDate(activity.created_at) }}\n                </p>\n              </div>\n              <div class=\"flex-shrink-0\">\n                <span class=\"text-sm font-medium text-primary-600 dark:text-primary-400\">\n                  {{ formatHours(activity.hours) }}\n                </span>\n              </div>\n            </div>\n          </div>\n          \n          <div v-if=\"recentActivities.length === 0\" class=\"text-center py-4\">\n            <p class=\"text-gray-500 dark:text-gray-400\">Nessuna attività recente</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- Pending Approvals (Manager Only) -->\n      <div v-if=\"canApprove\" class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n        <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <div class=\"flex justify-between items-center\">\n            <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Approvazioni in Attesa\n            </h3>\n            <router-link \n              to=\"/app/timesheet/approvals\"\n              class=\"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 text-sm\"\n            >\n              Vedi Tutte →\n            </router-link>\n          </div>\n        </div>\n        <div class=\"p-6\">\n          <div class=\"space-y-4\">\n            <div v-for=\"approval in pendingApprovals\" :key=\"approval.id\" class=\"flex items-center justify-between\">\n              <div class=\"flex items-center space-x-3\">\n                <div class=\"flex-shrink-0\">\n                  <div class=\"w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center\">\n                    <svg class=\"w-4 h-4 text-yellow-600 dark:text-yellow-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                    </svg>\n                  </div>\n                </div>\n                <div>\n                  <p class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {{ approval.user?.first_name }} {{ approval.user?.last_name }}\n                  </p>\n                  <p class=\"text-sm text-gray-500 dark:text-gray-400\">\n                    {{ approval.month }}/{{ approval.year }} - {{ formatHours(approval.total_hours) }}\n                  </p>\n                </div>\n              </div>\n              <div class=\"flex space-x-2\">\n                <button \n                  @click=\"quickApprove(approval.id)\"\n                  class=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 text-sm\"\n                >\n                  Approva\n                </button>\n                <button \n                  @click=\"viewApprovalDetails(approval)\"\n                  class=\"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 text-sm\"\n                >\n                  Dettagli\n                </button>\n              </div>\n            </div>\n          </div>\n          \n          <div v-if=\"pendingApprovals.length === 0\" class=\"text-center py-4\">\n            <p class=\"text-gray-500 dark:text-gray-400\">Nessuna approvazione in attesa</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- Team Summary (Employee View) -->\n      <div v-else class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n        <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Il Mio Stato\n          </h3>\n        </div>\n        <div class=\"p-6\">\n          <div class=\"space-y-4\">\n            <div class=\"flex justify-between items-center\">\n              <span class=\"text-sm text-gray-500 dark:text-gray-400\">Timesheet Corrente</span>\n              <span \n                class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                :class=\"getStatusClass(myStatus.status)\"\n              >\n                {{ getStatusText(myStatus.status) }}\n              </span>\n            </div>\n            <div class=\"flex justify-between items-center\">\n              <span class=\"text-sm text-gray-500 dark:text-gray-400\">Ore Registrate</span>\n              <span class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(myStatus.totalHours) }}\n              </span>\n            </div>\n            <div class=\"flex justify-between items-center\">\n              <span class=\"text-sm text-gray-500 dark:text-gray-400\">Ore Fatturabili</span>\n              <span class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(myStatus.billableHours) }}\n              </span>\n            </div>\n          </div>\n          \n          <div class=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-600\">\n            <router-link \n              to=\"/app/timesheet/status\"\n              class=\"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 text-sm\"\n            >\n              Vedi Stato Completo →\n            </router-link>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst authStore = useAuthStore()\n\n// State\nconst loading = ref(false)\nconst stats = ref({\n  weeklyHours: 0,\n  monthlyHours: 0,\n  pendingApprovals: 0,\n  efficiency: 0\n})\nconst recentActivities = ref([])\nconst pendingApprovals = ref([])\nconst myStatus = ref({\n  status: 'draft',\n  totalHours: 0,\n  billableHours: 0\n})\n\n// Computed\nconst canApprove = computed(() => {\n  return authStore.user?.role === 'manager' || authStore.user?.role === 'admin'\n})\n\n// Methods\nconst loadDashboardStats = async () => {\n  try {\n    const response = await fetch('/api/dashboard/stats', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      // Map dashboard stats to timesheet specific stats\n      stats.value = {\n        weeklyHours: result.data?.activities?.recent_timesheets || 0,\n        monthlyHours: result.data?.activities?.recent_timesheets || 0,\n        pendingApprovals: result.data?.activities?.unread_notifications || 0,\n        efficiency: 85 // TODO: Calculate from actual data\n      }\n    }\n  } catch (err) {\n    console.error('Error loading dashboard stats:', err)\n  }\n}\n\nconst loadRecentActivities = async () => {\n  try {\n    const response = await fetch('/api/timesheets/?per_page=5&page=1', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      const entries = result.data || []\n\n      recentActivities.value = entries.map(entry => ({\n        id: entry.id,\n        description: `${entry.project_name || 'Progetto'} - ${entry.task_name || 'Task'}`,\n        hours: entry.hours,\n        created_at: entry.created_at,\n        date: entry.date\n      }))\n    }\n  } catch (err) {\n    console.error('Error loading recent activities:', err)\n    recentActivities.value = []\n  }\n}\n\nconst loadPendingApprovals = async () => {\n  if (!canApprove.value) return\n\n  try {\n    const response = await fetch('/api/monthly-timesheets/?status=submitted', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      pendingApprovals.value = result.data || []\n    }\n  } catch (err) {\n    console.error('Error loading pending approvals:', err)\n  }\n}\n\nconst loadMyStatus = async () => {\n  if (canApprove.value) return\n\n  try {\n    const currentDate = new Date()\n    const response = await fetch(`/api/monthly-timesheets/generate`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      },\n      body: JSON.stringify({\n        year: currentDate.getFullYear(),\n        month: currentDate.getMonth() + 1\n      })\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      myStatus.value = {\n        status: result.data?.status || 'draft',\n        totalHours: result.data?.total_hours || 0,\n        billableHours: result.data?.billable_hours || 0\n      }\n    }\n  } catch (err) {\n    console.error('Error loading my status:', err)\n  }\n}\n\nconst refreshData = async () => {\n  loading.value = true\n  \n  try {\n    await Promise.all([\n      loadDashboardStats(),\n      loadRecentActivities(),\n      loadPendingApprovals(),\n      loadMyStatus()\n    ])\n  } finally {\n    loading.value = false\n  }\n}\n\nconst quickApprove = async (timesheetId) => {\n  try {\n    const response = await fetch(`/api/monthly-timesheets/${timesheetId}/approve`, {\n      method: 'PUT',\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      await loadPendingApprovals()\n    }\n  } catch (err) {\n    console.error('Error approving timesheet:', err)\n  }\n}\n\nconst viewApprovalDetails = (approval) => {\n  // Navigate to detailed approval view\n  window.location.href = `/app/timesheet/approvals?timesheet=${approval.id}`\n}\n\nconst formatHours = (hours) => {\n  if (!hours || hours === 0) return '0h'\n  return hours % 1 === 0 ? `${hours}h` : `${hours.toFixed(1)}h`\n}\n\nconst formatDate = (dateString) => {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('it-IT')\n}\n\nconst getStatusClass = (status) => {\n  switch (status) {\n    case 'approved':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n    case 'submitted':\n      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n    case 'rejected':\n      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n    default:\n      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\n  }\n}\n\nconst getStatusText = (status) => {\n  switch (status) {\n    case 'approved':\n      return 'Approvato'\n    case 'submitted':\n      return 'In Attesa'\n    case 'rejected':\n      return 'Rifiutato'\n    default:\n      return 'Bozza'\n  }\n}\n\n// Lifecycle\nonMounted(() => {\n  refreshData()\n})\n</script>\n", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- <PERSON>rro<PERSON> -->\n    <div v-if=\"error\" class=\"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4\">\n      <div class=\"flex\">\n        <div class=\"flex-shrink-0\">\n          <svg class=\"h-5 w-5 text-red-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z\"></path>\n          </svg>\n        </div>\n        <div class=\"ml-3\">\n          <p class=\"text-sm text-red-800 dark:text-red-200\">{{ error }}</p>\n        </div>\n        <div class=\"ml-auto pl-3\">\n          <div class=\"-mx-1.5 -my-1.5\">\n            <button @click=\"clearError\" class=\"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900\">\n              <svg class=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Dashboard Timesheet</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Panoramica ore e approvazioni del team\n          </p>\n        </div>\n        \n        <div class=\"flex space-x-3\">\n          <button \n            @click=\"refreshData\"\n            :disabled=\"loading\"\n            class=\"bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium disabled:opacity-50\"\n          >\n            <svg v-if=\"loading\" class=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n              <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            {{ loading ? 'Aggiornamento...' : 'Aggiorna' }}\n          </button>\n          \n          <router-link \n            to=\"/app/timesheet/entry\"\n            class=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Registra Ore\n          </router-link>\n        </div>\n      </div>\n    </div>\n\n    <!-- Quick Stats -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Questa Settimana\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(stats.weeklyHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Questo Mese\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(stats.monthlyHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\" v-if=\"canApprove\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Da Approvare\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ stats.pendingApprovals }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Efficienza\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ stats.efficiency }}%\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Recent Activity & Quick Actions -->\n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n      <!-- Recent Timesheets -->\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n        <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Attività Recenti\n          </h3>\n        </div>\n        <div class=\"p-6\">\n          <div class=\"space-y-4\">\n            <div v-for=\"activity in recentActivities\" :key=\"activity.id\" class=\"flex items-center space-x-3\">\n              <div class=\"flex-shrink-0\">\n                <div class=\"w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full flex items-center justify-center\">\n                  <svg class=\"w-4 h-4 text-gray-600 dark:text-gray-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                  </svg>\n                </div>\n              </div>\n              <div class=\"flex-1 min-w-0\">\n                <p class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ activity.description }}\n                </p>\n                <p class=\"text-sm text-gray-500 dark:text-gray-400\">\n                  {{ formatDate(activity.created_at) }}\n                </p>\n              </div>\n              <div class=\"flex-shrink-0\">\n                <span class=\"text-sm font-medium text-primary-600 dark:text-primary-400\">\n                  {{ formatHours(activity.hours) }}\n                </span>\n              </div>\n            </div>\n          </div>\n          \n          <div v-if=\"recentActivities.length === 0\" class=\"text-center py-4\">\n            <p class=\"text-gray-500 dark:text-gray-400\">Nessuna attività recente</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- Pending Approvals (Manager Only) -->\n      <div v-if=\"canApprove\" class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n        <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <div class=\"flex justify-between items-center\">\n            <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Approvazioni in Attesa\n            </h3>\n            <router-link \n              to=\"/app/timesheet/approvals\"\n              class=\"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 text-sm\"\n            >\n              Vedi Tutte →\n            </router-link>\n          </div>\n        </div>\n        <div class=\"p-6\">\n          <div class=\"space-y-4\">\n            <div v-for=\"approval in pendingApprovals\" :key=\"approval.id\" class=\"flex items-center justify-between\">\n              <div class=\"flex items-center space-x-3\">\n                <div class=\"flex-shrink-0\">\n                  <div class=\"w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center\">\n                    <svg class=\"w-4 h-4 text-yellow-600 dark:text-yellow-300\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                    </svg>\n                  </div>\n                </div>\n                <div>\n                  <p class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {{ approval.user?.first_name }} {{ approval.user?.last_name }}\n                  </p>\n                  <p class=\"text-sm text-gray-500 dark:text-gray-400\">\n                    {{ approval.month }}/{{ approval.year }} - {{ formatHours(approval.total_hours) }}\n                  </p>\n                </div>\n              </div>\n              <div class=\"flex space-x-2\">\n                <button \n                  @click=\"quickApprove(approval.id)\"\n                  class=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 text-sm\"\n                >\n                  Approva\n                </button>\n                <button \n                  @click=\"viewApprovalDetails(approval)\"\n                  class=\"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 text-sm\"\n                >\n                  Dettagli\n                </button>\n              </div>\n            </div>\n          </div>\n          \n          <div v-if=\"pendingApprovals.length === 0\" class=\"text-center py-4\">\n            <p class=\"text-gray-500 dark:text-gray-400\">Nessuna approvazione in attesa</p>\n          </div>\n        </div>\n      </div>\n\n      <!-- Team Summary (Employee View) -->\n      <div v-else class=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n        <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Il Mio Stato\n          </h3>\n        </div>\n        <div class=\"p-6\">\n          <div class=\"space-y-4\">\n            <div class=\"flex justify-between items-center\">\n              <span class=\"text-sm text-gray-500 dark:text-gray-400\">Timesheet Corrente</span>\n              <span \n                class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                :class=\"getStatusClass(myStatus.status)\"\n              >\n                {{ getStatusText(myStatus.status) }}\n              </span>\n            </div>\n            <div class=\"flex justify-between items-center\">\n              <span class=\"text-sm text-gray-500 dark:text-gray-400\">Ore Registrate</span>\n              <span class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(myStatus.totalHours) }}\n              </span>\n            </div>\n            <div class=\"flex justify-between items-center\">\n              <span class=\"text-sm text-gray-500 dark:text-gray-400\">Ore Fatturabili</span>\n              <span class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(myStatus.billableHours) }}\n              </span>\n            </div>\n          </div>\n          \n          <div class=\"mt-4 pt-4 border-t border-gray-200 dark:border-gray-600\">\n            <router-link \n              to=\"/app/timesheet/status\"\n              class=\"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 text-sm\"\n            >\n              Vedi Stato Completo →\n            </router-link>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { computed, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { useTimesheetStore } from '@/stores/timesheet'\nimport { formatHours, formatDate, getStatusClass, getStatusText } from '@/utils/timesheet'\n\nconst router = useRouter()\nconst timesheetStore = useTimesheetStore()\n\n// Computed properties from store\nconst loading = computed(() => timesheetStore.loading.dashboard)\nconst stats = computed(() => timesheetStore.stats)\nconst recentActivities = computed(() => timesheetStore.recentActivities)\nconst pendingApprovals = computed(() => timesheetStore.pendingApprovals)\nconst myStatus = computed(() => timesheetStore.myStatus)\nconst canApprove = computed(() => timesheetStore.canApprove)\nconst error = computed(() => timesheetStore.error)\n\n// Methods\nconst refreshData = async () => {\n  await timesheetStore.refreshAll()\n}\n\nconst quickApprove = async (timesheetId) => {\n  const success = await timesheetStore.approveTimesheet(timesheetId)\n  if (!success && timesheetStore.error) {\n    // Error is stored in store, can be displayed via error computed\n    console.error('Failed to approve timesheet')\n  }\n}\n\nconst viewApprovalDetails = (approval) => {\n  router.push(`/app/timesheet/approvals?timesheet=${approval.id}`)\n}\n\nconst clearError = () => {\n  timesheetStore.clearError()\n}\n\n// Lifecycle\nonMounted(() => {\n  // Load dashboard data with caching\n  Promise.all([\n    timesheetStore.loadDashboardStats(),\n    timesheetStore.loadRecentActivities(),\n    timesheetStore.loadPendingApprovals(),\n    timesheetStore.loadMyStatus()\n  ])\n})\n</script>\n"}