{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetHistory.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Storico Timesheet</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Visualizza i riepiloghi mensili delle tue ore lavorate\n          </p>\n        </div>\n\n        <div class=\"flex space-x-3\">\n          <button\n            @click=\"exportData\"\n            class=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n            </svg>\n            Esporta Report\n          </button>\n          <router-link\n            to=\"/app/timesheet/entry\"\n            class=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n            </svg>\n            Registra Ore\n          </router-link>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filtri -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Anno\n          </label>\n          <select\n            v-model=\"selectedYear\"\n            @change=\"loadMonthlySummaries\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n            <option v-for=\"year in availableYears\" :key=\"year\" :value=\"year\">\n              {{ year }}\n            </option>\n          </select>\n        </div>\n\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Progetto\n          </label>\n          <select\n            v-model=\"selectedProject\"\n            @change=\"loadMonthlySummaries\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n            <option value=\"\">Tutti i progetti</option>\n            <option\n              v-for=\"project in projects\"\n              :key=\"project.id\"\n              :value=\"project.id\"\n            >\n              {{ project.name }}\n            </option>\n          </select>\n        </div>\n\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Visualizzazione\n          </label>\n          <select\n            v-model=\"viewMode\"\n            @change=\"loadMonthlySummaries\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n            <option value=\"monthly\">Riepilogo Mensile</option>\n            <option value=\"detailed\">Dettaglio Giornaliero</option>\n          </select>\n        </div>\n\n        <div class=\"flex items-end\">\n          <button\n            @click=\"loadMonthlySummaries\"\n            :disabled=\"loading\"\n            class=\"w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            {{ loading ? 'Caricando...' : 'Aggiorna' }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Statistiche Periodo -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Totali\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(summary.totalHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Fatturabili\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(summary.billableHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Progetti Attivi\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ summary.activeProjects }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Media Giornaliera\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(summary.dailyAverage) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Riepiloghi Mensili -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            {{ viewMode === 'monthly' ? 'Riepiloghi Mensili' : 'Dettaglio Giornaliero' }} - {{ selectedYear }}\n          </h3>\n          <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n            {{ monthlySummaries.length }} {{ viewMode === 'monthly' ? 'mesi' : 'giorni' }} trovati\n          </div>\n        </div>\n      </div>\n\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr v-if=\"viewMode === 'monthly'\">\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Mese\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Ore Totali\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Ore Fatturabili\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Produttività\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Progetti\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Media Giornaliera\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Stato\n              </th>\n              <th class=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Azioni\n              </th>\n            </tr>\n            <tr v-else>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Data\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Progetto\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Task\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Ore\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Fatturabile\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Stato\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Descrizione\n              </th>\n            </tr>\n          </thead>\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <!-- Monthly View -->\n            <template v-if=\"viewMode === 'monthly'\">\n              <tr v-for=\"month in monthlySummaries\" :key=\"month.month\" class=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"flex items-center\">\n                    <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {{ getMonthName(month.month) }} {{ selectedYear }}\n                    </div>\n                  </div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  {{ formatHours(month.total_hours) }}\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  {{ formatHours(month.billable_hours) }}\n                  <span class=\"text-xs text-gray-500 dark:text-gray-400 ml-1\">\n                    ({{ Math.round((month.billable_hours / month.total_hours) * 100) || 0 }}%)\n                  </span>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"flex items-center\">\n                    <div class=\"flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2 max-w-[60px]\">\n                      <div\n                        class=\"h-2 rounded-full\"\n                        :class=\"getProductivityColor(month.productivity)\"\n                        :style=\"{ width: Math.min(month.productivity, 100) + '%' }\"\n                      ></div>\n                    </div>\n                    <span class=\"text-sm text-gray-900 dark:text-white\">{{ Math.round(month.productivity) }}%</span>\n                  </div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  <div class=\"flex flex-wrap gap-1\">\n                    <span\n                      v-for=\"project in month.projects.slice(0, 2)\"\n                      :key=\"project.id\"\n                      class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\"\n                    >\n                      {{ project.name }}\n                    </span>\n                    <span\n                      v-if=\"month.projects.length > 2\"\n                      class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300\"\n                    >\n                      +{{ month.projects.length - 2 }}\n                    </span>\n                  </div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  {{ formatHours(month.daily_average) }}\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <span\n                    class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                    :class=\"getMonthStatusClass(month.status)\"\n                  >\n                    {{ getMonthStatusText(month.status) }}\n                  </span>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                  <button\n                    @click=\"viewMonthDetails(month)\"\n                    class=\"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3\"\n                  >\n                    Dettagli\n                  </button>\n                  <button\n                    @click=\"exportMonth(month)\"\n                    class=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300\"\n                  >\n                    Esporta\n                  </button>\n                </td>\n              </tr>\n            </template>\n\n            <!-- Detailed View -->\n            <template v-else>\n              <tr v-for=\"entry in detailedEntries\" :key=\"entry.id\" class=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  {{ formatDate(entry.date) }}\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  {{ entry.project?.name || 'N/A' }}\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  {{ entry.task?.name || 'N/A' }}\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  {{ formatHours(entry.hours) }}\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <span\n                    class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                    :class=\"entry.billable ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\"\n                  >\n                    {{ entry.billable ? 'Fatturabile' : 'Interno' }}\n                  </span>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <span\n                    class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                    :class=\"getStatusClass(entry.status)\"\n                  >\n                    {{ getStatusText(entry.status) }}\n                  </span>\n                </td>\n                <td class=\"px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate\">\n                  {{ entry.description || '-' }}\n                </td>\n              </tr>\n            </template>\n          </tbody>\n        </table>\n\n        <!-- Empty state -->\n        <div v-if=\"(viewMode === 'monthly' ? monthlySummaries : detailedEntries).length === 0\" class=\"text-center py-12\">\n          <div class=\"mx-auto h-12 w-12 text-gray-400\">\n            <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n            </svg>\n          </div>\n          <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun dato disponibile</h3>\n          <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            Non ci sono registrazioni per l'anno {{ selectedYear }}{{ selectedProject ? ' e progetto selezionato' : '' }}.\n          </p>\n          <div class=\"mt-4\">\n            <router-link\n              to=\"/app/timesheet/entry\"\n              class=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900 dark:text-primary-300 dark:hover:bg-primary-800\"\n            >\n              Inizia a registrare ore\n            </router-link>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Paginazione -->\n    <div v-if=\"pagination.totalPages > 1\" class=\"bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6 rounded-lg shadow\">\n      <div class=\"flex-1 flex justify-between sm:hidden\">\n        <button \n          @click=\"changePage(pagination.currentPage - 1)\"\n          :disabled=\"pagination.currentPage === 1\"\n          class=\"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\"\n        >\n          Precedente\n        </button>\n        <button \n          @click=\"changePage(pagination.currentPage + 1)\"\n          :disabled=\"pagination.currentPage === pagination.totalPages\"\n          class=\"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\"\n        >\n          Successivo\n        </button>\n      </div>\n      <div class=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\n        <div>\n          <p class=\"text-sm text-gray-700 dark:text-gray-300\">\n            Mostrando\n            <span class=\"font-medium\">{{ (pagination.currentPage - 1) * pagination.perPage + 1 }}</span>\n            a\n            <span class=\"font-medium\">{{ Math.min(pagination.currentPage * pagination.perPage, pagination.total) }}</span>\n            di\n            <span class=\"font-medium\">{{ pagination.total }}</span>\n            risultati\n          </p>\n        </div>\n        <div>\n          <nav class=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\">\n            <button \n              v-for=\"page in visiblePages\" \n              :key=\"page\"\n              @click=\"changePage(page)\"\n              :class=\"[\n                'relative inline-flex items-center px-4 py-2 border text-sm font-medium',\n                page === pagination.currentPage \n                  ? 'z-10 bg-primary-50 border-primary-500 text-primary-600' \n                  : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'\n              ]\"\n            >\n              {{ page }}\n            </button>\n          </nav>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted, watch } from 'vue'\nimport { useAuthStore } from '@/stores/auth'\n\nconst authStore = useAuthStore()\n\n// State\nconst projects = ref([])\nconst monthlySummaries = ref([])\nconst detailedEntries = ref([])\nconst loading = ref(false)\n\n// Filters\nconst selectedYear = ref(new Date().getFullYear())\nconst selectedProject = ref('')\nconst viewMode = ref('monthly')\n\n// Available years (last 5 years)\nconst availableYears = ref([])\n\n// Pagination\nconst pagination = ref({\n  currentPage: 1,\n  perPage: 50,\n  total: 0,\n  totalPages: 0\n})\n\n// Summary\nconst summary = ref({\n  totalHours: 0,\n  billableHours: 0,\n  activeProjects: 0,\n  dailyAverage: 0\n})\n\n// Computed\nconst visiblePages = computed(() => {\n  const pages = []\n  const start = Math.max(1, pagination.value.currentPage - 2)\n  const end = Math.min(pagination.value.totalPages, pagination.value.currentPage + 2)\n  \n  for (let i = start; i <= end; i++) {\n    pages.push(i)\n  }\n  \n  return pages\n})\n\n// Methods\nconst initializeYears = () => {\n  const currentYear = new Date().getFullYear()\n  const years = []\n  for (let i = 0; i < 5; i++) {\n    years.push(currentYear - i)\n  }\n  availableYears.value = years\n}\n\nconst loadProjects = async () => {\n  try {\n    const response = await fetch('/api/projects/', {\n      headers: {\n        'Content-Type': 'application/json',\n        'X-CSRFToken': authStore.csrfToken\n      }\n    })\n\n    if (response.ok) {\n      const result = await response.json()\n      // Handle the correct API response structure\n      if (result.data && Array.isArray(result.data.projects)) {\n        projects.value = result.data.projects\n      } else if (result.data && Array.isArray(result.data.items)) {\n        projects.value = result.data.items\n      } else if (result.data && Array.isArray(result.data)) {\n        projects.value = result.data\n      } else if (Array.isArray(result)) {\n        projects.value = result\n      } else {\n        projects.value = []\n      }\n      console.log('Loaded projects:', projects.value)\n    } else {\n      console.error('Failed to load projects:', response.status)\n      projects.value = []\n    }\n  } catch (err) {\n    console.error('Error loading projects:', err)\n    projects.value = []\n  }\n}\n\nconst loadMonthlySummaries = async () => {\n  loading.value = true\n\n  try {\n    if (viewMode.value === 'monthly') {\n      await loadMonthlyData()\n    } else {\n      await loadDetailedData()\n    }\n\n    calculateSummary()\n  } catch (err) {\n    console.error('Error loading data:', err)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst loadMonthlyData = async () => {\n  // Generate monthly summaries from timesheet data\n  const params = new URLSearchParams({\n    start_date: `${selectedYear.value}-01-01`,\n    end_date: `${selectedYear.value}-12-31`\n  })\n\n  if (selectedProject.value) {\n    params.append('project_id', selectedProject.value)\n  }\n\n  const response = await fetch(`/api/timesheets/?${params}`, {\n    headers: {\n      'Content-Type': 'application/json',\n      'X-CSRFToken': authStore.csrfToken\n    }\n  })\n\n  if (response.ok) {\n    const result = await response.json()\n    // Ensure timesheets is always an array\n    let timesheets = []\n    if (result.data && Array.isArray(result.data)) {\n      timesheets = result.data\n    } else if (result.data && Array.isArray(result.data.items)) {\n      timesheets = result.data.items\n    } else if (Array.isArray(result)) {\n      timesheets = result\n    }\n\n    console.log('Loaded timesheets for monthly data:', timesheets.length, 'entries')\n\n    // Group by month and calculate summaries\n    const monthlyData = {}\n\n    timesheets.forEach(entry => {\n      const date = new Date(entry.date)\n      const month = date.getMonth() + 1\n\n      if (!monthlyData[month]) {\n        monthlyData[month] = {\n          month,\n          total_hours: 0,\n          billable_hours: 0,\n          entries: [],\n          projects: new Set(),\n          working_days: new Set()\n        }\n      }\n\n      monthlyData[month].total_hours += entry.hours || 0\n      if (entry.billable) {\n        monthlyData[month].billable_hours += entry.hours || 0\n      }\n      monthlyData[month].entries.push(entry)\n      if (entry.project) {\n        monthlyData[month].projects.add(JSON.stringify({\n          id: entry.project.id,\n          name: entry.project.name\n        }))\n      }\n      monthlyData[month].working_days.add(date.getDate())\n    })\n\n    // Convert to array and calculate additional metrics\n    monthlySummaries.value = Object.values(monthlyData).map(month => ({\n      ...month,\n      projects: Array.from(month.projects).map(p => JSON.parse(p)),\n      daily_average: month.total_hours / Math.max(month.working_days.size, 1),\n      productivity: (month.billable_hours / month.total_hours) * 100 || 0,\n      status: month.total_hours > 0 ? 'active' : 'inactive'\n    })).sort((a, b) => a.month - b.month)\n  }\n}\n\nconst loadDetailedData = async () => {\n  const params = new URLSearchParams({\n    start_date: `${selectedYear.value}-01-01`,\n    end_date: `${selectedYear.value}-12-31`,\n    page: pagination.value.currentPage,\n    per_page: pagination.value.perPage\n  })\n\n  if (selectedProject.value) {\n    params.append('project_id', selectedProject.value)\n  }\n\n  const response = await fetch(`/api/timesheets/?${params}`, {\n    headers: {\n      'Content-Type': 'application/json',\n      'X-CSRFToken': authStore.csrfToken\n    }\n  })\n\n  if (response.ok) {\n    const result = await response.json()\n    // Ensure detailedEntries is always an array\n    if (result.data && Array.isArray(result.data)) {\n      detailedEntries.value = result.data\n    } else if (result.data && Array.isArray(result.data.items)) {\n      detailedEntries.value = result.data.items\n    } else if (Array.isArray(result)) {\n      detailedEntries.value = result\n    } else {\n      detailedEntries.value = []\n    }\n\n    console.log('Loaded detailed entries:', detailedEntries.value.length, 'entries')\n\n    // Update pagination if provided\n    if (result.pagination) {\n      pagination.value = {\n        ...pagination.value,\n        ...result.pagination\n      }\n    }\n  } else {\n    console.error('Failed to load detailed data:', response.status)\n    detailedEntries.value = []\n  }\n}\n\nconst calculateSummary = () => {\n  let total = 0\n  let billable = 0\n  let projectIds = new Set()\n\n  if (viewMode.value === 'monthly') {\n    // Ensure monthlySummaries.value is an array\n    const summariesArray = Array.isArray(monthlySummaries.value) ? monthlySummaries.value : []\n    total = summariesArray.reduce((sum, month) => sum + (month.total_hours || 0), 0)\n    billable = summariesArray.reduce((sum, month) => sum + (month.billable_hours || 0), 0)\n    summariesArray.forEach(month => {\n      if (month.projects && Array.isArray(month.projects)) {\n        month.projects.forEach(project => projectIds.add(project.id))\n      }\n    })\n  } else {\n    // Ensure detailedEntries.value is an array\n    const entriesArray = Array.isArray(detailedEntries.value) ? detailedEntries.value : []\n    total = entriesArray.reduce((sum, entry) => sum + (entry.hours || 0), 0)\n    billable = entriesArray.reduce((sum, entry) => sum + (entry.billable ? (entry.hours || 0) : 0), 0)\n    entriesArray.forEach(entry => {\n      if (entry.project_id) {\n        projectIds.add(entry.project_id)\n      }\n    })\n  }\n\n  summary.value = {\n    totalHours: total,\n    billableHours: billable,\n    activeProjects: projectIds.size,\n    dailyAverage: total / 365 // Yearly average\n  }\n}\n\nconst changePage = (page) => {\n  if (page >= 1 && page <= pagination.value.totalPages) {\n    pagination.value.currentPage = page\n    loadMonthlySummaries()\n  }\n}\n\n// Helper methods\nconst getMonthName = (month) => {\n  const monthNames = [\n    'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',\n    'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'\n  ]\n  return monthNames[month - 1]\n}\n\nconst getProductivityColor = (productivity) => {\n  if (productivity >= 80) return 'bg-green-500'\n  if (productivity >= 60) return 'bg-yellow-500'\n  return 'bg-red-500'\n}\n\nconst getMonthStatusClass = (status) => {\n  switch (status) {\n    case 'active':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n    case 'inactive':\n      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\n    default:\n      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n  }\n}\n\nconst getMonthStatusText = (status) => {\n  switch (status) {\n    case 'active':\n      return 'Attivo'\n    case 'inactive':\n      return 'Inattivo'\n    default:\n      return 'Parziale'\n  }\n}\n\nconst getStatusClass = (status) => {\n  switch (status) {\n    case 'approved':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n    case 'rejected':\n      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'\n    default:\n      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n  }\n}\n\nconst getStatusText = (status) => {\n  switch (status) {\n    case 'approved':\n      return 'Approvato'\n    case 'rejected':\n      return 'Rifiutato'\n    default:\n      return 'In Attesa'\n  }\n}\n\nconst viewMonthDetails = (month) => {\n  // Switch to detailed view for specific month\n  viewMode.value = 'detailed'\n  // You could add month filtering here\n  loadMonthlySummaries()\n}\n\nconst exportMonth = (month) => {\n  // Export specific month data\n  alert(`Esportazione dati per ${getMonthName(month.month)} ${selectedYear.value} - Funzionalità in sviluppo`)\n}\n\nconst exportData = () => {\n  // Export all data\n  alert('Esportazione report completo - Funzionalità in sviluppo')\n}\n\nconst formatDate = (dateString) => {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('it-IT')\n}\n\nconst formatHours = (hours) => {\n  if (!hours || hours === 0) return '0h'\n  return hours % 1 === 0 ? `${hours}h` : `${hours.toFixed(1)}h`\n}\n\n// Lifecycle\nonMounted(() => {\n  initializeYears()\n  loadProjects()\n  loadMonthlySummaries()\n})\n\n// Watchers\nwatch([selectedYear, selectedProject, viewMode], () => {\n  pagination.value.currentPage = 1\n  loadMonthlySummaries()\n})\n</script>\n", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- <PERSON>rro<PERSON> -->\n    <div v-if=\"error\" class=\"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4\">\n      <div class=\"flex\">\n        <div class=\"flex-shrink-0\">\n          <svg class=\"h-5 w-5 text-red-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z\"></path>\n          </svg>\n        </div>\n        <div class=\"ml-3\">\n          <p class=\"text-sm text-red-800 dark:text-red-200\">{{ error }}</p>\n        </div>\n        <div class=\"ml-auto pl-3\">\n          <div class=\"-mx-1.5 -my-1.5\">\n            <button @click=\"clearError\" class=\"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900\">\n              <svg class=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Storico Timesheet</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Visualizza i riepiloghi mensili delle tue ore lavorate\n          </p>\n        </div>\n\n        <div class=\"flex space-x-3\">\n          <button\n            @click=\"exportData\"\n            :disabled=\"loading\"\n            class=\"bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n            </svg>\n            {{ loading ? 'Esportando...' : 'Esporta Report' }}\n          </button>\n          <router-link\n            to=\"/app/timesheet/entry\"\n            class=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n            </svg>\n            Registra Ore\n          </router-link>\n        </div>\n      </div>\n    </div>\n\n    <!-- Filtri -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-4\">\n      <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Anno\n          </label>\n          <select\n            v-model=\"selectedYear\"\n            @change=\"loadMonthlySummaries\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n            <option v-for=\"year in availableYears\" :key=\"year\" :value=\"year\">\n              {{ year }}\n            </option>\n          </select>\n        </div>\n\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Progetto\n          </label>\n          <select\n            v-model=\"selectedProject\"\n            @change=\"loadMonthlySummaries\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n            <option value=\"\">Tutti i progetti</option>\n            <option\n              v-for=\"project in projects\"\n              :key=\"project.id\"\n              :value=\"project.id\"\n            >\n              {{ project.name }}\n            </option>\n          </select>\n        </div>\n\n        <div>\n          <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n            Visualizzazione\n          </label>\n          <select\n            v-model=\"viewMode\"\n            @change=\"loadMonthlySummaries\"\n            class=\"w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500\"\n          >\n            <option value=\"monthly\">Riepilogo Mensile</option>\n            <option value=\"detailed\">Dettaglio Giornaliero</option>\n          </select>\n        </div>\n\n        <div class=\"flex items-end\">\n          <button\n            @click=\"loadMonthlySummaries\"\n            :disabled=\"loading\"\n            class=\"w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            {{ loading ? 'Caricando...' : 'Aggiorna' }}\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Statistiche Periodo -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Totali\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(summary.totalHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Fatturabili\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(summary.billableHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Progetti Attivi\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ summary.activeProjects }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Media Giornaliera\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(summary.dailyAverage) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Riepiloghi Mensili -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            {{ viewMode === 'monthly' ? 'Riepiloghi Mensili' : 'Dettaglio Giornaliero' }} - {{ selectedYear }}\n          </h3>\n          <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n            {{ monthlySummaries.length }} {{ viewMode === 'monthly' ? 'mesi' : 'giorni' }} trovati\n          </div>\n        </div>\n      </div>\n\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr v-if=\"viewMode === 'monthly'\">\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Mese\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Ore Totali\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Ore Fatturabili\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Produttività\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Progetti\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Media Giornaliera\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Stato\n              </th>\n              <th class=\"px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Azioni\n              </th>\n            </tr>\n            <tr v-else>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Data\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Progetto\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Task\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Ore\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Fatturabile\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Stato\n              </th>\n              <th class=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider\">\n                Descrizione\n              </th>\n            </tr>\n          </thead>\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <!-- Monthly View -->\n            <template v-if=\"viewMode === 'monthly'\">\n              <tr v-for=\"month in monthlySummaries\" :key=\"month.month\" class=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"flex items-center\">\n                    <div class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {{ getMonthName(month.month) }} {{ selectedYear }}\n                    </div>\n                  </div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  {{ formatHours(month.total_hours) }}\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  {{ formatHours(month.billable_hours) }}\n                  <span class=\"text-xs text-gray-500 dark:text-gray-400 ml-1\">\n                    ({{ Math.round((month.billable_hours / month.total_hours) * 100) || 0 }}%)\n                  </span>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <div class=\"flex items-center\">\n                    <div class=\"flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2 max-w-[60px]\">\n                      <div\n                        class=\"h-2 rounded-full\"\n                        :class=\"getProductivityColor(month.productivity)\"\n                        :style=\"{ width: Math.min(month.productivity, 100) + '%' }\"\n                      ></div>\n                    </div>\n                    <span class=\"text-sm text-gray-900 dark:text-white\">{{ Math.round(month.productivity) }}%</span>\n                  </div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  <div class=\"flex flex-wrap gap-1\">\n                    <span\n                      v-for=\"project in month.projects.slice(0, 2)\"\n                      :key=\"project.id\"\n                      class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\"\n                    >\n                      {{ project.name }}\n                    </span>\n                    <span\n                      v-if=\"month.projects.length > 2\"\n                      class=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300\"\n                    >\n                      +{{ month.projects.length - 2 }}\n                    </span>\n                  </div>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  {{ formatHours(month.daily_average) }}\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <span\n                    class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                    :class=\"getMonthStatusClass(month.status)\"\n                  >\n                    {{ getMonthStatusText(month.status) }}\n                  </span>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                  <button\n                    @click=\"viewMonthDetails(month)\"\n                    class=\"text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3\"\n                  >\n                    Dettagli\n                  </button>\n                  <button\n                    @click=\"exportMonth(month)\"\n                    class=\"text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300\"\n                  >\n                    Esporta\n                  </button>\n                </td>\n              </tr>\n            </template>\n\n            <!-- Detailed View -->\n            <template v-else>\n              <tr v-for=\"entry in detailedEntries\" :key=\"entry.id\" class=\"hover:bg-gray-50 dark:hover:bg-gray-700\">\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  {{ formatDate(entry.date) }}\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  {{ entry.project?.name || 'N/A' }}\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  {{ entry.task?.name || 'N/A' }}\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white\">\n                  {{ formatHours(entry.hours) }}\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <span\n                    class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                    :class=\"entry.billable ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\"\n                  >\n                    {{ entry.billable ? 'Fatturabile' : 'Interno' }}\n                  </span>\n                </td>\n                <td class=\"px-6 py-4 whitespace-nowrap\">\n                  <span\n                    class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium\"\n                    :class=\"getStatusClass(entry.status)\"\n                  >\n                    {{ getStatusText(entry.status) }}\n                  </span>\n                </td>\n                <td class=\"px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate\">\n                  {{ entry.description || '-' }}\n                </td>\n              </tr>\n            </template>\n          </tbody>\n        </table>\n\n        <!-- Empty state -->\n        <div v-if=\"(viewMode === 'monthly' ? monthlySummaries : detailedEntries).length === 0\" class=\"text-center py-12\">\n          <div class=\"mx-auto h-12 w-12 text-gray-400\">\n            <svg fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"></path>\n            </svg>\n          </div>\n          <h3 class=\"mt-2 text-sm font-medium text-gray-900 dark:text-white\">Nessun dato disponibile</h3>\n          <p class=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\n            Non ci sono registrazioni per l'anno {{ selectedYear }}{{ selectedProject ? ' e progetto selezionato' : '' }}.\n          </p>\n          <div class=\"mt-4\">\n            <router-link\n              to=\"/app/timesheet/entry\"\n              class=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900 dark:text-primary-300 dark:hover:bg-primary-800\"\n            >\n              Inizia a registrare ore\n            </router-link>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Paginazione -->\n    <div v-if=\"pagination.totalPages > 1\" class=\"bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6 rounded-lg shadow\">\n      <div class=\"flex-1 flex justify-between sm:hidden\">\n        <button \n          @click=\"changePage(pagination.currentPage - 1)\"\n          :disabled=\"pagination.currentPage === 1\"\n          class=\"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\"\n        >\n          Precedente\n        </button>\n        <button \n          @click=\"changePage(pagination.currentPage + 1)\"\n          :disabled=\"pagination.currentPage === pagination.totalPages\"\n          class=\"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50\"\n        >\n          Successivo\n        </button>\n      </div>\n      <div class=\"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between\">\n        <div>\n          <p class=\"text-sm text-gray-700 dark:text-gray-300\">\n            Mostrando\n            <span class=\"font-medium\">{{ (pagination.currentPage - 1) * pagination.perPage + 1 }}</span>\n            a\n            <span class=\"font-medium\">{{ Math.min(pagination.currentPage * pagination.perPage, pagination.total) }}</span>\n            di\n            <span class=\"font-medium\">{{ pagination.total }}</span>\n            risultati\n          </p>\n        </div>\n        <div>\n          <nav class=\"relative z-0 inline-flex rounded-md shadow-sm -space-x-px\">\n            <button \n              v-for=\"page in visiblePages\" \n              :key=\"page\"\n              @click=\"changePage(page)\"\n              :class=\"[\n                'relative inline-flex items-center px-4 py-2 border text-sm font-medium',\n                page === pagination.currentPage \n                  ? 'z-10 bg-primary-50 border-primary-500 text-primary-600' \n                  : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'\n              ]\"\n            >\n              {{ page }}\n            </button>\n          </nav>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useTimesheetStore } from '@/stores/timesheet'\nimport { formatDate, formatHours, getStatusClass, getStatusText } from '@/utils/timesheet'\n\nconst timesheetStore = useTimesheetStore()\n\n// Local state\nconst loading = ref(false)\nconst monthlySummaries = ref([])\nconst detailedEntries = ref([])\n\n// Filters\nconst selectedYear = ref(new Date().getFullYear())\nconst selectedProject = ref('')\nconst viewMode = ref('monthly')\n\n// Available years (last 5 years)\nconst availableYears = ref([])\n\n// Pagination\nconst pagination = ref({\n  currentPage: 1,\n  perPage: 50,\n  total: 0,\n  totalPages: 0\n})\n\n// Summary\nconst summary = ref({\n  totalHours: 0,\n  billableHours: 0,\n  activeProjects: 0,\n  dailyAverage: 0\n})\n\n// Computed properties from store\nconst error = computed(() => timesheetStore.error)\nconst projects = computed(() => timesheetStore.availableProjects)\n\nconst visiblePages = computed(() => {\n  const pages = []\n  const start = Math.max(1, pagination.value.currentPage - 2)\n  const end = Math.min(pagination.value.totalPages, pagination.value.currentPage + 2)\n  \n  for (let i = start; i <= end; i++) {\n    pages.push(i)\n  }\n  \n  return pages\n})\n\n// Methods\nconst initializeYears = () => {\n  const currentYear = new Date().getFullYear()\n  const years = []\n  for (let i = 0; i < 5; i++) {\n    years.push(currentYear - i)\n  }\n  availableYears.value = years\n}\n\nconst loadMonthlySummaries = async () => {\n  loading.value = true\n\n  try {\n    if (viewMode.value === 'monthly') {\n      await loadMonthlyData()\n    } else {\n      await loadDetailedData()\n    }\n\n    calculateSummary()\n  } catch (err) {\n    console.error('Error loading data:', err)\n  } finally {\n    loading.value = false\n  }\n}\n\nconst loadMonthlyData = async () => {\n  const startDate = `${selectedYear.value}-01-01`\n  const endDate = `${selectedYear.value}-12-31`\n  \n  const filters = {\n    start_date: startDate,\n    end_date: endDate,\n    project_id: selectedProject.value\n  }\n\n  const timesheets = await timesheetStore.loadTimesheetHistory(filters)\n\n  // Group by month and calculate summaries\n  const monthlyData = {}\n\n  timesheets.forEach(entry => {\n    const date = new Date(entry.date)\n    const month = date.getMonth() + 1\n\n    if (!monthlyData[month]) {\n      monthlyData[month] = {\n        month,\n        total_hours: 0,\n        billable_hours: 0,\n        entries: [],\n        projects: new Set(),\n        working_days: new Set()\n      }\n    }\n\n    monthlyData[month].total_hours += entry.hours || 0\n    if (entry.billable) {\n      monthlyData[month].billable_hours += entry.hours || 0\n    }\n    monthlyData[month].entries.push(entry)\n    if (entry.project) {\n      monthlyData[month].projects.add(JSON.stringify({\n        id: entry.project.id,\n        name: entry.project.name\n      }))\n    }\n    monthlyData[month].working_days.add(date.getDate())\n  })\n\n  // Convert to array and calculate additional metrics\n  monthlySummaries.value = Object.values(monthlyData).map(month => ({\n    ...month,\n    projects: Array.from(month.projects).map(p => JSON.parse(p)),\n    daily_average: month.total_hours / Math.max(month.working_days.size, 1),\n    productivity: (month.billable_hours / month.total_hours) * 100 || 0,\n    status: month.total_hours > 0 ? 'active' : 'inactive'\n  })).sort((a, b) => a.month - b.month)\n}\n\nconst loadDetailedData = async () => {\n  const filters = {\n    start_date: `${selectedYear.value}-01-01`,\n    end_date: `${selectedYear.value}-12-31`,\n    project_id: selectedProject.value,\n    page: pagination.value.currentPage,\n    per_page: pagination.value.perPage\n  }\n\n  detailedEntries.value = await timesheetStore.loadTimesheetHistory(filters)\n}\n\nconst calculateSummary = () => {\n  let total = 0\n  let billable = 0\n  let projectIds = new Set()\n\n  if (viewMode.value === 'monthly') {\n    // Ensure monthlySummaries.value is an array\n    const summariesArray = Array.isArray(monthlySummaries.value) ? monthlySummaries.value : []\n    total = summariesArray.reduce((sum, month) => sum + (month.total_hours || 0), 0)\n    billable = summariesArray.reduce((sum, month) => sum + (month.billable_hours || 0), 0)\n    summariesArray.forEach(month => {\n      if (month.projects && Array.isArray(month.projects)) {\n        month.projects.forEach(project => projectIds.add(project.id))\n      }\n    })\n  } else {\n    // Ensure detailedEntries.value is an array\n    const entriesArray = Array.isArray(detailedEntries.value) ? detailedEntries.value : []\n    total = entriesArray.reduce((sum, entry) => sum + (entry.hours || 0), 0)\n    billable = entriesArray.reduce((sum, entry) => sum + (entry.billable ? (entry.hours || 0) : 0), 0)\n    entriesArray.forEach(entry => {\n      if (entry.project_id) {\n        projectIds.add(entry.project_id)\n      }\n    })\n  }\n\n  summary.value = {\n    totalHours: total,\n    billableHours: billable,\n    activeProjects: projectIds.size,\n    dailyAverage: total / 365 // Yearly average\n  }\n}\n\nconst changePage = (page) => {\n  if (page >= 1 && page <= pagination.value.totalPages) {\n    pagination.value.currentPage = page\n    loadMonthlySummaries()\n  }\n}\n\n// Helper methods\nconst getMonthName = (month) => {\n  const monthNames = [\n    'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',\n    'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'\n  ]\n  return monthNames[month - 1]\n}\n\nconst getProductivityColor = (productivity) => {\n  if (productivity >= 80) return 'bg-green-500'\n  if (productivity >= 60) return 'bg-yellow-500'\n  return 'bg-red-500'\n}\n\nconst getMonthStatusClass = (status) => {\n  switch (status) {\n    case 'active':\n      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'\n    case 'inactive':\n      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'\n    default:\n      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'\n  }\n}\n\nconst getMonthStatusText = (status) => {\n  switch (status) {\n    case 'active':\n      return 'Attivo'\n    case 'inactive':\n      return 'Inattivo'\n    default:\n      return 'Parziale'\n  }\n}\n\n\n\nconst viewMonthDetails = (month) => {\n  // Switch to detailed view for specific month\n  viewMode.value = 'detailed'\n  // You could add month filtering here\n  loadMonthlySummaries()\n}\n\nconst exportMonth = async (month) => {\n  const filters = {\n    start_date: `${selectedYear.value}-${month.month.toString().padStart(2, '0')}-01`,\n    end_date: `${selectedYear.value}-${month.month.toString().padStart(2, '0')}-31`\n  }\n  await timesheetStore.exportTimesheetData(filters)\n}\n\nconst exportData = async () => {\n  const filters = {\n    start_date: `${selectedYear.value}-01-01`,\n    end_date: `${selectedYear.value}-12-31`,\n    project_id: selectedProject.value\n  }\n  await timesheetStore.exportTimesheetData(filters)\n}\n\nconst clearError = () => {\n  timesheetStore.clearError()\n}\n\n// Lifecycle\nonMounted(async () => {\n  initializeYears()\n  await timesheetStore.loadAvailableProjects()\n  await loadMonthlySummaries()\n})\n</script>\n"}