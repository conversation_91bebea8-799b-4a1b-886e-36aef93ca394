{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "frontend/src/views/timesheet/TimesheetEntry.vue"}, "originalCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- <PERSON>rro<PERSON> -->\n    <div v-if=\"error\" class=\"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4\">\n      <div class=\"flex\">\n        <div class=\"flex-shrink-0\">\n          <svg class=\"h-5 w-5 text-red-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z\"></path>\n          </svg>\n        </div>\n        <div class=\"ml-3\">\n          <p class=\"text-sm text-red-800 dark:text-red-200\">{{ error }}</p>\n        </div>\n        <div class=\"ml-auto pl-3\">\n          <div class=\"-mx-1.5 -my-1.5\">\n            <button @click=\"clearError\" class=\"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900\">\n              <svg class=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Le Mie Ore</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Registra le tue ore di lavoro con la griglia mensile\n          </p>\n        </div>\n\n        <!-- Controlli periodo -->\n        <div class=\"flex items-center space-x-4\">\n          <div class=\"flex items-center space-x-2\">\n            <button\n              @click=\"previousMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\"></path>\n              </svg>\n            </button>\n\n            <div class=\"text-center\">\n              <h2 class=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n              </h2>\n            </div>\n\n            <button\n              @click=\"nextMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n              </svg>\n            </button>\n          </div>\n\n          <button\n            @click=\"showAddProjectModal = true\"\n            class=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium\"\n          >\n            Aggiungi Progetto\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Griglia Timesheet Mensile -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Griglia Ore - {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n          </h3>\n          <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n            Totale: {{ formatHours(totalHours) }} | Fatturabili: {{ formatHours(billableHours) }}\n          </div>\n        </div>\n      </div>\n\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <!-- Header con giorni del mese -->\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600\">\n                Progetto/Task\n              </th>\n              <th\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day, currentMonth, currentYear),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day, currentMonth, currentYear)\n                }\"\n              >\n                <div>{{ day }}</div>\n                <div class=\"text-xs text-gray-400\">{{ getDayName(day, currentMonth, currentYear) }}</div>\n              </th>\n              <th class=\"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600\">\n                Totale\n              </th>\n            </tr>\n          </thead>\n\n          <!-- Righe progetti/task -->\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-if=\"timesheetGridData.length === 0\">\n              <td :colspan=\"daysInMonth.length + 2\" class=\"px-6 py-8 text-center text-gray-500 dark:text-gray-400\">\n                <div class=\"space-y-2\">\n                  <p>Nessun timesheet registrato per {{ getMonthName(currentMonth) }} {{ currentYear }}</p>\n                  <button\n                    @click=\"showAddProjectModal = true\"\n                    class=\"text-primary-600 hover:text-primary-700 dark:text-primary-400\"\n                  >\n                    Registra le tue prime ore\n                  </button>\n                </div>\n              </td>\n            </tr>\n            \n            <tr v-for=\"taskRow in timesheetGridData\" :key=\"taskRow.taskId\">\n              <!-- Colonna progetto/task -->\n              <td class=\"sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600\">\n                <div class=\"flex items-center justify-between\">\n                  <div class=\"min-w-0 flex-1\">\n                    <p class=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                      {{ taskRow.projectName }}\n                    </p>\n                    <p v-if=\"taskRow.taskName\" class=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\n                      {{ taskRow.taskName }}\n                    </p>\n                    <p class=\"text-xs text-gray-500 dark:text-gray-400\">\n                      {{ taskRow.assignees }}\n                    </p>\n                  </div>\n                </div>\n              </td>\n              \n              <!-- Colonne giorni -->\n              <td\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-1 py-3 text-center\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day, currentMonth, currentYear),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day, currentMonth, currentYear)\n                }\"\n              >\n                <div v-if=\"taskRow.hours[getDateString(day)]\" class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ taskRow.hours[getDateString(day)] }}\n                </div>\n                <div v-else class=\"text-gray-300 dark:text-gray-600\">-</div>\n              </td>\n              \n              <!-- Colonna totale riga -->\n              <td class=\"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 border-l border-gray-200 dark:border-gray-600\">\n                {{ taskRow.total }}\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Quick Stats -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Totali\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(totalHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Fatturabili\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(billableHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                In Attesa\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(pendingHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Progetti Attivi\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ activeProjects }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal Aggiungi Progetto -->\n    <div v-if=\"showAddProjectModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div class=\"relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800\">\n        <div class=\"mt-3\">\n          <div class=\"flex items-center justify-between mb-4\">\n            <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Aggiungi Progetto alla Griglia\n            </h3>\n            <button @click=\"showAddProjectModal = false\" class=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\">\n              <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          <div class=\"space-y-4\">\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Progetto *\n              </label>\n              <select \n                v-model=\"newProjectTask.project_id\" \n                class=\"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                required\n              >\n                <option value=\"\">Seleziona progetto...</option>\n                <option v-for=\"project in availableProjects\" :key=\"project.id\" :value=\"project.id\">\n                  {{ project.name }}\n                </option>\n              </select>\n            </div>\n\n            <div>\n              <label class=\"flex items-center\">\n                <input \n                  type=\"checkbox\" \n                  v-model=\"newProjectTask.billable\"\n                  class=\"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                />\n                <span class=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">Ore fatturabili</span>\n              </label>\n            </div>\n\n            <div class=\"flex justify-end space-x-3\">\n              <button\n                @click=\"addProject\"\n                :disabled=\"!newProjectTask.project_id || loading\"\n                class=\"bg-primary-600 hover:bg-primary-700 disabled:opacity-50 text-white px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                {{ loading ? 'Aggiunta...' : 'Aggiungi' }}\n              </button>\n              <button\n                @click=\"showAddProjectModal = false\"\n                class=\"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                Annulla\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useTimesheetStore } from '@/stores/timesheet'\nimport { \n  monthNames, \n  formatHours, \n  isToday, \n  isWeekend, \n  getDayName,\n  validateHours\n} from '@/utils/timesheet'\n\nconst timesheetStore = useTimesheetStore()\n\n// Local state\nconst showAddProjectModal = ref(false)\nconst newProjectTask = ref({\n  project_id: '',\n  task_id: '',\n  billable: true\n})\n\n// Computed properties from store\nconst currentYear = computed(() => timesheetStore.currentYear)\nconst currentMonth = computed(() => timesheetStore.currentMonth)\nconst projectTasks = computed(() => timesheetStore.projectTasks)\nconst monthlyEntries = computed(() => timesheetStore.monthlyEntries)\nconst availableProjects = computed(() => timesheetStore.availableProjects)\nconst loading = computed(() => timesheetStore.loading.monthlyData || timesheetStore.loading.saving)\nconst error = computed(() => timesheetStore.error)\n\n// Computed calculations\nconst daysInMonth = computed(() => timesheetStore.daysInMonth)\nconst totalHours = computed(() => timesheetStore.totalHours)\nconst billableHours = computed(() => timesheetStore.billableHours)\nconst pendingHours = computed(() => timesheetStore.pendingHours)\nconst activeProjects = computed(() => timesheetStore.activeProjects)\n\n// Build timesheet grid from existing entries like PersonnelProfile.vue\nconst timesheetGridData = computed(() => {\n  const taskGroups = {}\n  \n  // Process timesheet entries from the store\n  Object.entries(monthlyEntries.value).forEach(([date, dayEntries]) => {\n    Object.entries(dayEntries).forEach(([taskKey, hours]) => {\n      if (!taskGroups[taskKey]) {\n        // Find project task info\n        const projectTask = projectTasks.value.find(pt => pt.id === taskKey)\n        taskGroups[taskKey] = {\n          taskId: taskKey,\n          taskName: projectTask?.task_name || 'Attività Generica',\n          projectName: projectTask?.project_name || 'Progetto Sconosciuto', \n          assignees: 'Tu',\n          hours: {},\n          total: 0\n        }\n      }\n      \n      taskGroups[taskKey].hours[date] = parseFloat(hours || 0).toFixed(1)\n      taskGroups[taskKey].total += parseFloat(hours || 0)\n    })\n  })\n  \n  // Format totals\n  return Object.values(taskGroups).map(task => ({\n    ...task,\n    total: task.total.toFixed(1)\n  }))\n})\n\n// Methods\nconst previousMonth = () => {\n  timesheetStore.navigateMonth('previous')\n}\n\nconst nextMonth = () => {\n  timesheetStore.navigateMonth('next')\n}\n\nconst clearError = () => {\n  timesheetStore.clearError()\n}\n\nconst updateEntry = async (projectTaskId, day, value) => {\n  const hours = validateHours(value)\n  if (hours === null) return\n  \n  await timesheetStore.saveEntry(projectTaskId, day, hours)\n}\n\nconst addProject = async () => {\n  if (!newProjectTask.value.project_id) return\n  \n  const success = await timesheetStore.addProjectToTimesheet(\n    newProjectTask.value.project_id, \n    newProjectTask.value.task_id\n  )\n  \n  if (success) {\n    showAddProjectModal.value = false\n    newProjectTask.value = {\n      project_id: '',\n      task_id: '',\n      billable: true\n    }\n  }\n}\n\nconst removeProjectTask = (projectTask) => {\n  if (confirm('Rimuovere questo progetto dalla griglia? Le ore registrate non verranno eliminate.')) {\n    // Remove from local projectTasks - this should be handled by the store\n    const index = projectTasks.value.findIndex(pt => \n      pt.project_id === projectTask.project_id && pt.task_id === projectTask.task_id\n    )\n    if (index !== -1) {\n      projectTasks.value.splice(index, 1)\n    }\n  }\n}\n\nconst getEntryValue = (projectTaskId, day) => {\n  const entryKey = `${currentYear.value}-${currentMonth.value}-${day}`\n  return monthlyEntries.value[entryKey]?.[projectTaskId] || 0\n}\n\nconst getRowTotal = (projectTask) => {\n  return daysInMonth.value.reduce((sum, day) => {\n    return sum + (getEntryValue(projectTask.id, day) || 0)\n  }, 0)\n}\n\nconst getDateString = (day) => {\n  return `${currentYear.value}-${currentMonth.value.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`\n}\n\nconst getMonthName = (month) => {\n  const names = [\n    'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',\n    'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'\n  ]\n  return names[month - 1]\n}\n\n// Lifecycle\nonMounted(async () => {\n  // Load data from store with caching\n  await Promise.all([\n    timesheetStore.loadAvailableProjects(),\n    timesheetStore.loadMonthlyData()\n  ])\n})\n</script>", "modifiedCode": "<template>\n  <div class=\"space-y-6\">\n    <!-- <PERSON>rro<PERSON> -->\n    <div v-if=\"error\" class=\"bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4\">\n      <div class=\"flex\">\n        <div class=\"flex-shrink-0\">\n          <svg class=\"h-5 w-5 text-red-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z\"></path>\n          </svg>\n        </div>\n        <div class=\"ml-3\">\n          <p class=\"text-sm text-red-800 dark:text-red-200\">{{ error }}</p>\n        </div>\n        <div class=\"ml-auto pl-3\">\n          <div class=\"-mx-1.5 -my-1.5\">\n            <button @click=\"clearError\" class=\"inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900\">\n              <svg class=\"h-4 w-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Header -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n      <div class=\"flex justify-between items-center\">\n        <div>\n          <h1 class=\"text-2xl font-bold text-gray-900 dark:text-white\">Le Mie Ore</h1>\n          <p class=\"mt-1 text-sm text-gray-600 dark:text-gray-400\">\n            Registra le tue ore di lavoro con la griglia mensile\n          </p>\n        </div>\n\n        <!-- Controlli periodo -->\n        <div class=\"flex items-center space-x-4\">\n          <div class=\"flex items-center space-x-2\">\n            <button\n              @click=\"previousMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 19l-7-7 7-7\"></path>\n              </svg>\n            </button>\n\n            <div class=\"text-center\">\n              <h2 class=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n              </h2>\n            </div>\n\n            <button\n              @click=\"nextMonth\"\n              class=\"p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            >\n              <svg class=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5l7 7-7 7\"></path>\n              </svg>\n            </button>\n          </div>\n\n          <button\n            @click=\"showAddHoursModal = true\"\n            class=\"bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center\"\n          >\n            <svg class=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n            </svg>\n            Aggiungi Ore\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Griglia Timesheet Mensile -->\n    <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden\">\n      <div class=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n        <div class=\"flex items-center justify-between\">\n          <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n            Griglia Ore - {{ monthNames[currentMonth - 1] }} {{ currentYear }}\n          </h3>\n          <div class=\"text-sm text-gray-500 dark:text-gray-400\">\n            Totale: {{ formatHours(totalHours) }} | Fatturabili: {{ formatHours(billableHours) }}\n          </div>\n        </div>\n      </div>\n\n      <div class=\"overflow-x-auto\">\n        <table class=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\n          <!-- Header con giorni del mese -->\n          <thead class=\"bg-gray-50 dark:bg-gray-700\">\n            <tr>\n              <th class=\"sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600\">\n                Progetto/Task\n              </th>\n              <th\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day, currentMonth, currentYear),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day, currentMonth, currentYear)\n                }\"\n              >\n                <div>{{ day }}</div>\n                <div class=\"text-xs text-gray-400\">{{ getDayName(day, currentMonth, currentYear) }}</div>\n              </th>\n              <th class=\"px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600\">\n                Totale\n              </th>\n            </tr>\n          </thead>\n\n          <!-- Righe progetti/task -->\n          <tbody class=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\n            <tr v-if=\"timesheetGridData.length === 0\">\n              <td :colspan=\"daysInMonth.length + 2\" class=\"px-6 py-8 text-center text-gray-500 dark:text-gray-400\">\n                <div class=\"space-y-2\">\n                  <p>Nessun timesheet registrato per {{ getMonthName(currentMonth) }} {{ currentYear }}</p>\n                  <button\n                    @click=\"showAddProjectModal = true\"\n                    class=\"text-primary-600 hover:text-primary-700 dark:text-primary-400\"\n                  >\n                    Registra le tue prime ore\n                  </button>\n                </div>\n              </td>\n            </tr>\n            \n            <tr v-for=\"taskRow in timesheetGridData\" :key=\"taskRow.taskId\">\n              <!-- Colonna progetto/task -->\n              <td class=\"sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600\">\n                <div class=\"flex items-center justify-between\">\n                  <div class=\"min-w-0 flex-1\">\n                    <p class=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                      {{ taskRow.projectName }}\n                    </p>\n                    <p v-if=\"taskRow.taskName\" class=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\n                      {{ taskRow.taskName }}\n                    </p>\n                    <p class=\"text-xs text-gray-500 dark:text-gray-400\">\n                      {{ taskRow.assignees }}\n                    </p>\n                  </div>\n                </div>\n              </td>\n              \n              <!-- Colonne giorni -->\n              <td\n                v-for=\"day in daysInMonth\"\n                :key=\"day\"\n                class=\"px-1 py-3 text-center\"\n                :class=\"{\n                  'bg-blue-50 dark:bg-blue-900/20': isToday(day, currentMonth, currentYear),\n                  'bg-red-50 dark:bg-red-900/20': isWeekend(day, currentMonth, currentYear)\n                }\"\n              >\n                <div v-if=\"taskRow.hours[getDateString(day)]\" class=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {{ taskRow.hours[getDateString(day)] }}\n                </div>\n                <div v-else class=\"text-gray-300 dark:text-gray-600\">-</div>\n              </td>\n              \n              <!-- Colonna totale riga -->\n              <td class=\"px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 border-l border-gray-200 dark:border-gray-600\">\n                {{ taskRow.total }}\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n\n    <!-- Quick Stats -->\n    <div class=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Totali\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(totalHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Ore Fatturabili\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(billableHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                In Attesa\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ formatHours(pendingHours) }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n      \n      <div class=\"bg-white dark:bg-gray-800 shadow rounded-lg p-6\">\n        <div class=\"flex items-center\">\n          <div class=\"flex-shrink-0\">\n            <div class=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n              <svg class=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n              </svg>\n            </div>\n          </div>\n          <div class=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt class=\"text-sm font-medium text-gray-500 dark:text-gray-400 truncate\">\n                Progetti Attivi\n              </dt>\n              <dd class=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {{ activeProjects }}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Modal Aggiungi Progetto -->\n    <div v-if=\"showAddProjectModal\" class=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n      <div class=\"relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800\">\n        <div class=\"mt-3\">\n          <div class=\"flex items-center justify-between mb-4\">\n            <h3 class=\"text-lg font-medium text-gray-900 dark:text-white\">\n              Aggiungi Progetto alla Griglia\n            </h3>\n            <button @click=\"showAddProjectModal = false\" class=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\">\n              <svg class=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          <div class=\"space-y-4\">\n            <div>\n              <label class=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Progetto *\n              </label>\n              <select \n                v-model=\"newProjectTask.project_id\" \n                class=\"w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                required\n              >\n                <option value=\"\">Seleziona progetto...</option>\n                <option v-for=\"project in availableProjects\" :key=\"project.id\" :value=\"project.id\">\n                  {{ project.name }}\n                </option>\n              </select>\n            </div>\n\n            <div>\n              <label class=\"flex items-center\">\n                <input \n                  type=\"checkbox\" \n                  v-model=\"newProjectTask.billable\"\n                  class=\"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                />\n                <span class=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">Ore fatturabili</span>\n              </label>\n            </div>\n\n            <div class=\"flex justify-end space-x-3\">\n              <button\n                @click=\"addProject\"\n                :disabled=\"!newProjectTask.project_id || loading\"\n                class=\"bg-primary-600 hover:bg-primary-700 disabled:opacity-50 text-white px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                {{ loading ? 'Aggiunta...' : 'Aggiungi' }}\n              </button>\n              <button\n                @click=\"showAddProjectModal = false\"\n                class=\"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium\"\n              >\n                Annulla\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, onMounted } from 'vue'\nimport { useTimesheetStore } from '@/stores/timesheet'\nimport { \n  monthNames, \n  formatHours, \n  isToday, \n  isWeekend, \n  getDayName,\n  validateHours\n} from '@/utils/timesheet'\n\nconst timesheetStore = useTimesheetStore()\n\n// Local state\nconst showAddProjectModal = ref(false)\nconst newProjectTask = ref({\n  project_id: '',\n  task_id: '',\n  billable: true\n})\n\n// Computed properties from store\nconst currentYear = computed(() => timesheetStore.currentYear)\nconst currentMonth = computed(() => timesheetStore.currentMonth)\nconst projectTasks = computed(() => timesheetStore.projectTasks)\nconst monthlyEntries = computed(() => timesheetStore.monthlyEntries)\nconst availableProjects = computed(() => timesheetStore.availableProjects)\nconst loading = computed(() => timesheetStore.loading.monthlyData || timesheetStore.loading.saving)\nconst error = computed(() => timesheetStore.error)\n\n// Computed calculations\nconst daysInMonth = computed(() => timesheetStore.daysInMonth)\nconst totalHours = computed(() => timesheetStore.totalHours)\nconst billableHours = computed(() => timesheetStore.billableHours)\nconst pendingHours = computed(() => timesheetStore.pendingHours)\nconst activeProjects = computed(() => timesheetStore.activeProjects)\n\n// Build timesheet grid from existing entries like PersonnelProfile.vue\nconst timesheetGridData = computed(() => {\n  const taskGroups = {}\n  \n  // Process timesheet entries from the store\n  Object.entries(monthlyEntries.value).forEach(([date, dayEntries]) => {\n    Object.entries(dayEntries).forEach(([taskKey, hours]) => {\n      if (!taskGroups[taskKey]) {\n        // Find project task info\n        const projectTask = projectTasks.value.find(pt => pt.id === taskKey)\n        taskGroups[taskKey] = {\n          taskId: taskKey,\n          taskName: projectTask?.task_name || 'Attività Generica',\n          projectName: projectTask?.project_name || 'Progetto Sconosciuto', \n          assignees: 'Tu',\n          hours: {},\n          total: 0\n        }\n      }\n      \n      taskGroups[taskKey].hours[date] = parseFloat(hours || 0).toFixed(1)\n      taskGroups[taskKey].total += parseFloat(hours || 0)\n    })\n  })\n  \n  // Format totals\n  return Object.values(taskGroups).map(task => ({\n    ...task,\n    total: task.total.toFixed(1)\n  }))\n})\n\n// Methods\nconst previousMonth = () => {\n  timesheetStore.navigateMonth('previous')\n}\n\nconst nextMonth = () => {\n  timesheetStore.navigateMonth('next')\n}\n\nconst clearError = () => {\n  timesheetStore.clearError()\n}\n\nconst updateEntry = async (projectTaskId, day, value) => {\n  const hours = validateHours(value)\n  if (hours === null) return\n  \n  await timesheetStore.saveEntry(projectTaskId, day, hours)\n}\n\nconst addProject = async () => {\n  if (!newProjectTask.value.project_id) return\n  \n  const success = await timesheetStore.addProjectToTimesheet(\n    newProjectTask.value.project_id, \n    newProjectTask.value.task_id\n  )\n  \n  if (success) {\n    showAddProjectModal.value = false\n    newProjectTask.value = {\n      project_id: '',\n      task_id: '',\n      billable: true\n    }\n  }\n}\n\nconst removeProjectTask = (projectTask) => {\n  if (confirm('Rimuovere questo progetto dalla griglia? Le ore registrate non verranno eliminate.')) {\n    // Remove from local projectTasks - this should be handled by the store\n    const index = projectTasks.value.findIndex(pt => \n      pt.project_id === projectTask.project_id && pt.task_id === projectTask.task_id\n    )\n    if (index !== -1) {\n      projectTasks.value.splice(index, 1)\n    }\n  }\n}\n\nconst getEntryValue = (projectTaskId, day) => {\n  const entryKey = `${currentYear.value}-${currentMonth.value}-${day}`\n  return monthlyEntries.value[entryKey]?.[projectTaskId] || 0\n}\n\nconst getRowTotal = (projectTask) => {\n  return daysInMonth.value.reduce((sum, day) => {\n    return sum + (getEntryValue(projectTask.id, day) || 0)\n  }, 0)\n}\n\nconst getDateString = (day) => {\n  return `${currentYear.value}-${currentMonth.value.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`\n}\n\nconst getMonthName = (month) => {\n  const names = [\n    'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',\n    'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'\n  ]\n  return names[month - 1]\n}\n\n// Lifecycle\nonMounted(async () => {\n  // Load data from store with caching\n  await Promise.all([\n    timesheetStore.loadAvailableProjects(),\n    timesheetStore.loadMonthlyData()\n  ])\n})\n</script>"}