"""
API Blueprint per la gestione dei timesheet (Task 3.1 - Nuovo Schema).
Supporta il nuovo modello con Timesheet (contenitore giornaliero) e TimesheetEntry (singole entry).
"""
from flask import Blueprint, request, jsonify
from flask_login import current_user, login_required
from sqlalchemy import extract, func, and_, or_
from datetime import datetime, date, timedelta
from calendar import monthrange

from models import Timesheet, TimesheetEntry, TimeOffRequest, Task, Project, User
from utils.api_utils import api_response, handle_api_error
from utils.permissions import user_has_permission
from extensions import db
from extensions import csrf

api_timesheets = Blueprint('api_timesheets', __name__)

@api_timesheets.route('/', methods=['GET'])
@csrf.exempt
@login_required
def get_timesheets():
    """Recupera i timesheet (contenitori giornalieri) con filtri generali."""
    try:
        # Parametri query
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        user_id = request.args.get('user_id', type=int)
        status = request.args.get('status')
        limit = request.args.get('limit', type=int, default=50)
        offset = request.args.get('offset', type=int, default=0)

        # Query base
        query = Timesheet.query

        # Verifica permessi
        if not user_has_permission(current_user.role, 'view_all_timesheets'):
            # L'utente può vedere solo i propri timesheet
            query = query.filter(Timesheet.user_id == current_user.id)

        # Applica filtri
        if start_date:
            query = query.filter(Timesheet.date >= datetime.strptime(start_date, '%Y-%m-%d').date())
        if end_date:
            query = query.filter(Timesheet.date <= datetime.strptime(end_date, '%Y-%m-%d').date())
        if user_id:
            # Verifica permessi per vedere timesheet di altri utenti
            if not user_has_permission(current_user.role, 'view_all_timesheets') and user_id != current_user.id:
                return api_response(False, 'Non puoi visualizzare timesheet di altri utenti', status_code=403)
            query = query.filter(Timesheet.user_id == user_id)
        if status:
            query = query.filter(Timesheet.status == status)

        # Conta totale
        total = query.count()

        # Applica paginazione e ordina per data
        timesheets = query.order_by(Timesheet.date.desc()).offset(offset).limit(limit).all()

        # Prepara dati
        timesheets_data = []
        for ts in timesheets:
            # Calcola statistiche delle entry
            entries_data = []
            for entry in ts.entries:
                entries_data.append({
                    'id': entry.id,
                    'project_id': entry.project_id,
                    'project_name': entry.project.name if entry.project else None,
                    'task_id': entry.task_id,
                    'task_name': entry.task.name if entry.task else None,
                    'hours': entry.hours,
                    'description': entry.description,
                    'entry_type': entry.entry_type,
                    'is_billable': entry.is_billable
                })

            timesheets_data.append({
                'id': ts.id,
                'user_id': ts.user_id,
                'user_name': f"{ts.user.first_name} {ts.user.last_name}" if ts.user else None,
                'date': ts.date.isoformat(),
                'status': ts.status,
                'total_hours': ts.total_hours,
                'total_vacation_hours': ts.total_vacation_hours,
                'total_leave_hours': ts.total_leave_hours,
                'total_smartworking_hours': ts.total_smartworking_hours,
                'submission_date': ts.submission_date.isoformat() if ts.submission_date else None,
                'approval_date': ts.approval_date.isoformat() if ts.approval_date else None,
                'approved_by': ts.approved_by,
                'approver_name': f"{ts.approver.first_name} {ts.approver.last_name}" if ts.approver else None,
                'rejection_reason': ts.rejection_reason,
                'entries': entries_data,
                'can_be_submitted': ts.can_be_submitted,
                'can_be_approved': ts.can_be_approved,
                'created_at': ts.created_at.isoformat() if ts.created_at else None,
                'updated_at': ts.updated_at.isoformat() if ts.updated_at else None
            })

        return api_response(
            data=timesheets_data,
            message=f"Recuperati {len(timesheets_data)} timesheet",
            meta={
                'total': total,
                'limit': limit,
                'offset': offset,
                'has_more': offset + limit < total
            }
        )

    except Exception as e:
        return handle_api_error(e)

# === NUOVI ENDPOINT PER IL NUOVO SCHEMA ===

@api_timesheets.route('/daily/<string:date_str>', methods=['GET'])
@csrf.exempt
@login_required
def get_daily_timesheet(date_str):
    """Recupera o crea il timesheet giornaliero per un utente."""
    try:
        # Parse della data
        target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        user_id = request.args.get('user_id', type=int, default=current_user.id)

        # Verifica permessi
        if user_id != current_user.id and not user_has_permission(current_user.role, 'view_all_timesheets'):
            return api_response(False, 'Non puoi visualizzare timesheet di altri utenti', status_code=403)

        # Cerca timesheet esistente
        timesheet = Timesheet.query.filter_by(user_id=user_id, date=target_date).first()

        if not timesheet:
            # Crea timesheet vuoto se non esiste
            timesheet = Timesheet(
                user_id=user_id,
                date=target_date,
                status='draft'
            )
            db.session.add(timesheet)
            db.session.commit()

        # Prepara dati entry
        entries_data = []
        for entry in timesheet.entries:
            entries_data.append({
                'id': entry.id,
                'project_id': entry.project_id,
                'project_name': entry.project.name if entry.project else None,
                'task_id': entry.task_id,
                'task_name': entry.task.name if entry.task else None,
                'hours': entry.hours,
                'description': entry.description,
                'entry_type': entry.entry_type,
                'is_billable': entry.is_billable,
                'created_at': entry.created_at.isoformat() if entry.created_at else None,
                'updated_at': entry.updated_at.isoformat() if entry.updated_at else None
            })

        return api_response(
            data={
                'id': timesheet.id,
                'user_id': timesheet.user_id,
                'user_name': f"{timesheet.user.first_name} {timesheet.user.last_name}" if timesheet.user else None,
                'date': timesheet.date.isoformat(),
                'status': timesheet.status,
                'total_hours': timesheet.total_hours,
                'total_vacation_hours': timesheet.total_vacation_hours,
                'total_leave_hours': timesheet.total_leave_hours,
                'total_smartworking_hours': timesheet.total_smartworking_hours,
                'submission_date': timesheet.submission_date.isoformat() if timesheet.submission_date else None,
                'approval_date': timesheet.approval_date.isoformat() if timesheet.approval_date else None,
                'approved_by': timesheet.approved_by,
                'approver_name': f"{timesheet.approver.first_name} {timesheet.approver.last_name}" if timesheet.approver else None,
                'rejection_reason': timesheet.rejection_reason,
                'entries': entries_data,
                'can_be_submitted': timesheet.can_be_submitted,
                'can_be_approved': timesheet.can_be_approved,
                'created_at': timesheet.created_at.isoformat() if timesheet.created_at else None,
                'updated_at': timesheet.updated_at.isoformat() if timesheet.updated_at else None
            },
            message="Timesheet giornaliero recuperato con successo"
        )

    except Exception as e:
        return handle_api_error(e)

@api_timesheets.route('/entries', methods=['POST'])
@csrf.exempt
@login_required
def create_timesheet_entry():
    """Crea una nuova entry in un timesheet."""
    try:
        data = request.get_json()

        # Validazione campi richiesti
        required_fields = ['date', 'project_id', 'hours']
        for field in required_fields:
            if field not in data:
                return api_response(False, f'Campo {field} richiesto', status_code=400)

        # Parse della data
        target_date = datetime.strptime(data['date'], '%Y-%m-%d').date()
        user_id = data.get('user_id', current_user.id)

        # Verifica permessi
        if user_id != current_user.id and not user_has_permission(current_user.role, 'manage_timesheets'):
            return api_response(False, 'Non puoi inserire entry per altri utenti', status_code=403)

        # Verifica progetto
        project = Project.query.get_or_404(data['project_id'])

        # Trova o crea timesheet giornaliero
        timesheet = Timesheet.query.filter_by(user_id=user_id, date=target_date).first()
        if not timesheet:
            timesheet = Timesheet(
                user_id=user_id,
                date=target_date,
                status='draft'
            )
            db.session.add(timesheet)
            db.session.flush()  # Per ottenere l'ID

        # Verifica che il timesheet sia modificabile
        if timesheet.status not in ['draft']:
            return api_response(False, 'Non puoi modificare un timesheet già sottomesso o approvato', status_code=400)

        # Crea entry
        entry = TimesheetEntry(
            timesheet_id=timesheet.id,
            project_id=data['project_id'],
            task_id=data.get('task_id'),
            hours=float(data['hours']),
            description=data.get('description', ''),
            entry_type=data.get('entry_type', 'work')
        )

        db.session.add(entry)
        db.session.commit()

        return api_response(
            data={
                'id': entry.id,
                'timesheet_id': entry.timesheet_id,
                'project_id': entry.project_id,
                'task_id': entry.task_id,
                'hours': entry.hours,
                'description': entry.description,
                'entry_type': entry.entry_type,
                'is_billable': entry.is_billable
            },
            message='Entry timesheet creata con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_timesheets.route('/project/<int:project_id>/monthly', methods=['GET'])
@csrf.exempt
@login_required
def get_project_monthly_timesheet(project_id):
    """Recupera i timesheet mensili per un progetto con layout tabellare."""
    try:
        # Parametri query
        year = int(request.args.get('year', datetime.now().year))
        month = int(request.args.get('month', datetime.now().month))
        member_id = request.args.get('member_id', type=int)

        # Verifica permessi progetto
        project = Project.query.get_or_404(project_id)
        if not user_has_permission(current_user.role, 'view_all_projects'):
            # Verifica se l'utente è membro del team
            if not any(member.id == current_user.id for member in project.team_members):
                return api_response(False, 'Accesso negato al progetto', status_code=403)

        # Calcola giorni del mese
        days_in_month = monthrange(year, month)[1]

        # Query timesheet del progetto per il mese
        query = Timesheet.query.filter(
            Timesheet.project_id == project_id,
            extract('year', Timesheet.date) == year,
            extract('month', Timesheet.date) == month
        )

        # Filtra per membro se specificato
        if member_id:
            query = query.filter(Timesheet.user_id == member_id)

        timesheets = query.all()

        # Query task del progetto
        tasks = Task.query.filter(Task.project_id == project_id).all()

        # Organizza dati per task e giorni
        task_daily_data = {}
        task_totals = {}

        for task in tasks:
            task_daily_data[task.id] = {day: 0 for day in range(1, days_in_month + 1)}
            task_totals[task.id] = 0

        # Popola dati timesheet
        for ts in timesheets:
            day = ts.date.day
            task_id = ts.task_id
            if task_id and task_id in task_daily_data:
                task_daily_data[task_id][day] += ts.hours
                task_totals[task_id] += ts.hours

        # Prepara dati task con timesheet
        tasks_data = []
        for task in tasks:
            # Trova lavoratori per questo task nel mese
            workers = db.session.query(User).join(Timesheet).filter(
                Timesheet.task_id == task.id,
                Timesheet.project_id == project_id,
                extract('year', Timesheet.date) == year,
                extract('month', Timesheet.date) == month
            ).distinct().all()

            tasks_data.append({
                'id': task.id,
                'name': task.name,
                'description': task.description,
                'daily_hours': task_daily_data[task.id],
                'total_hours': task_totals[task.id],
                'workers': [f"{w.first_name} {w.last_name}" for w in workers]
            })

        # Calcola totali giornalieri
        daily_totals = {day: 0 for day in range(1, days_in_month + 1)}
        for task_id, daily_data in task_daily_data.items():
            for day, hours in daily_data.items():
                daily_totals[day] += hours

        return api_response(
            data={
                'year': year,
                'month': month,
                'days_in_month': days_in_month,
                'tasks': tasks_data,
                'daily_totals': daily_totals,
                'grand_total': sum(daily_totals.values()),
                'project': {
                    'id': project.id,
                    'name': project.name
                }
            },
            message="Timesheet mensile recuperato con successo"
        )

    except Exception as e:
        return handle_api_error(e)


@api_timesheets.route('/project/<int:project_id>', methods=['GET'])
@csrf.exempt
@login_required
def get_project_timesheets(project_id):
    """Recupera i timesheet per un progetto con filtri."""
    try:
        # Parametri query
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        task_id = request.args.get('task_id', type=int)
        user_id = request.args.get('user_id', type=int)

        # Verifica permessi progetto
        project = Project.query.get_or_404(project_id)
        if not user_has_permission(current_user.role, 'view_all_projects'):
            if not any(member.id == current_user.id for member in project.team_members):
                return api_response(False, 'Accesso negato al progetto', status_code=403)

        # Query base
        query = Timesheet.query.filter(Timesheet.project_id == project_id)

        # Applica filtri
        if start_date:
            query = query.filter(Timesheet.date >= datetime.strptime(start_date, '%Y-%m-%d').date())
        if end_date:
            query = query.filter(Timesheet.date <= datetime.strptime(end_date, '%Y-%m-%d').date())
        if task_id:
            query = query.filter(Timesheet.task_id == task_id)
        if user_id:
            query = query.filter(Timesheet.user_id == user_id)

        # Ordina per data
        timesheets = query.order_by(Timesheet.date.desc()).all()

        # Prepara dati
        timesheets_data = []
        for ts in timesheets:
            timesheets_data.append({
                'id': ts.id,
                'user_id': ts.user_id,
                'task_id': ts.task_id,
                'date': ts.date.isoformat(),
                'hours': ts.hours,
                'description': ts.description,
                'status': ts.status,
                'user': {
                    'id': ts.user.id,
                    'first_name': ts.user.first_name,
                    'last_name': ts.user.last_name
                } if ts.user else None,
                'task': {
                    'id': ts.task.id,
                    'name': ts.task.name
                } if ts.task else None
            })

        return api_response(
            data=timesheets_data,
            message="Timesheet recuperati con successo"
        )

    except Exception as e:
        return handle_api_error(e)


@api_timesheets.route('/', methods=['POST'])
@csrf.exempt
@login_required
def create_timesheet():
    """Crea un nuovo timesheet."""
    try:
        data = request.get_json()

        # Validazione campi richiesti
        required_fields = ['project_id', 'date', 'hours']
        for field in required_fields:
            if field not in data:
                return api_response(
                    False,
                    f'Campo {field} richiesto',
                    status_code=400
                )

        # Verifica permessi progetto
        project = Project.query.get_or_404(data['project_id'])
        if not user_has_permission(current_user.role, 'manage_timesheets'):
            # Verifica se può inserire timesheet per se stesso
            user_id = data.get('user_id', current_user.id)
            if user_id != current_user.id:
                return api_response(False, 'Non puoi inserire timesheet per altri utenti', status_code=403)

        # Crea timesheet
        timesheet = Timesheet(
            user_id=data.get('user_id', current_user.id),
            project_id=data['project_id'],
            task_id=data.get('task_id'),
            date=datetime.strptime(data['date'], '%Y-%m-%d').date(),
            hours=float(data['hours']),
            description=data.get('description', ''),
            status=data.get('status', 'pending')
        )

        db.session.add(timesheet)
        db.session.commit()

        return api_response(
            data={'id': timesheet.id},
            message='Timesheet creato con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_timesheets.route('/<int:timesheet_id>', methods=['PUT'])
@csrf.exempt
@login_required
def update_timesheet(timesheet_id):
    """Aggiorna un timesheet esistente."""
    try:
        timesheet = Timesheet.query.get_or_404(timesheet_id)

        # Verifica permessi
        if not user_has_permission(current_user.role, 'manage_timesheets'):
            if timesheet.user_id != current_user.id:
                return api_response(False, 'Non puoi modificare timesheet di altri utenti', status_code=403)

        data = request.get_json()

        # Aggiorna campi
        if 'date' in data:
            timesheet.date = datetime.strptime(data['date'], '%Y-%m-%d').date()
        if 'hours' in data:
            timesheet.hours = float(data['hours'])
        if 'description' in data:
            timesheet.description = data['description']
        if 'status' in data:
            timesheet.status = data['status']
        if 'task_id' in data:
            timesheet.task_id = data['task_id']

        db.session.commit()

        return api_response(
            data={},
            message='Timesheet aggiornato con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)


@api_timesheets.route('/<int:timesheet_id>', methods=['DELETE'])
@csrf.exempt
@login_required
def delete_timesheet(timesheet_id):
    """Elimina un timesheet."""
    try:
        timesheet = Timesheet.query.get_or_404(timesheet_id)

        # Verifica permessi
        if not user_has_permission(current_user.role, 'manage_timesheets'):
            if timesheet.user_id != current_user.id:
                return api_response(False, 'Non puoi eliminare timesheet di altri utenti', status_code=403)

        db.session.delete(timesheet)
        db.session.commit()

        return api_response(
            data={},
            message='Timesheet eliminato con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)