<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Analytics Timesheet</h1>
          <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Report avanzati e analisi delle performance del team
          </p>
        </div>
        <div class="flex items-center space-x-3">
          <button 
            @click="exportData"
            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Esporta Report
          </button>
          <button 
            @click="refreshData"
            :disabled="loading"
            class="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {{ loading ? 'Caricando...' : 'Aggiorna' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Periodo</label>
          <select v-model="selectedPeriod" @change="refreshData" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
            <option value="current_month">Mese Corrente</option>
            <option value="last_month">Mese Scorso</option>
            <option value="current_quarter">Trimestre Corrente</option>
            <option value="last_quarter">Trimestre Scorso</option>
            <option value="current_year">Anno Corrente</option>
            <option value="custom">Personalizzato</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Dipartimento</label>
          <select v-model="selectedDepartment" @change="refreshData" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
            <option value="">Tutti</option>
            <option v-for="dept in departments" :key="dept.id" :value="dept.id">{{ dept.name }}</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Progetto</label>
          <select v-model="selectedProject" @change="refreshData" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
            <option value="">Tutti</option>
            <option v-for="project in projects" :key="project.id" :value="project.id">{{ project.name }}</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Tipo Analisi</label>
          <select v-model="selectedAnalysisType" @change="refreshData" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
            <option value="productivity">Produttività</option>
            <option value="utilization">Utilizzo</option>
            <option value="billing">Fatturazione</option>
            <option value="trends">Trend</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Ore Totali</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ metrics.totalHours }}h</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              <span :class="metrics.totalHoursChange >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ metrics.totalHoursChange >= 0 ? '+' : '' }}{{ metrics.totalHoursChange }}%
              </span>
              vs periodo precedente
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Produttività Media</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ metrics.avgProductivity }}%</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              <span :class="metrics.productivityChange >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ metrics.productivityChange >= 0 ? '+' : '' }}{{ metrics.productivityChange }}%
              </span>
              vs periodo precedente
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Revenue Generato</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">€{{ formatCurrency(metrics.revenue) }}</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              <span :class="metrics.revenueChange >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ metrics.revenueChange >= 0 ? '+' : '' }}{{ metrics.revenueChange }}%
              </span>
              vs periodo precedente
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900/20 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Utilizzo Risorse</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ metrics.utilization }}%</p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              <span :class="metrics.utilizationChange >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ metrics.utilizationChange >= 0 ? '+' : '' }}{{ metrics.utilizationChange }}%
              </span>
              vs periodo precedente
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Productivity Trend Chart -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">Trend Produttività</h3>
          <div class="flex items-center space-x-2">
            <button 
              @click="chartPeriod = 'week'"
              :class="chartPeriod === 'week' ? 'bg-blue-100 text-blue-700' : 'text-gray-500'"
              class="px-3 py-1 rounded text-sm"
            >
              Settimana
            </button>
            <button 
              @click="chartPeriod = 'month'"
              :class="chartPeriod === 'month' ? 'bg-blue-100 text-blue-700' : 'text-gray-500'"
              class="px-3 py-1 rounded text-sm"
            >
              Mese
            </button>
          </div>
        </div>
        <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded">
          <p class="text-gray-500 dark:text-gray-400">Grafico Produttività (Chart.js placeholder)</p>
        </div>
      </div>

      <!-- Team Performance Chart -->
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Performance Team</h3>
        <div class="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded">
          <p class="text-gray-500 dark:text-gray-400">Grafico Performance Team (Chart.js placeholder)</p>
        </div>
      </div>
    </div>

    <!-- Detailed Analytics Table -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Analisi Dettagliata per Dipendente</h3>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Dipendente
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Ore Lavorate
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Ore Fatturabili
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Produttività
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Revenue
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Progetti Attivi
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-if="loading">
              <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                Caricamento dati analytics...
              </td>
            </tr>
            <tr v-else-if="analyticsData.length === 0">
              <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                Nessun dato disponibile per il periodo selezionato
              </td>
            </tr>
            <tr v-else v-for="employee in analyticsData" :key="employee.id">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                      <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ getInitials(employee.full_name) }}
                      </span>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                      {{ employee.full_name }}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                      {{ employee.department }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ employee.total_hours }}h
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ employee.billable_hours }}h
                <span class="text-xs text-gray-500 dark:text-gray-400">
                  ({{ Math.round((employee.billable_hours / employee.total_hours) * 100) }}%)
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2">
                    <div 
                      class="h-2 rounded-full"
                      :class="getProductivityColor(employee.productivity)"
                      :style="{ width: employee.productivity + '%' }"
                    ></div>
                  </div>
                  <span class="text-sm text-gray-900 dark:text-white">{{ employee.productivity }}%</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                €{{ formatCurrency(employee.revenue) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ employee.active_projects }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// State
const loading = ref(false)
const departments = ref([])
const projects = ref([])
const analyticsData = ref([])
const chartPeriod = ref('month')

// Filters
const selectedPeriod = ref('current_month')
const selectedDepartment = ref('')
const selectedProject = ref('')
const selectedAnalysisType = ref('productivity')

// Metrics data
const metrics = ref({
  totalHours: 0,
  totalHoursChange: 0,
  avgProductivity: 0,
  productivityChange: 0,
  revenue: 0,
  revenueChange: 0,
  utilization: 0,
  utilizationChange: 0
})

// Methods
const getDateRange = () => {
  const today = new Date()
  let startDate, endDate

  switch (selectedPeriod.value) {
    case 'current_month':
      startDate = new Date(today.getFullYear(), today.getMonth(), 1)
      endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0)
      break
    case 'last_month':
      startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1)
      endDate = new Date(today.getFullYear(), today.getMonth(), 0)
      break
    case 'current_quarter':
      const currentQuarter = Math.floor(today.getMonth() / 3)
      startDate = new Date(today.getFullYear(), currentQuarter * 3, 1)
      endDate = new Date(today.getFullYear(), (currentQuarter + 1) * 3, 0)
      break
    case 'last_quarter':
      const lastQuarter = Math.floor(today.getMonth() / 3) - 1
      const year = lastQuarter < 0 ? today.getFullYear() - 1 : today.getFullYear()
      const quarter = lastQuarter < 0 ? 3 : lastQuarter
      startDate = new Date(year, quarter * 3, 1)
      endDate = new Date(year, (quarter + 1) * 3, 0)
      break
    case 'current_year':
      startDate = new Date(today.getFullYear(), 0, 1)
      endDate = new Date(today.getFullYear(), 11, 31)
      break
    default:
      startDate = new Date(today.getFullYear(), today.getMonth(), 1)
      endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0)
  }

  return {
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0]
  }
}

const calculateMetrics = () => {
  const totalHours = analyticsData.value.reduce((sum, user) => sum + user.total_hours, 0)
  const totalBillableHours = analyticsData.value.reduce((sum, user) => sum + user.billable_hours, 0)
  const totalRevenue = analyticsData.value.reduce((sum, user) => sum + user.revenue, 0)

  const avgProductivity = analyticsData.value.length > 0
    ? analyticsData.value.reduce((sum, user) => sum + user.productivity, 0) / analyticsData.value.length
    : 0

  const utilization = totalHours > 0 ? (totalBillableHours / totalHours) * 100 : 0

  metrics.value = {
    totalHours: Math.round(totalHours),
    totalHoursChange: 0, // TODO: Calculate vs previous period
    avgProductivity: Math.round(avgProductivity),
    productivityChange: 0, // TODO: Calculate vs previous period
    revenue: Math.round(totalRevenue),
    revenueChange: 0, // TODO: Calculate vs previous period
    utilization: Math.round(utilization),
    utilizationChange: 0 // TODO: Calculate vs previous period
  }
}

const refreshData = async () => {
  loading.value = true

  try {
    await Promise.all([
      loadAnalyticsData(),
      loadDepartments(),
      loadProjects()
    ])
    calculateMetrics()
  } finally {
    loading.value = false
  }
}

const loadAnalyticsData = async () => {
  try {
    // Calculate date range based on selected period
    const { startDate, endDate } = getDateRange()
    console.log('Loading analytics for date range:', startDate, 'to', endDate)

    const params = new URLSearchParams({
      start_date: startDate,
      end_date: endDate
    })

    if (selectedDepartment.value) {
      params.append('department_id', selectedDepartment.value)
    }

    if (selectedProject.value) {
      params.append('project_id', selectedProject.value)
    }

    console.log('Fetching timesheets with params:', params.toString())

    const response = await fetch(`/api/timesheets/?${params}`, {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      const result = await response.json()
      const timesheets = result.data || []
      console.log('Received timesheets:', timesheets.length, 'entries')
      console.log('Sample timesheet entry:', timesheets[0])

      // Group by user and calculate analytics
      const userAnalytics = {}

      timesheets.forEach(entry => {
        const userId = entry.user_id || entry.user?.id || 'unknown'
        if (!userAnalytics[userId]) {
          userAnalytics[userId] = {
            id: userId,
            full_name: entry.user?.full_name || entry.user?.name || `User ${userId}`,
            department: entry.user?.department || 'N/A',
            total_hours: 0,
            billable_hours: 0,
            projects: new Set(),
            entries: []
          }
        }

        userAnalytics[userId].total_hours += entry.hours
        if (entry.billable) {
          userAnalytics[userId].billable_hours += entry.hours
        }
        if (entry.project_id) {
          userAnalytics[userId].projects.add(entry.project_id)
        }
        userAnalytics[userId].entries.push(entry)
      })

      // Convert to array and calculate additional metrics
      analyticsData.value = Object.values(userAnalytics).map(user => ({
        ...user,
        active_projects: user.projects.size,
        productivity: user.total_hours > 0 ? Math.round((user.billable_hours / user.total_hours) * 100) : 0,
        revenue: Math.round(user.billable_hours * 50) // Assuming €50/hour average rate
      }))

      console.log('Final analytics data:', analyticsData.value)
    } else {
      console.error('Failed to load analytics data:', response.status, response.statusText)
      analyticsData.value = []
    }
  } catch (err) {
    console.error('Error loading analytics data:', err)
    analyticsData.value = []
  }
}

const loadDepartments = async () => {
  try {
    const response = await fetch('/api/departments/', {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      const result = await response.json()
      departments.value = result.data || []
    }
  } catch (err) {
    console.error('Error loading departments:', err)
    departments.value = []
  }
}

const loadProjects = async () => {
  try {
    const response = await fetch('/api/projects/', {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      const result = await response.json()
      projects.value = result.data || []
    }
  } catch (err) {
    console.error('Error loading projects:', err)
    projects.value = []
  }
}

const exportData = () => {
  // Implement CSV export
  alert('Funzionalità di export in sviluppo')
}

const getInitials = (fullName) => {
  if (!fullName) return '??'
  return fullName.split(' ').map(n => n[0]).join('').toUpperCase()
}

const getProductivityColor = (productivity) => {
  if (productivity >= 90) return 'bg-green-500'
  if (productivity >= 75) return 'bg-yellow-500'
  return 'bg-red-500'
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('it-IT').format(amount)
}

// Lifecycle
onMounted(() => {
  refreshData()
})
</script>
