#!/usr/bin/env python3
"""
Script per ripristinare TUTTI i file di ieri sera intorno alle 21:00
ESCLUSA la cartella /tasks
"""
import json
import os
import glob
from datetime import datetime
from datetime import datetime
from datetime import datetime

def restore_file(checkpoint_path, target_path):
    """Ripristina un file da checkpoint"""
    try:
        with open(checkpoint_path, 'r') as f:
            data = json.load(f)

        # Crea le directory se non esistono
        os.makedirs(os.path.dirname(target_path), exist_ok=True)

        with open(target_path, 'w') as out:
            out.write(data['modifiedCode'])

        print(f"✅ {target_path}")
        return True
    except Exception as e:
        print(f"❌ {target_path}: {e}")
        return False

def main():
    # Trova tutti i checkpoint
    pattern = ".config/.vscode-server/data/User/workspaceStorage/*/Augment.vscode-augment/augment-user-assets/checkpoint-documents/*/document-*-*.json"

    checkpoints = glob.glob(pattern)
    print(f"Trovati {len(checkpoints)} checkpoint totali")

    # Target: 31 maggio 2025 intorno alle 21:00
    target_date = datetime(2025, 5, 31, 21, 0, 0)

    # Raggruppa per file (prende il più vicino alle 21:00 del 31 maggio)
    files_map = {}
    valid_count = 0

    for checkpoint in checkpoints:
        # Estrae il path del file dal nome del checkpoint
        parts = checkpoint.split('document-')[1].split('-')
        if len(parts) < 2:
            continue

        filename = parts[0]
        timestamp_str = parts[1]

        # ESCLUDI la cartella tasks
        if 'tasks_' in filename:
            continue

        try:
            # Converte timestamp da millisecondi a datetime
            timestamp_ms = int(timestamp_str)
            timestamp_dt = datetime.fromtimestamp(timestamp_ms / 1000)

            # Verifica che sia del 31 maggio 2025 prima delle 21:00
            if (timestamp_dt.year == 2025 and
                timestamp_dt.month == 5 and
                timestamp_dt.day == 31 and
                timestamp_dt.hour <= 21):

                valid_count += 1

                # Calcola distanza dalle 21:00
                distance = abs((timestamp_dt - target_date).total_seconds())

                # Prende il più vicino alle 21:00
                if (filename not in files_map or
                    distance < files_map[filename]['distance']):
                    files_map[filename] = {
                        'checkpoint': checkpoint,
                        'timestamp': timestamp_str,
                        'datetime': timestamp_dt,
                        'distance': distance
                    }

        except (ValueError, OSError):
            continue

    print(f"File unici da ripristinare (esclusi /tasks): {len(files_map)}")

    success_count = 0
    for filename, info in sorted(files_map.items()):
        # Converte il nome del file in path reale
        target_path = filename.replace('_home_runner_workspace_', '').replace('_', '/')

        if restore_file(info['checkpoint'], target_path):
            success_count += 1

    print(f"\n🎯 Ripristinati {success_count}/{len(files_map)} file di ieri sera!")

if __name__ == "__main__":
    main()
