#!/usr/bin/env python3
"""
Script per ripristinare TUTTI i file di ieri sera intorno alle 21:00
ESCLUSA la cartella /tasks
"""
import json
import os
import glob
from datetime import datetime
from datetime import datetime

def restore_file(checkpoint_path, target_path):
    """Ripristina un file da checkpoint"""
    try:
        with open(checkpoint_path, 'r') as f:
            data = json.load(f)

        # Crea le directory se non esistono
        os.makedirs(os.path.dirname(target_path), exist_ok=True)

        with open(target_path, 'w') as out:
            out.write(data['modifiedCode'])

        print(f"✅ {target_path}")
        return True
    except Exception as e:
        print(f"❌ {target_path}: {e}")
        return False

def main():
    # Trova tutti i checkpoint di ieri sera (1748[6-7]xxxxx - più ampio range)
    pattern = ".config/.vscode-server/data/User/workspaceStorage/*/Augment.vscode-augment/augment-user-assets/checkpoint-documents/*/document-*-1748[6-7]*-*.json"

    checkpoints = glob.glob(pattern)
    print(f"Trovati {len(checkpoints)} checkpoint di ieri sera")

    # Raggruppa per file (prende il timestamp PIÙ ALTO per ogni file)
    files_map = {}
    for checkpoint in checkpoints:
        # Estrae il path del file dal nome del checkpoint
        parts = checkpoint.split('document-')[1].split('-')
        filename = parts[0]
        timestamp = parts[1]  # Il timestamp completo

        # ESCLUDI la cartella tasks
        if 'tasks_' in filename:
            continue

        # Prende il timestamp PIÙ ALTO (più recente)
        if filename not in files_map or int(timestamp) > int(files_map[filename]['timestamp']):
            files_map[filename] = {
                'checkpoint': checkpoint,
                'timestamp': timestamp
            }

    print(f"File unici da ripristinare (esclusi /tasks): {len(files_map)}")

    success_count = 0
    for filename, info in sorted(files_map.items()):
        # Converte il nome del file in path reale
        target_path = filename.replace('_home_runner_workspace_', '').replace('_', '/')

        if restore_file(info['checkpoint'], target_path):
            success_count += 1

    print(f"\n🎯 Ripristinati {success_count}/{len(files_map)} file di ieri sera!")

if __name__ == "__main__":
    main()
