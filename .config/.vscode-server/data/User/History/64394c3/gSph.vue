<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Storico Timesheet</h1>
          <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Visualizza i riepiloghi mensili delle tue ore lavorate
          </p>
        </div>

        <div class="flex space-x-3">
          <button
            @click="exportData"
            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Esporta Report
          </button>
          <router-link
            to="/app/timesheet/entry"
            class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Registra Ore
          </router-link>
        </div>
      </div>
    </div>

    <!-- Filtri -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Anno
          </label>
          <select
            v-model="selectedYear"
            @change="loadMonthlySummaries"
            class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
          >
            <option v-for="year in availableYears" :key="year" :value="year">
              {{ year }}
            </option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Progetto
          </label>
          <select
            v-model="selectedProject"
            @change="loadMonthlySummaries"
            class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
          >
            <option value="">Tutti i progetti</option>
            <option
              v-for="project in projects"
              :key="project.id"
              :value="project.id"
            >
              {{ project.name }}
            </option>
          </select>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Visualizzazione
          </label>
          <select
            v-model="viewMode"
            @change="loadMonthlySummaries"
            class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
          >
            <option value="monthly">Riepilogo Mensile</option>
            <option value="detailed">Dettaglio Giornaliero</option>
          </select>
        </div>

        <div class="flex items-end">
          <button
            @click="loadMonthlySummaries"
            :disabled="loading"
            class="w-full bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            {{ loading ? 'Caricando...' : 'Aggiorna' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Statistiche Periodo -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Ore Totali
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ formatHours(summary.totalHours) }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Ore Fatturabili
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ formatHours(summary.billableHours) }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Progetti Attivi
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ summary.activeProjects }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Media Giornaliera
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ formatHours(summary.dailyAverage) }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Riepiloghi Mensili -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            {{ viewMode === 'monthly' ? 'Riepiloghi Mensili' : 'Dettaglio Giornaliero' }} - {{ selectedYear }}
          </h3>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            {{ monthlySummaries.length }} {{ viewMode === 'monthly' ? 'mesi' : 'giorni' }} trovati
          </div>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr v-if="viewMode === 'monthly'">
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Mese
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Ore Totali
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Ore Fatturabili
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Produttività
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Progetti
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Media Giornaliera
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Stato
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Azioni
              </th>
            </tr>
            <tr v-else>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Data
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Progetto
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Task
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Ore
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Fatturabile
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Stato
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Descrizione
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <!-- Monthly View -->
            <template v-if="viewMode === 'monthly'">
              <tr v-for="month in monthlySummaries" :key="month.month" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                      {{ getMonthName(month.month) }} {{ selectedYear }}
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {{ formatHours(month.total_hours) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {{ formatHours(month.billable_hours) }}
                  <span class="text-xs text-gray-500 dark:text-gray-400 ml-1">
                    ({{ Math.round((month.billable_hours / month.total_hours) * 100) || 0 }}%)
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2 max-w-[60px]">
                      <div
                        class="h-2 rounded-full"
                        :class="getProductivityColor(month.productivity)"
                        :style="{ width: Math.min(month.productivity, 100) + '%' }"
                      ></div>
                    </div>
                    <span class="text-sm text-gray-900 dark:text-white">{{ Math.round(month.productivity) }}%</span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  <div class="flex flex-wrap gap-1">
                    <span
                      v-for="project in month.projects.slice(0, 2)"
                      :key="project.id"
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                    >
                      {{ project.name }}
                    </span>
                    <span
                      v-if="month.projects.length > 2"
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                    >
                      +{{ month.projects.length - 2 }}
                    </span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {{ formatHours(month.daily_average) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="getMonthStatusClass(month.status)"
                  >
                    {{ getMonthStatusText(month.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button
                    @click="viewMonthDetails(month)"
                    class="text-primary-600 hover:text-primary-900 dark:text-primary-400 dark:hover:text-primary-300 mr-3"
                  >
                    Dettagli
                  </button>
                  <button
                    @click="exportMonth(month)"
                    class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                  >
                    Esporta
                  </button>
                </td>
              </tr>
            </template>

            <!-- Detailed View -->
            <template v-else>
              <tr v-for="entry in detailedEntries" :key="entry.id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {{ formatDate(entry.date) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {{ entry.project?.name || 'N/A' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {{ entry.task?.name || 'N/A' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {{ formatHours(entry.hours) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="entry.billable ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'"
                  >
                    {{ entry.billable ? 'Fatturabile' : 'Interno' }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="getStatusClass(entry.status)"
                  >
                    {{ getStatusText(entry.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 text-sm text-gray-900 dark:text-white max-w-xs truncate">
                  {{ entry.description || '-' }}
                </td>
              </tr>
            </template>
          </tbody>
        </table>

        <!-- Empty state -->
        <div v-if="(viewMode === 'monthly' ? monthlySummaries : detailedEntries).length === 0" class="text-center py-12">
          <div class="mx-auto h-12 w-12 text-gray-400">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">Nessun dato disponibile</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Non ci sono registrazioni per l'anno {{ selectedYear }}{{ selectedProject ? ' e progetto selezionato' : '' }}.
          </p>
          <div class="mt-4">
            <router-link
              to="/app/timesheet/entry"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200 dark:bg-primary-900 dark:text-primary-300 dark:hover:bg-primary-800"
            >
              Inizia a registrare ore
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Paginazione -->
    <div v-if="pagination.totalPages > 1" class="bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6 rounded-lg shadow">
      <div class="flex-1 flex justify-between sm:hidden">
        <button 
          @click="changePage(pagination.currentPage - 1)"
          :disabled="pagination.currentPage === 1"
          class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
        >
          Precedente
        </button>
        <button 
          @click="changePage(pagination.currentPage + 1)"
          :disabled="pagination.currentPage === pagination.totalPages"
          class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
        >
          Successivo
        </button>
      </div>
      <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
          <p class="text-sm text-gray-700 dark:text-gray-300">
            Mostrando
            <span class="font-medium">{{ (pagination.currentPage - 1) * pagination.perPage + 1 }}</span>
            a
            <span class="font-medium">{{ Math.min(pagination.currentPage * pagination.perPage, pagination.total) }}</span>
            di
            <span class="font-medium">{{ pagination.total }}</span>
            risultati
          </p>
        </div>
        <div>
          <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
            <button 
              v-for="page in visiblePages" 
              :key="page"
              @click="changePage(page)"
              :class="[
                'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                page === pagination.currentPage 
                  ? 'z-10 bg-primary-50 border-primary-500 text-primary-600' 
                  : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
              ]"
            >
              {{ page }}
            </button>
          </nav>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// State
const projects = ref([])
const monthlySummaries = ref([])
const detailedEntries = ref([])
const loading = ref(false)

// Filters
const selectedYear = ref(new Date().getFullYear())
const selectedProject = ref('')
const viewMode = ref('monthly')

// Available years (last 5 years)
const availableYears = ref([])

// Pagination
const pagination = ref({
  currentPage: 1,
  perPage: 50,
  total: 0,
  totalPages: 0
})

// Summary
const summary = ref({
  totalHours: 0,
  billableHours: 0,
  activeProjects: 0,
  dailyAverage: 0
})

// Computed
const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, pagination.value.currentPage - 2)
  const end = Math.min(pagination.value.totalPages, pagination.value.currentPage + 2)
  
  for (let i = start; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

// Methods
const initializeYears = () => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let i = 0; i < 5; i++) {
    years.push(currentYear - i)
  }
  availableYears.value = years
}

const loadProjects = async () => {
  try {
    const response = await fetch('/api/projects/', {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      const result = await response.json()
      // Handle both paginated and non-paginated responses
      if (result.data && Array.isArray(result.data.items)) {
        projects.value = result.data.items
      } else if (result.data && Array.isArray(result.data)) {
        projects.value = result.data
      } else if (Array.isArray(result)) {
        projects.value = result
      } else {
        projects.value = []
      }
      console.log('Loaded projects:', projects.value)
    } else {
      console.error('Failed to load projects:', response.status)
      projects.value = []
    }
  } catch (err) {
    console.error('Error loading projects:', err)
    projects.value = []
  }
}

const loadMonthlySummaries = async () => {
  loading.value = true

  try {
    if (viewMode.value === 'monthly') {
      await loadMonthlyData()
    } else {
      await loadDetailedData()
    }

    calculateSummary()
  } catch (err) {
    console.error('Error loading data:', err)
  } finally {
    loading.value = false
  }
}

const loadMonthlyData = async () => {
  // Generate monthly summaries from timesheet data
  const params = new URLSearchParams({
    start_date: `${selectedYear.value}-01-01`,
    end_date: `${selectedYear.value}-12-31`
  })

  if (selectedProject.value) {
    params.append('project_id', selectedProject.value)
  }

  const response = await fetch(`/api/timesheets/?${params}`, {
    headers: {
      'Content-Type': 'application/json',
      'X-CSRFToken': authStore.csrfToken
    }
  })

  if (response.ok) {
    const result = await response.json()
    const timesheets = result.data || []

    // Group by month and calculate summaries
    const monthlyData = {}

    timesheets.forEach(entry => {
      const date = new Date(entry.date)
      const month = date.getMonth() + 1

      if (!monthlyData[month]) {
        monthlyData[month] = {
          month,
          total_hours: 0,
          billable_hours: 0,
          entries: [],
          projects: new Set(),
          working_days: new Set()
        }
      }

      monthlyData[month].total_hours += entry.hours
      if (entry.billable) {
        monthlyData[month].billable_hours += entry.hours
      }
      monthlyData[month].entries.push(entry)
      if (entry.project) {
        monthlyData[month].projects.add(JSON.stringify({
          id: entry.project.id,
          name: entry.project.name
        }))
      }
      monthlyData[month].working_days.add(date.getDate())
    })

    // Convert to array and calculate additional metrics
    monthlySummaries.value = Object.values(monthlyData).map(month => ({
      ...month,
      projects: Array.from(month.projects).map(p => JSON.parse(p)),
      daily_average: month.total_hours / Math.max(month.working_days.size, 1),
      productivity: (month.billable_hours / month.total_hours) * 100 || 0,
      status: month.total_hours > 0 ? 'active' : 'inactive'
    })).sort((a, b) => a.month - b.month)
  }
}

const loadDetailedData = async () => {
  const params = new URLSearchParams({
    start_date: `${selectedYear.value}-01-01`,
    end_date: `${selectedYear.value}-12-31`,
    page: pagination.value.currentPage,
    per_page: pagination.value.perPage
  })

  if (selectedProject.value) {
    params.append('project_id', selectedProject.value)
  }

  const response = await fetch(`/api/timesheets/?${params}`, {
    headers: {
      'Content-Type': 'application/json',
      'X-CSRFToken': authStore.csrfToken
    }
  })

  if (response.ok) {
    const result = await response.json()
    detailedEntries.value = result.data || []

    // Update pagination if provided
    if (result.pagination) {
      pagination.value = {
        ...pagination.value,
        ...result.pagination
      }
    }
  }
}

const calculateSummary = () => {
  let total = 0
  let billable = 0
  let projectIds = new Set()

  if (viewMode.value === 'monthly') {
    total = monthlySummaries.value.reduce((sum, month) => sum + month.total_hours, 0)
    billable = monthlySummaries.value.reduce((sum, month) => sum + month.billable_hours, 0)
    monthlySummaries.value.forEach(month => {
      month.projects.forEach(project => projectIds.add(project.id))
    })
  } else {
    total = detailedEntries.value.reduce((sum, entry) => sum + entry.hours, 0)
    billable = detailedEntries.value.reduce((sum, entry) => sum + (entry.billable ? entry.hours : 0), 0)
    projectIds = new Set(detailedEntries.value.map(entry => entry.project_id))
  }

  summary.value = {
    totalHours: total,
    billableHours: billable,
    activeProjects: projectIds.size,
    dailyAverage: total / 365 // Yearly average
  }
}

const changePage = (page) => {
  if (page >= 1 && page <= pagination.value.totalPages) {
    pagination.value.currentPage = page
    loadMonthlySummaries()
  }
}

// Helper methods
const getMonthName = (month) => {
  const monthNames = [
    'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',
    'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'
  ]
  return monthNames[month - 1]
}

const getProductivityColor = (productivity) => {
  if (productivity >= 80) return 'bg-green-500'
  if (productivity >= 60) return 'bg-yellow-500'
  return 'bg-red-500'
}

const getMonthStatusClass = (status) => {
  switch (status) {
    case 'active':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    case 'inactive':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
    default:
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
  }
}

const getMonthStatusText = (status) => {
  switch (status) {
    case 'active':
      return 'Attivo'
    case 'inactive':
      return 'Inattivo'
    default:
      return 'Parziale'
  }
}

const getStatusClass = (status) => {
  switch (status) {
    case 'approved':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    case 'rejected':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    default:
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'approved':
      return 'Approvato'
    case 'rejected':
      return 'Rifiutato'
    default:
      return 'In Attesa'
  }
}

const viewMonthDetails = (month) => {
  // Switch to detailed view for specific month
  viewMode.value = 'detailed'
  // You could add month filtering here
  loadMonthlySummaries()
}

const exportMonth = (month) => {
  // Export specific month data
  alert(`Esportazione dati per ${getMonthName(month.month)} ${selectedYear.value} - Funzionalità in sviluppo`)
}

const exportData = () => {
  // Export all data
  alert('Esportazione report completo - Funzionalità in sviluppo')
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('it-IT')
}

const formatHours = (hours) => {
  if (!hours || hours === 0) return '0h'
  return hours % 1 === 0 ? `${hours}h` : `${hours.toFixed(1)}h`
}

// Lifecycle
onMounted(() => {
  initializeYears()
  loadProjects()
  loadMonthlySummaries()
})

// Watchers
watch([selectedYear, selectedProject, viewMode], () => {
  pagination.value.currentPage = 1
  loadMonthlySummaries()
})
</script>
