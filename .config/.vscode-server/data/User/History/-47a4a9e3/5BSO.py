"""
API Blueprint per la gestione delle richieste di time-off (Task 3.1).
Gestisce richieste di ferie, permessi e smartworking.
"""
from flask import Blueprint, request, jsonify
from flask_login import current_user, login_required
from sqlalchemy import extract, func, and_, or_
from datetime import datetime, date, timedelta

from models import TimeOffRequest, User
from utils.api_utils import api_response, handle_api_error
from utils.permissions import user_has_permission
from extensions import db
from extensions import csrf

api_time_off = Blueprint('api_time_off', __name__)

@api_time_off.route('/', methods=['GET'])
@csrf.exempt
@login_required
def get_time_off_requests():
    """Recupera le richieste di time-off con filtri."""
    try:
        # Parametri query
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        user_id = request.args.get('user_id', type=int)
        request_type = request.args.get('request_type')
        status = request.args.get('status')
        limit = request.args.get('limit', type=int, default=50)
        offset = request.args.get('offset', type=int, default=0)

        # Query base
        query = TimeOffRequest.query

        # Verifica permessi
        if not user_has_permission(current_user.role, 'view_all_time_off_requests'):
            # L'utente può vedere solo le proprie richieste
            query = query.filter(TimeOffRequest.user_id == current_user.id)

        # Applica filtri
        if start_date:
            query = query.filter(TimeOffRequest.start_date >= datetime.strptime(start_date, '%Y-%m-%d').date())
        if end_date:
            query = query.filter(TimeOffRequest.end_date <= datetime.strptime(end_date, '%Y-%m-%d').date())
        if user_id:
            # Verifica permessi per vedere richieste di altri utenti
            if not user_has_permission(current_user.role, 'view_all_time_off_requests') and user_id != current_user.id:
                return api_response(False, 'Non puoi visualizzare richieste di altri utenti', status_code=403)
            query = query.filter(TimeOffRequest.user_id == user_id)
        if request_type:
            query = query.filter(TimeOffRequest.request_type == request_type)
        if status:
            query = query.filter(TimeOffRequest.status == status)

        # Conta totale
        total = query.count()

        # Applica paginazione e ordina per data di creazione
        requests = query.order_by(TimeOffRequest.submission_date.desc()).offset(offset).limit(limit).all()

        # Prepara dati
        requests_data = []
        for req in requests:
            requests_data.append({
                'id': req.id,
                'user_id': req.user_id,
                'user_name': f"{req.user.first_name} {req.user.last_name}" if req.user else None,
                'request_type': req.request_type,
                'start_date': req.start_date.isoformat(),
                'end_date': req.end_date.isoformat(),
                'duration_days': req.duration_days,
                'status': req.status,
                'notes': req.notes,
                'submission_date': req.submission_date.isoformat() if req.submission_date else None,
                'approval_date': req.approval_date.isoformat() if req.approval_date else None,
                'approved_by': req.approved_by,
                'approver_name': f"{req.approver.first_name} {req.approver.last_name}" if req.approver else None,
                'rejection_reason': req.rejection_reason,
                'can_be_approved': req.can_be_approved,
                'is_current': req.is_current,
                'created_at': req.created_at.isoformat() if req.created_at else None,
                'updated_at': req.updated_at.isoformat() if req.updated_at else None
            })

        return api_response(
            data=requests_data,
            message=f"Recuperate {len(requests_data)} richieste time-off",
            meta={
                'total': total,
                'limit': limit,
                'offset': offset,
                'has_more': offset + limit < total
            }
        )

    except Exception as e:
        return handle_api_error(e)

@api_time_off.route('/', methods=['POST'])
@csrf.exempt
@login_required
def create_time_off_request():
    """Crea una nuova richiesta di time-off."""
    try:
        data = request.get_json()

        # Validazione campi richiesti
        required_fields = ['request_type', 'start_date', 'end_date']
        for field in required_fields:
            if field not in data:
                return api_response(False, f'Campo {field} richiesto', status_code=400)

        # Validazione tipo richiesta
        valid_types = ['vacation', 'leave', 'smartworking']
        if data['request_type'] not in valid_types:
            return api_response(False, f'Tipo richiesta non valido. Valori ammessi: {", ".join(valid_types)}', status_code=400)

        # Parse date
        start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
        end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()

        # Validazione date
        if start_date > end_date:
            return api_response(False, 'La data di inizio non può essere successiva alla data di fine', status_code=400)

        if start_date < date.today():
            return api_response(False, 'Non puoi creare richieste per date passate', status_code=400)

        user_id = data.get('user_id', current_user.id)

        # Verifica permessi
        if user_id != current_user.id and not user_has_permission(current_user.role, 'manage_time_off_requests'):
            return api_response(False, 'Non puoi creare richieste per altri utenti', status_code=403)

        # Verifica sovrapposizioni con richieste esistenti approvate
        overlapping = TimeOffRequest.query.filter(
            TimeOffRequest.user_id == user_id,
            TimeOffRequest.status == 'approved',
            or_(
                and_(TimeOffRequest.start_date <= start_date, TimeOffRequest.end_date >= start_date),
                and_(TimeOffRequest.start_date <= end_date, TimeOffRequest.end_date >= end_date),
                and_(TimeOffRequest.start_date >= start_date, TimeOffRequest.end_date <= end_date)
            )
        ).first()

        if overlapping:
            return api_response(
                False, 
                f'Esiste già una richiesta approvata che si sovrappone al periodo richiesto ({overlapping.start_date} - {overlapping.end_date})', 
                status_code=400
            )

        # Crea richiesta
        time_off_request = TimeOffRequest(
            user_id=user_id,
            request_type=data['request_type'],
            start_date=start_date,
            end_date=end_date,
            notes=data.get('notes', ''),
            status='pending'
        )

        db.session.add(time_off_request)
        db.session.commit()

        return api_response(
            data={
                'id': time_off_request.id,
                'user_id': time_off_request.user_id,
                'request_type': time_off_request.request_type,
                'start_date': time_off_request.start_date.isoformat(),
                'end_date': time_off_request.end_date.isoformat(),
                'duration_days': time_off_request.duration_days,
                'status': time_off_request.status,
                'notes': time_off_request.notes
            },
            message='Richiesta time-off creata con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_time_off.route('/<int:request_id>', methods=['PUT'])
@csrf.exempt
@login_required
def update_time_off_request(request_id):
    """Aggiorna una richiesta di time-off (solo se in stato pending)."""
    try:
        time_off_request = TimeOffRequest.query.get_or_404(request_id)

        # Verifica permessi
        if time_off_request.user_id != current_user.id and not user_has_permission(current_user.role, 'manage_time_off_requests'):
            return api_response(False, 'Non puoi modificare richieste di altri utenti', status_code=403)

        # Verifica che sia modificabile
        if time_off_request.status != 'pending':
            return api_response(False, 'Puoi modificare solo richieste in stato pending', status_code=400)

        data = request.get_json()

        # Aggiorna campi
        if 'start_date' in data:
            start_date = datetime.strptime(data['start_date'], '%Y-%m-%d').date()
            if start_date < date.today():
                return api_response(False, 'Non puoi impostare date passate', status_code=400)
            time_off_request.start_date = start_date

        if 'end_date' in data:
            end_date = datetime.strptime(data['end_date'], '%Y-%m-%d').date()
            time_off_request.end_date = end_date

        if 'notes' in data:
            time_off_request.notes = data['notes']

        if 'request_type' in data:
            valid_types = ['vacation', 'leave', 'smartworking']
            if data['request_type'] not in valid_types:
                return api_response(False, f'Tipo richiesta non valido. Valori ammessi: {", ".join(valid_types)}', status_code=400)
            time_off_request.request_type = data['request_type']

        # Validazione date finali
        if time_off_request.start_date > time_off_request.end_date:
            return api_response(False, 'La data di inizio non può essere successiva alla data di fine', status_code=400)

        db.session.commit()

        return api_response(
            data={
                'id': time_off_request.id,
                'start_date': time_off_request.start_date.isoformat(),
                'end_date': time_off_request.end_date.isoformat(),
                'duration_days': time_off_request.duration_days,
                'notes': time_off_request.notes,
                'request_type': time_off_request.request_type
            },
            message='Richiesta aggiornata con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_time_off.route('/<int:request_id>', methods=['DELETE'])
@csrf.exempt
@login_required
def delete_time_off_request(request_id):
    """Elimina una richiesta di time-off (solo se in stato pending)."""
    try:
        time_off_request = TimeOffRequest.query.get_or_404(request_id)

        # Verifica permessi
        if time_off_request.user_id != current_user.id and not user_has_permission(current_user.role, 'manage_time_off_requests'):
            return api_response(False, 'Non puoi eliminare richieste di altri utenti', status_code=403)

        # Verifica che sia eliminabile
        if time_off_request.status != 'pending':
            return api_response(False, 'Puoi eliminare solo richieste in stato pending', status_code=400)

        db.session.delete(time_off_request)
        db.session.commit()

        return api_response(
            data={},
            message='Richiesta eliminata con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_time_off.route('/<int:request_id>/approve', methods=['POST'])
@csrf.exempt
@login_required
def approve_time_off_request(request_id):
    """Approva una richiesta di time-off."""
    try:
        time_off_request = TimeOffRequest.query.get_or_404(request_id)

        # Verifica permessi di approvazione
        if not user_has_permission(current_user.role, 'approve_time_off_requests'):
            return api_response(False, 'Non hai i permessi per approvare richieste time-off', status_code=403)

        # Verifica che possa essere approvata
        if not time_off_request.can_be_approved:
            return api_response(False, 'La richiesta non può essere approvata (deve essere in stato pending)', status_code=400)

        # Aggiorna stato
        time_off_request.status = 'approved'
        time_off_request.approval_date = datetime.utcnow()
        time_off_request.approved_by = current_user.id
        time_off_request.rejection_reason = None  # Reset eventuale motivo di rifiuto precedente

        db.session.commit()

        return api_response(
            data={
                'id': time_off_request.id,
                'status': time_off_request.status,
                'approval_date': time_off_request.approval_date.isoformat(),
                'approved_by': time_off_request.approved_by
            },
            message='Richiesta time-off approvata con successo'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_time_off.route('/<int:request_id>/reject', methods=['POST'])
@csrf.exempt
@login_required
def reject_time_off_request(request_id):
    """Rifiuta una richiesta di time-off."""
    try:
        time_off_request = TimeOffRequest.query.get_or_404(request_id)
        data = request.get_json()

        # Verifica permessi di approvazione
        if not user_has_permission(current_user.role, 'approve_time_off_requests'):
            return api_response(False, 'Non hai i permessi per rifiutare richieste time-off', status_code=403)

        # Verifica che possa essere rifiutata
        if not time_off_request.can_be_approved:
            return api_response(False, 'La richiesta non può essere rifiutata (deve essere in stato pending)', status_code=400)

        # Aggiorna stato
        time_off_request.status = 'rejected'
        time_off_request.approval_date = datetime.utcnow()
        time_off_request.approved_by = current_user.id
        time_off_request.rejection_reason = data.get('reason', 'Nessun motivo specificato')

        db.session.commit()

        return api_response(
            data={
                'id': time_off_request.id,
                'status': time_off_request.status,
                'approval_date': time_off_request.approval_date.isoformat(),
                'approved_by': time_off_request.approved_by,
                'rejection_reason': time_off_request.rejection_reason
            },
            message='Richiesta time-off rifiutata'
        )

    except Exception as e:
        db.session.rollback()
        return handle_api_error(e)

@api_time_off.route('/pending', methods=['GET'])
@csrf.exempt
@login_required
def get_pending_time_off_requests():
    """Recupera le richieste time-off in attesa di approvazione (per manager)."""
    try:
        # Verifica permessi
        if not user_has_permission(current_user.role, 'approve_time_off_requests'):
            return api_response(False, 'Non hai i permessi per visualizzare richieste in attesa', status_code=403)

        # Query richieste pending
        requests = TimeOffRequest.query.filter_by(status='pending').order_by(TimeOffRequest.submission_date.asc()).all()

        # Prepara dati
        requests_data = []
        for req in requests:
            requests_data.append({
                'id': req.id,
                'user_id': req.user_id,
                'user_name': f"{req.user.first_name} {req.user.last_name}" if req.user else None,
                'request_type': req.request_type,
                'start_date': req.start_date.isoformat(),
                'end_date': req.end_date.isoformat(),
                'duration_days': req.duration_days,
                'notes': req.notes,
                'submission_date': req.submission_date.isoformat() if req.submission_date else None,
                'created_at': req.created_at.isoformat() if req.created_at else None
            })

        return api_response(
            data=requests_data,
            message=f"Trovate {len(requests_data)} richieste in attesa di approvazione"
        )

    except Exception as e:
        return handle_api_error(e)
