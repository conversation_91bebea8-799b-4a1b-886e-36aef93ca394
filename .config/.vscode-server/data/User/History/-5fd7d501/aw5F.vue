<template>
  <div class="space-y-6">
    <!-- Header with AI Analysis -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Approvazioni Timesheet</h1>
          <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Gestisci approvazioni con assistenza AI e operazioni bulk
          </p>
        </div>
        <div class="flex items-center space-x-3">
          <!-- AI Anomaly Detection -->
          <button 
            @click="runAnomalyDetection" 
            :disabled="analyzingAnomalies"
            class="bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
            {{ analyzingAnomalies ? 'Analizzando...' : 'Rileva Anomalie AI' }}
          </button>
          
          <!-- Bulk Actions -->
          <div class="relative" v-if="selectedTimesheets.length">
            <button 
              @click="showBulkMenu = !showBulkMenu" 
              class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium"
            >
              Azioni Multiple ({{ selectedTimesheets.length }})
            </button>
            <div v-if="showBulkMenu" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-700 rounded-md shadow-lg z-10">
              <div class="py-1">
                <button 
                  @click="bulkApprove"
                  class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600"
                >
                  Approva Tutti
                </button>
                <button 
                  @click="bulkReject"
                  class="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-600"
                >
                  Rifiuta Tutti
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- AI Anomalies Alert -->
      <div v-if="anomalies.length" class="mt-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div class="flex items-start">
          <svg class="w-5 h-5 text-red-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
              Anomalie Rilevate ({{ anomalies.length }})
            </h3>
            <div class="mt-2 space-y-1">
              <div v-for="anomaly in anomalies" :key="anomaly.id" class="text-sm text-red-700 dark:text-red-300">
                • {{ anomaly.user_name }}: {{ anomaly.description }}
                <button 
                  @click="viewAnomalyDetails(anomaly)"
                  class="ml-2 text-red-600 dark:text-red-400 underline"
                >
                  Dettagli
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/20 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Da Approvare</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.pending }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/20 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Approvati</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.approved }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-red-100 dark:bg-red-900/20 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Con Anomalie</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ anomalies.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Ore Totali</p>
            <p class="text-2xl font-semibold text-gray-900 dark:text-white">{{ stats.totalHours }}h</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-4">
      <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Mese</label>
          <select v-model="selectedMonth" @change="loadTimesheets" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
            <option v-for="month in months" :key="month.value" :value="month.value">{{ month.label }}</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Stato</label>
          <select v-model="selectedStatus" @change="loadTimesheets" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
            <option value="">Tutti</option>
            <option value="submitted">Da Approvare</option>
            <option value="approved">Approvati</option>
            <option value="rejected">Rifiutati</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Membro Team</label>
          <select v-model="selectedMember" @change="loadTimesheets" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
            <option value="">Tutti</option>
            <option v-for="member in teamMembers" :key="member.id" :value="member.id">{{ member.full_name }}</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Anomalie</label>
          <select v-model="showOnlyAnomalies" @change="loadTimesheets" class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white">
            <option :value="false">Tutti</option>
            <option :value="true">Solo con Anomalie</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Cerca</label>
          <input 
            v-model="searchQuery" 
            @input="loadTimesheets"
            type="text" 
            placeholder="Nome utente..."
            class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
          >
        </div>
      </div>
    </div>

    <!-- Timesheet Table -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Timesheet da Approvare</h3>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                <input
                  type="checkbox"
                  @change="toggleSelectAll"
                  :checked="selectedTimesheets.length === timesheets.length && timesheets.length > 0"
                  class="rounded border-gray-300 dark:border-gray-600"
                >
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Dipendente
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Periodo
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Ore Totali
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Ore Fatturabili
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Stato
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Sottomesso
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Azioni
              </th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-if="loading">
              <td colspan="8" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                Caricamento...
              </td>
            </tr>
            <tr v-else-if="timesheets.length === 0">
              <td colspan="8" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                Nessun timesheet trovato
              </td>
            </tr>
            <tr v-else v-for="timesheet in timesheets" :key="timesheet.id"
                :class="{ 'bg-red-50 dark:bg-red-900/10': hasAnomaly(timesheet) }">
              <td class="px-6 py-4 whitespace-nowrap">
                <input
                  type="checkbox"
                  :value="timesheet.id"
                  v-model="selectedTimesheets"
                  class="rounded border-gray-300 dark:border-gray-600"
                >
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                      <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                        {{ getInitials(timesheet.user?.full_name) }}
                      </span>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                      {{ timesheet.user?.full_name }}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                      {{ timesheet.user?.email }}
                    </div>
                  </div>
                  <div v-if="hasAnomaly(timesheet)" class="ml-2">
                    <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ timesheet.month }}/{{ timesheet.year }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ timesheet.total_hours }}h
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                {{ timesheet.billable_hours }}h
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusClass(timesheet.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ getStatusText(timesheet.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ formatDate(timesheet.submission_date) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <button
                    @click="viewDetails(timesheet)"
                    class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    Dettagli
                  </button>
                  <button
                    v-if="timesheet.status === 'submitted'"
                    @click="approveTimesheet(timesheet)"
                    class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                  >
                    Approva
                  </button>
                  <button
                    v-if="timesheet.status === 'submitted'"
                    @click="rejectTimesheet(timesheet)"
                    class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                  >
                    Rifiuta
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Modals -->
    <div v-if="showDetailModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
              Dettagli Timesheet - {{ selectedTimesheet?.user?.full_name }}
            </h3>
            <button @click="showDetailModal = false" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Periodo</label>
                <p class="text-sm text-gray-900 dark:text-white">{{ selectedTimesheet?.month }}/{{ selectedTimesheet?.year }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Stato</label>
                <span :class="getStatusClass(selectedTimesheet?.status)" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ getStatusText(selectedTimesheet?.status) }}
                </span>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Ore Totali</label>
                <p class="text-sm text-gray-900 dark:text-white">{{ selectedTimesheet?.total_hours }}h</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Ore Fatturabili</label>
                <p class="text-sm text-gray-900 dark:text-white">{{ selectedTimesheet?.billable_hours }}h</p>
              </div>
            </div>

            <div v-if="selectedTimesheet?.status === 'submitted'" class="flex space-x-3">
              <button
                @click="approveTimesheet(selectedTimesheet)"
                class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Approva
              </button>
              <button
                @click="rejectTimesheet(selectedTimesheet)"
                class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Rifiuta
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Reject Modal -->
    <div v-if="showRejectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Rifiuta Timesheet
          </h3>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Motivo del rifiuto *
              </label>
              <textarea
                v-model="rejectionReason"
                rows="4"
                class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                placeholder="Specifica il motivo del rifiuto..."
              ></textarea>
            </div>

            <div class="flex space-x-3">
              <button
                @click="confirmReject"
                :disabled="!rejectionReason.trim()"
                class="bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Conferma Rifiuto
              </button>
              <button
                @click="showRejectModal = false"
                class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
              >
                Annulla
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// State
const timesheets = ref([])
const teamMembers = ref([])
const anomalies = ref([])
const selectedTimesheets = ref([])
const selectedTimesheet = ref(null)
const loading = ref(false)
const analyzingAnomalies = ref(false)
const showBulkMenu = ref(false)
const showDetailModal = ref(false)
const showRejectModal = ref(false)
const rejectionReason = ref('')

// Filters
const selectedMonth = ref(new Date().getMonth() + 1)
const selectedStatus = ref('submitted')
const selectedMember = ref('')
const showOnlyAnomalies = ref(false)
const searchQuery = ref('')

// Computed
const stats = computed(() => {
  // Ensure timesheets.value is an array
  const timesheetsArray = Array.isArray(timesheets.value) ? timesheets.value : []

  const pending = timesheetsArray.filter(t => t.status === 'submitted').length
  const approved = timesheetsArray.filter(t => t.status === 'approved').length
  const totalHours = timesheetsArray.reduce((sum, t) => sum + (t.total_hours || 0), 0)

  return { pending, approved, totalHours }
})

const months = computed(() => [
  { value: 1, label: 'Gennaio' },
  { value: 2, label: 'Febbraio' },
  { value: 3, label: 'Marzo' },
  { value: 4, label: 'Aprile' },
  { value: 5, label: 'Maggio' },
  { value: 6, label: 'Giugno' },
  { value: 7, label: 'Luglio' },
  { value: 8, label: 'Agosto' },
  { value: 9, label: 'Settembre' },
  { value: 10, label: 'Ottobre' },
  { value: 11, label: 'Novembre' },
  { value: 12, label: 'Dicembre' }
])

// Methods
const loadTimesheets = async () => {
  loading.value = true

  try {
    const params = new URLSearchParams({
      month: selectedMonth.value,
      year: new Date().getFullYear()
    })

    if (selectedStatus.value) params.append('status', selectedStatus.value)
    if (selectedMember.value) params.append('user_id', selectedMember.value)
    if (searchQuery.value) params.append('search', searchQuery.value)
    if (showOnlyAnomalies.value) params.append('anomalies_only', 'true')

    const response = await fetch(`/api/monthly-timesheets/?${params}`, {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      const result = await response.json()
      // Handle the correct API response structure
      if (result.data && Array.isArray(result.data.timesheets)) {
        timesheets.value = result.data.timesheets
      } else if (result.data && Array.isArray(result.data)) {
        timesheets.value = result.data
      } else if (result.data && Array.isArray(result.data.items)) {
        timesheets.value = result.data.items
      } else {
        timesheets.value = []
      }
      console.log('Loaded timesheets:', timesheets.value)
    } else {
      console.error('Failed to load timesheets:', response.status)
      timesheets.value = []
    }
  } catch (err) {
    console.error('Error loading timesheets:', err)
    timesheets.value = []
  } finally {
    loading.value = false
  }
}

const loadTeamMembers = async () => {
  try {
    const response = await fetch('/api/personnel/users', {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      const result = await response.json()
      // Handle different response formats
      if (result.data && Array.isArray(result.data.users)) {
        teamMembers.value = result.data.users
      } else if (result.data && Array.isArray(result.data)) {
        teamMembers.value = result.data
      } else {
        teamMembers.value = []
      }
      console.log('Loaded team members:', teamMembers.value)
    } else {
      console.error('Failed to load team members:', response.status)
      teamMembers.value = []
    }
  } catch (err) {
    console.error('Error loading team members:', err)
    teamMembers.value = []
  }
}

const runAnomalyDetection = async () => {
  analyzingAnomalies.value = true
  
  try {
    // Simulate AI analysis with timeout
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Mock anomalies for demonstration
    anomalies.value = [
      {
        id: 1,
        user_name: 'Mario Rossi',
        description: 'Ore eccessive nel weekend (16h sabato)',
        confidence: 95,
        type: 'weekend_overtime'
      },
      {
        id: 2,
        user_name: 'Giulia Bianchi',
        description: 'Pattern insolito: 12h consecutive senza pause',
        confidence: 87,
        type: 'unusual_pattern'
      }
    ]
  } catch (err) {
    console.error('Error running anomaly detection:', err)
  } finally {
    analyzingAnomalies.value = false
  }
}

const viewAnomalyDetails = (anomaly) => {
  // Show detailed anomaly information
  alert(`Anomalia: ${anomaly.description}\nConfidenza: ${anomaly.confidence}%`)
}

const bulkApprove = async () => {
  // Implement bulk approval
  console.log('Bulk approve:', selectedTimesheets.value)
  showBulkMenu.value = false
}

const bulkReject = async () => {
  // Implement bulk rejection
  console.log('Bulk reject:', selectedTimesheets.value)
  showBulkMenu.value = false
}

const toggleSelectAll = () => {
  if (selectedTimesheets.value.length === timesheets.value.length) {
    selectedTimesheets.value = []
  } else {
    selectedTimesheets.value = timesheets.value.map(t => t.id)
  }
}

const hasAnomaly = (timesheet) => {
  return anomalies.value.some(a => a.user_name === timesheet.user?.full_name)
}

const getInitials = (fullName) => {
  if (!fullName) return '??'
  return fullName.split(' ').map(n => n[0]).join('').toUpperCase()
}

const getStatusClass = (status) => {
  const classes = {
    'draft': 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300',
    'submitted': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    'approved': 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    'rejected': 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
  }
  return classes[status] || classes.draft
}

const getStatusText = (status) => {
  const texts = {
    'draft': 'Bozza',
    'submitted': 'Da Approvare',
    'approved': 'Approvato',
    'rejected': 'Rifiutato'
  }
  return texts[status] || 'Sconosciuto'
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  return date.toLocaleDateString('it-IT')
}

const viewDetails = (timesheet) => {
  selectedTimesheet.value = timesheet
  showDetailModal.value = true
}

const approveTimesheet = async (timesheet) => {
  try {
    const response = await fetch(`/api/monthly-timesheets/${timesheet.id}/approve`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      await loadTimesheets()
      showDetailModal.value = false
      // Show success notification
      alert('Timesheet approvato con successo!')
    } else {
      const error = await response.json()
      alert(`Errore: ${error.message}`)
    }
  } catch (err) {
    console.error('Error approving timesheet:', err)
    alert('Errore durante l\'approvazione')
  }
}

const rejectTimesheet = (timesheet) => {
  selectedTimesheet.value = timesheet
  rejectionReason.value = ''
  showDetailModal.value = false
  showRejectModal.value = true
}

const confirmReject = async () => {
  try {
    const response = await fetch(`/api/monthly-timesheets/${selectedTimesheet.value.id}/reject`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      },
      body: JSON.stringify({
        reason: rejectionReason.value
      })
    })

    if (response.ok) {
      await loadTimesheets()
      showRejectModal.value = false
      // Show success notification
      alert('Timesheet rifiutato')
    } else {
      const error = await response.json()
      alert(`Errore: ${error.message}`)
    }
  } catch (err) {
    console.error('Error rejecting timesheet:', err)
    alert('Errore durante il rifiuto')
  }
}

// Lifecycle
onMounted(() => {
  loadTimesheets()
  loadTeamMembers()
})
</script>
