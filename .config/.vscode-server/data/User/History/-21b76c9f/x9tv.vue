<template>
  <div class="space-y-6">
    <!-- <PERSON>rro<PERSON> -->
    <div v-if="error" class="bg-red-50 dark:bg-red-900/50 border border-red-200 dark:border-red-800 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm text-red-800 dark:text-red-200">{{ error }}</p>
        </div>
        <div class="ml-auto pl-3">
          <div class="-mx-1.5 -my-1.5">
            <button @click="clearError" class="inline-flex bg-red-50 dark:bg-red-900/50 rounded-md p-1.5 text-red-500 hover:bg-red-100 dark:hover:bg-red-900">
              <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Le Mie Ore</h1>
          <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Registra le tue ore di lavoro con la griglia mensile
          </p>
        </div>

        <!-- Controlli periodo -->
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <button
              @click="previousMonth"
              class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </button>

            <div class="text-center">
              <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ monthNames[currentMonth - 1] }} {{ currentYear }}
              </h2>
            </div>

            <button
              @click="nextMonth"
              class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </button>
          </div>

          <button
            @click="showAddHoursModal = true"
            class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Aggiungi Ore
          </button>
        </div>
      </div>
    </div>

    <!-- Griglia Timesheet Mensile -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            Griglia Ore - {{ monthNames[currentMonth - 1] }} {{ currentYear }}
          </h3>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            Totale: {{ formatHours(totalHours) }} | Fatturabili: {{ formatHours(billableHours) }}
          </div>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <!-- Header con giorni del mese -->
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th class="sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600">
                Progetto/Task
              </th>
              <th
                v-for="day in daysInMonth"
                :key="day"
                class="px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]"
                :class="{
                  'bg-blue-50 dark:bg-blue-900/20': isToday(day, currentMonth, currentYear),
                  'bg-red-50 dark:bg-red-900/20': isWeekend(day, currentMonth, currentYear)
                }"
              >
                <div>{{ day }}</div>
                <div class="text-xs text-gray-400">{{ getDayName(day, currentMonth, currentYear) }}</div>
              </th>
              <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600">
                Totale
              </th>
            </tr>
          </thead>

          <!-- Righe progetti/task -->
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-if="timesheetGridData.length === 0">
              <td :colspan="daysInMonth.length + 2" class="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                <div class="space-y-2">
                  <p>Nessun timesheet registrato per {{ getMonthName(currentMonth) }} {{ currentYear }}</p>
                  <button
                    @click="showAddProjectModal = true"
                    class="text-primary-600 hover:text-primary-700 dark:text-primary-400"
                  >
                    Registra le tue prime ore
                  </button>
                </div>
              </td>
            </tr>
            
            <tr v-for="taskRow in timesheetGridData" :key="taskRow.taskId">
              <!-- Colonna progetto/task -->
              <td class="sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600">
                <div class="flex items-center justify-between">
                  <div class="min-w-0 flex-1">
                    <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {{ taskRow.projectName }}
                    </p>
                    <p v-if="taskRow.taskName" class="text-xs text-gray-500 dark:text-gray-400 truncate">
                      {{ taskRow.taskName }}
                    </p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">
                      {{ taskRow.assignees }}
                    </p>
                  </div>
                </div>
              </td>
              
              <!-- Colonne giorni -->
              <td
                v-for="day in daysInMonth"
                :key="day"
                class="px-1 py-3 text-center"
                :class="{
                  'bg-blue-50 dark:bg-blue-900/20': isToday(day, currentMonth, currentYear),
                  'bg-red-50 dark:bg-red-900/20': isWeekend(day, currentMonth, currentYear)
                }"
              >
                <div v-if="taskRow.hours[getDateString(day)]" class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ taskRow.hours[getDateString(day)] }}
                </div>
                <div v-else class="text-gray-300 dark:text-gray-600">-</div>
              </td>
              
              <!-- Colonna totale riga -->
              <td class="px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white bg-gray-50 dark:bg-gray-700 border-l border-gray-200 dark:border-gray-600">
                {{ taskRow.total }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Ore Totali
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ formatHours(totalHours) }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Ore Fatturabili
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ formatHours(billableHours) }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                In Attesa
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ formatHours(pendingHours) }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Progetti Attivi
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ activeProjects }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal Aggiungi Progetto -->
    <div v-if="showAddProjectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
              Aggiungi Progetto alla Griglia
            </h3>
            <button @click="showAddProjectModal = false" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Progetto *
              </label>
              <select 
                v-model="newProjectTask.project_id" 
                class="w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                required
              >
                <option value="">Seleziona progetto...</option>
                <option v-for="project in availableProjects" :key="project.id" :value="project.id">
                  {{ project.name }}
                </option>
              </select>
            </div>

            <div>
              <label class="flex items-center">
                <input 
                  type="checkbox" 
                  v-model="newProjectTask.billable"
                  class="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Ore fatturabili</span>
              </label>
            </div>

            <div class="flex justify-end space-x-3">
              <button
                @click="addProject"
                :disabled="!newProjectTask.project_id || loading"
                class="bg-primary-600 hover:bg-primary-700 disabled:opacity-50 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                {{ loading ? 'Aggiunta...' : 'Aggiungi' }}
              </button>
              <button
                @click="showAddProjectModal = false"
                class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
              >
                Annulla
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useTimesheetStore } from '@/stores/timesheet'
import { 
  monthNames, 
  formatHours, 
  isToday, 
  isWeekend, 
  getDayName,
  validateHours
} from '@/utils/timesheet'

const timesheetStore = useTimesheetStore()

// Local state
const showAddProjectModal = ref(false)
const newProjectTask = ref({
  project_id: '',
  task_id: '',
  billable: true
})

// Computed properties from store
const currentYear = computed(() => timesheetStore.currentYear)
const currentMonth = computed(() => timesheetStore.currentMonth)
const projectTasks = computed(() => timesheetStore.projectTasks)
const monthlyEntries = computed(() => timesheetStore.monthlyEntries)
const availableProjects = computed(() => timesheetStore.availableProjects)
const loading = computed(() => timesheetStore.loading.monthlyData || timesheetStore.loading.saving)
const error = computed(() => timesheetStore.error)

// Computed calculations
const daysInMonth = computed(() => timesheetStore.daysInMonth)
const totalHours = computed(() => timesheetStore.totalHours)
const billableHours = computed(() => timesheetStore.billableHours)
const pendingHours = computed(() => timesheetStore.pendingHours)
const activeProjects = computed(() => timesheetStore.activeProjects)

// Build timesheet grid from existing entries like PersonnelProfile.vue
const timesheetGridData = computed(() => {
  const taskGroups = {}
  
  // Process timesheet entries from the store
  Object.entries(monthlyEntries.value).forEach(([date, dayEntries]) => {
    Object.entries(dayEntries).forEach(([taskKey, hours]) => {
      if (!taskGroups[taskKey]) {
        // Find project task info
        const projectTask = projectTasks.value.find(pt => pt.id === taskKey)
        taskGroups[taskKey] = {
          taskId: taskKey,
          taskName: projectTask?.task_name || 'Attività Generica',
          projectName: projectTask?.project_name || 'Progetto Sconosciuto', 
          assignees: 'Tu',
          hours: {},
          total: 0
        }
      }
      
      taskGroups[taskKey].hours[date] = parseFloat(hours || 0).toFixed(1)
      taskGroups[taskKey].total += parseFloat(hours || 0)
    })
  })
  
  // Format totals
  return Object.values(taskGroups).map(task => ({
    ...task,
    total: task.total.toFixed(1)
  }))
})

// Methods
const previousMonth = () => {
  timesheetStore.navigateMonth('previous')
}

const nextMonth = () => {
  timesheetStore.navigateMonth('next')
}

const clearError = () => {
  timesheetStore.clearError()
}

const updateEntry = async (projectTaskId, day, value) => {
  const hours = validateHours(value)
  if (hours === null) return
  
  await timesheetStore.saveEntry(projectTaskId, day, hours)
}

const addProject = async () => {
  if (!newProjectTask.value.project_id) return
  
  const success = await timesheetStore.addProjectToTimesheet(
    newProjectTask.value.project_id, 
    newProjectTask.value.task_id
  )
  
  if (success) {
    showAddProjectModal.value = false
    newProjectTask.value = {
      project_id: '',
      task_id: '',
      billable: true
    }
  }
}

const removeProjectTask = (projectTask) => {
  if (confirm('Rimuovere questo progetto dalla griglia? Le ore registrate non verranno eliminate.')) {
    // Remove from local projectTasks - this should be handled by the store
    const index = projectTasks.value.findIndex(pt => 
      pt.project_id === projectTask.project_id && pt.task_id === projectTask.task_id
    )
    if (index !== -1) {
      projectTasks.value.splice(index, 1)
    }
  }
}

const getEntryValue = (projectTaskId, day) => {
  const entryKey = `${currentYear.value}-${currentMonth.value}-${day}`
  return monthlyEntries.value[entryKey]?.[projectTaskId] || 0
}

const getRowTotal = (projectTask) => {
  return daysInMonth.value.reduce((sum, day) => {
    return sum + (getEntryValue(projectTask.id, day) || 0)
  }, 0)
}

const getDateString = (day) => {
  return `${currentYear.value}-${currentMonth.value.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
}

const getMonthName = (month) => {
  const names = [
    'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',
    'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'
  ]
  return names[month - 1]
}

// Lifecycle
onMounted(async () => {
  // Load data from store with caching
  await Promise.all([
    timesheetStore.loadAvailableProjects(),
    timesheetStore.loadMonthlyData()
  ])
})
</script>