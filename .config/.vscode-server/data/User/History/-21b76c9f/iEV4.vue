<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Le Mie Ore</h1>
          <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
            Registra le tue ore di lavoro con la griglia mensile
          </p>
        </div>

        <!-- Controlli periodo -->
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <button
              @click="previousMonth"
              class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
              </svg>
            </button>

            <div class="text-center">
              <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ monthNames[currentMonth - 1] }} {{ currentYear }}
              </h2>
            </div>

            <button
              @click="nextMonth"
              class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </button>
          </div>

          <button
            @click="showAddProjectModal = true"
            class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium"
          >
            Aggiungi Progetto
          </button>
        </div>
      </div>
    </div>

    <!-- Griglia Timesheet Mensile -->
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            Griglia Ore - {{ monthNames[currentMonth - 1] }} {{ currentYear }}
          </h3>
          <div class="flex items-center space-x-4">
            <div class="text-sm text-gray-500 dark:text-gray-400">
              Totale: {{ formatHours(totalHours) }} | Fatturabili: {{ formatHours(billableHours) }}
            </div>
            <!-- Debug button - remove in production -->
            <button
              @click="autoPopulateProjectTasks"
              class="text-xs bg-red-500 text-white px-2 py-1 rounded"
              v-if="projectTasks.length === 0 && timesheets.length > 0"
            >
              DEBUG: Popola Griglia ({{ timesheets.length }} ore trovate)
            </button>
          </div>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <!-- Header con giorni del mese -->
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th class="sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600">
                Progetto/Task
              </th>
              <th
                v-for="day in daysInMonth"
                :key="day"
                class="px-2 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider min-w-[50px]"
                :class="{
                  'bg-blue-50 dark:bg-blue-900/20': isToday(day),
                  'bg-red-50 dark:bg-red-900/20': isWeekend(day)
                }"
              >
                <div>{{ day }}</div>
                <div class="text-xs text-gray-400">{{ getDayName(day) }}</div>
              </th>
              <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-l border-gray-200 dark:border-gray-600">
                Totale
              </th>
            </tr>
          </thead>

          <!-- Righe progetti/task -->
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-if="projectTasks.length === 0">
              <td :colspan="daysInMonth.length + 2" class="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                <div class="space-y-2">
                  <p>Nessun progetto configurato</p>
                  <button
                    @click="showAddProjectModal = true"
                    class="text-primary-600 hover:text-primary-700 dark:text-primary-400"
                  >
                    Aggiungi il tuo primo progetto
                  </button>
                </div>
              </td>
            </tr>

            <tr v-for="projectTask in projectTasks" :key="`${projectTask.project_id}-${projectTask.task_id || 'no-task'}`">
              <!-- Colonna progetto/task -->
              <td class="sticky left-0 z-10 bg-white dark:bg-gray-800 px-4 py-3 border-r border-gray-200 dark:border-gray-600">
                <div class="text-sm font-medium text-gray-900 dark:text-white">
                  {{ projectTask.project_name }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  {{ projectTask.task_name || 'Nessun task specifico' }}
                </div>
                <div class="flex items-center mt-1 space-x-2">
                  <span
                    class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium"
                    :class="projectTask.billable ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'"
                  >
                    {{ projectTask.billable ? 'Fatt.' : 'Int.' }}
                  </span>
                  <button
                    @click="removeProjectTask(projectTask)"
                    class="text-red-400 hover:text-red-600 dark:hover:text-red-300"
                    title="Rimuovi dalla griglia"
                  >
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </td>

              <!-- Celle ore per ogni giorno -->
              <td
                v-for="day in daysInMonth"
                :key="day"
                class="px-1 py-2 text-center"
                :class="{
                  'bg-blue-50 dark:bg-blue-900/20': isToday(day),
                  'bg-red-50 dark:bg-red-900/20': isWeekend(day)
                }"
              >
                <input
                  type="number"
                  step="0.5"
                  min="0"
                  max="24"
                  :value="getHoursForDay(projectTask, day)"
                  @input="updateHours(projectTask, day, $event.target.value)"
                  @blur="saveEntry(projectTask, day)"
                  class="w-12 h-8 text-xs text-center border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:border-primary-500 focus:ring-1 focus:ring-primary-500"
                  :class="{
                    'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-300 dark:border-yellow-600': hasUnsavedChanges(projectTask, day),
                    'bg-green-50 dark:bg-green-900/20 border-green-300 dark:border-green-600': getHoursForDay(projectTask, day) > 0
                  }"
                />
              </td>

              <!-- Totale riga -->
              <td class="px-4 py-3 text-center text-sm font-medium text-gray-900 dark:text-white border-l border-gray-200 dark:border-gray-600">
                {{ formatHours(getRowTotal(projectTask)) }}
              </td>
            </tr>

            <!-- Riga totali giornalieri -->
            <tr class="bg-gray-50 dark:bg-gray-700 font-medium">
              <td class="sticky left-0 z-10 bg-gray-50 dark:bg-gray-700 px-4 py-3 text-sm text-gray-900 dark:text-white border-r border-gray-200 dark:border-gray-600">
                Totale Giornaliero
              </td>
              <td
                v-for="day in daysInMonth"
                :key="day"
                class="px-2 py-3 text-center text-sm text-gray-900 dark:text-white"
                :class="{
                  'bg-blue-100 dark:bg-blue-800': isToday(day),
                  'bg-red-100 dark:bg-red-800': isWeekend(day)
                }"
              >
                {{ formatHours(getDayTotal(day)) }}
              </td>
              <td class="px-4 py-3 text-center text-sm font-bold text-gray-900 dark:text-white border-l border-gray-200 dark:border-gray-600">
                {{ formatHours(totalHours) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Statistiche -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Ore Totali
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ formatHours(totalHours) }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Ore Fatturabili
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ formatHours(billableHours) }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                In Attesa
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ formatHours(pendingHours) }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
      
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                Progetti Attivi
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {{ activeProjects }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal Aggiungi Progetto -->
    <div v-if="showAddProjectModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white">
              Aggiungi Progetto alla Griglia
            </h3>
            <button @click="showAddProjectModal = false" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Progetto *
              </label>
              <select
                v-model="newProjectTask.project_id"
                @change="loadTasksForProject"
                class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">Seleziona progetto</option>
                <option v-for="project in projects" :key="project.id" :value="project.id">
                  {{ project.name }}
                </option>
              </select>
            </div>

            <div v-if="newProjectTask.project_id">
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Task (opzionale)
              </label>
              <select
                v-model="newProjectTask.task_id"
                class="w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              >
                <option value="">Nessun task specifico</option>
                <option v-for="task in availableTasks" :key="task.id" :value="task.id">
                  {{ task.name }}
                </option>
              </select>
            </div>

            <div>
              <label class="flex items-center">
                <input
                  type="checkbox"
                  v-model="newProjectTask.billable"
                  class="rounded border-gray-300 dark:border-gray-600"
                >
                <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Ore fatturabili</span>
              </label>
            </div>

            <div class="flex space-x-3">
              <button
                @click="addProjectToGrid"
                :disabled="!newProjectTask.project_id"
                class="bg-primary-600 hover:bg-primary-700 disabled:bg-primary-400 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Aggiungi alla Griglia
              </button>
              <button
                @click="showAddProjectModal = false"
                class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md text-sm font-medium"
              >
                Annulla
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

// State
const timesheets = ref([])
const projects = ref([])
const tasks = ref([])
const projectTasks = ref([])
const availableTasks = ref([])
const loading = ref(false)
const showAddProjectModal = ref(false)
const unsavedChanges = ref(new Map())

// Date navigation
const currentYear = ref(new Date().getFullYear())
const currentMonth = ref(new Date().getMonth() + 1)

// New project/task form
const newProjectTask = ref({
  project_id: '',
  task_id: '',
  billable: true
})

// Constants
const monthNames = [
  'Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',
  'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'
]

// Computed
const daysInMonth = computed(() => {
  const year = currentYear.value
  const month = currentMonth.value
  const daysCount = new Date(year, month, 0).getDate()
  return Array.from({ length: daysCount }, (_, i) => i + 1)
})

const totalHours = computed(() => {
  return timesheets.value.reduce((sum, entry) => sum + entry.hours, 0)
})

const billableHours = computed(() => {
  return timesheets.value.reduce((sum, entry) => sum + (entry.billable ? entry.hours : 0), 0)
})

const pendingHours = computed(() => {
  return timesheets.value.reduce((sum, entry) => sum + (entry.status === 'pending' ? entry.hours : 0), 0)
})

const activeProjects = computed(() => {
  return projectTasks.value.length
})

// Methods
const loadProjects = async () => {
  try {
    const response = await fetch('/api/projects/', {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      const result = await response.json()
      projects.value = result.data || []
    }
  } catch (err) {
    console.error('Error loading projects:', err)
  }
}

const loadTasks = async () => {
  try {
    const response = await fetch('/api/tasks/', {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      const result = await response.json()
      tasks.value = result.data || []
    }
  } catch (err) {
    console.error('Error loading tasks:', err)
  }
}

const loadTimesheets = async () => {
  loading.value = true

  try {
    // Calculate proper end date for the month
    const year = currentYear.value
    const month = currentMonth.value
    const lastDay = new Date(year, month, 0).getDate()

    const params = new URLSearchParams({
      start_date: `${year}-${String(month).padStart(2, '0')}-01`,
      end_date: `${year}-${String(month).padStart(2, '0')}-${String(lastDay).padStart(2, '0')}`
    })

    const response = await fetch(`/api/timesheets/?${params}`, {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': authStore.csrfToken
      }
    })

    if (response.ok) {
      const result = await response.json()
      timesheets.value = result.data || []

      // Auto-populate projectTasks from existing timesheet entries
      autoPopulateProjectTasks()
    }
  } catch (err) {
    console.error('Error loading timesheets:', err)
  } finally {
    loading.value = false
  }
}

const previousMonth = () => {
  if (currentMonth.value === 1) {
    currentMonth.value = 12
    currentYear.value--
  } else {
    currentMonth.value--
  }
  loadTimesheets()
}

const nextMonth = () => {
  if (currentMonth.value === 12) {
    currentMonth.value = 1
    currentYear.value++
  } else {
    currentMonth.value++
  }
  loadTimesheets()
}

// Auto-populate projectTasks from existing timesheet entries
const autoPopulateProjectTasks = () => {
  console.log('Auto-populating project tasks from', timesheets.value.length, 'timesheet entries')

  const existingProjectTasks = new Set()

  // Extract unique project/task combinations from timesheets
  timesheets.value.forEach(entry => {
    const key = `${entry.project_id}-${entry.task_id || 'no-task'}`
    if (!existingProjectTasks.has(key)) {
      existingProjectTasks.add(key)

      // Check if this project/task is already in the grid
      const alreadyExists = projectTasks.value.some(pt =>
        pt.project_id === entry.project_id &&
        pt.task_id === entry.task_id
      )

      if (!alreadyExists) {
        // Find project name from projects list
        const project = projects.value.find(p => p.id === entry.project_id)
        const projectName = project?.name || entry.project?.name || `Progetto ${entry.project_id}`

        // Find task name from tasks list
        const task = tasks.value.find(t => t.id === entry.task_id)
        const taskName = task?.name || entry.task?.name || null

        const newProjectTask = {
          project_id: entry.project_id,
          task_id: entry.task_id,
          project_name: projectName,
          task_name: taskName,
          billable: entry.billable || true
        }

        console.log('Adding project task to grid:', newProjectTask)
        projectTasks.value.push(newProjectTask)
      }
    }
  })

  console.log('Final projectTasks:', projectTasks.value)
}

// Grid helper methods
const isToday = (day) => {
  const today = new Date()
  return today.getDate() === day &&
         today.getMonth() + 1 === currentMonth.value &&
         today.getFullYear() === currentYear.value
}

const isWeekend = (day) => {
  const date = new Date(currentYear.value, currentMonth.value - 1, day)
  const dayOfWeek = date.getDay()
  return dayOfWeek === 0 || dayOfWeek === 6 // Sunday or Saturday
}

const getDayName = (day) => {
  const date = new Date(currentYear.value, currentMonth.value - 1, day)
  return date.toLocaleDateString('it-IT', { weekday: 'short' }).toUpperCase()
}

const getHoursForDay = (projectTask, day) => {
  const dateStr = `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-${String(day).padStart(2, '0')}`
  const entry = timesheets.value.find(t => {
    const projectMatch = t.project_id === projectTask.project_id
    const taskMatch = (t.task_id === projectTask.task_id) ||
                     (t.task_id === null && projectTask.task_id === null) ||
                     (t.task_id === undefined && projectTask.task_id === null)
    const dateMatch = t.date === dateStr

    return projectMatch && taskMatch && dateMatch
  })
  return entry ? entry.hours : 0
}

const updateHours = (projectTask, day, value) => {
  const key = `${projectTask.project_id}-${projectTask.task_id || 'no-task'}-${day}`
  if (value === '' || value === '0') {
    unsavedChanges.value.delete(key)
  } else {
    unsavedChanges.value.set(key, {
      projectTask,
      day,
      hours: parseFloat(value) || 0
    })
  }
}

const hasUnsavedChanges = (projectTask, day) => {
  const key = `${projectTask.project_id}-${projectTask.task_id || 'no-task'}-${day}`
  return unsavedChanges.value.has(key)
}

const saveEntry = async (projectTask, day) => {
  const key = `${projectTask.project_id}-${projectTask.task_id || 'no-task'}-${day}`
  const change = unsavedChanges.value.get(key)

  if (!change) return

  const dateStr = `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}-${String(day).padStart(2, '0')}`

  try {
    // Find existing entry
    const existingEntry = timesheets.value.find(t =>
      t.project_id === projectTask.project_id &&
      t.task_id === projectTask.task_id &&
      t.date === dateStr
    )

    if (change.hours === 0) {
      // Delete entry if hours is 0
      if (existingEntry) {
        await deleteTimesheetEntry(existingEntry.id)
      }
    } else {
      // Create or update entry
      const entryData = {
        project_id: projectTask.project_id,
        task_id: projectTask.task_id || null,
        date: dateStr,
        hours: change.hours,
        billable: projectTask.billable,
        description: ''
      }

      if (existingEntry) {
        await updateTimesheetEntry(existingEntry.id, entryData)
      } else {
        await createTimesheetEntry(entryData)
      }
    }

    unsavedChanges.value.delete(key)
    await loadTimesheets()
  } catch (err) {
    console.error('Error saving entry:', err)
    alert('Errore nel salvare le ore. Riprova.')
  }
}

const createTimesheetEntry = async (data) => {
  const response = await fetch('/api/timesheets/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRFToken': authStore.csrfToken
    },
    body: JSON.stringify(data)
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.message || 'Errore nella creazione')
  }
}

const updateTimesheetEntry = async (id, data) => {
  const response = await fetch(`/api/timesheets/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRFToken': authStore.csrfToken
    },
    body: JSON.stringify(data)
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.message || 'Errore nell\'aggiornamento')
  }
}

const deleteTimesheetEntry = async (id) => {
  const response = await fetch(`/api/timesheets/${id}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRFToken': authStore.csrfToken
    }
  })

  if (!response.ok) {
    const error = await response.json()
    throw new Error(error.message || 'Errore nell\'eliminazione')
  }
}

const getRowTotal = (projectTask) => {
  return daysInMonth.value.reduce((sum, day) => {
    return sum + getHoursForDay(projectTask, day)
  }, 0)
}

const getDayTotal = (day) => {
  return projectTasks.value.reduce((sum, projectTask) => {
    return sum + getHoursForDay(projectTask, day)
  }, 0)
}

// Project/Task management
const loadTasksForProject = async () => {
  if (!newProjectTask.value.project_id) {
    availableTasks.value = []
    return
  }

  availableTasks.value = tasks.value.filter(task =>
    task.project_id === parseInt(newProjectTask.value.project_id)
  )
}

const addProjectToGrid = () => {
  const project = projects.value.find(p => p.id === parseInt(newProjectTask.value.project_id))
  const task = newProjectTask.value.task_id ?
    availableTasks.value.find(t => t.id === parseInt(newProjectTask.value.task_id)) : null

  const projectTaskKey = `${newProjectTask.value.project_id}-${newProjectTask.value.task_id || 'no-task'}`

  // Check if already exists
  if (projectTasks.value.some(pt =>
    pt.project_id === parseInt(newProjectTask.value.project_id) &&
    pt.task_id === (newProjectTask.value.task_id ? parseInt(newProjectTask.value.task_id) : null)
  )) {
    alert('Questo progetto/task è già presente nella griglia')
    return
  }

  projectTasks.value.push({
    project_id: parseInt(newProjectTask.value.project_id),
    task_id: newProjectTask.value.task_id ? parseInt(newProjectTask.value.task_id) : null,
    project_name: project.name,
    task_name: task?.name || null,
    billable: newProjectTask.value.billable
  })

  // Reset form
  newProjectTask.value = {
    project_id: '',
    task_id: '',
    billable: true
  }
  availableTasks.value = []
  showAddProjectModal.value = false
}

const removeProjectTask = (projectTask) => {
  if (confirm('Rimuovere questo progetto/task dalla griglia? Le ore registrate non verranno eliminate.')) {
    const index = projectTasks.value.findIndex(pt =>
      pt.project_id === projectTask.project_id && pt.task_id === projectTask.task_id
    )
    if (index !== -1) {
      projectTasks.value.splice(index, 1)
    }
  }
}

const formatHours = (hours) => {
  if (!hours || hours === 0) return '0h'
  return hours % 1 === 0 ? `${hours}h` : `${hours.toFixed(1)}h`
}

// Lifecycle
onMounted(async () => {
  // Load projects and tasks first, then timesheets
  await Promise.all([
    loadProjects(),
    loadTasks()
  ])
  // Now load timesheets and auto-populate grid
  await loadTimesheets()
})

// Watchers
watch([currentMonth, currentYear], () => {
  loadTimesheets()
})
</script>
