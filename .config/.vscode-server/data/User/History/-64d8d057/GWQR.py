# utils/permissions.py

# Definizioni dei Ruoli
ROLE_ADMIN = 'admin'
ROLE_MANAGER = 'manager'
ROLE_EMPLOYEE = 'employee'
ROLE_SALES = 'sales'
ROLE_HR = 'human_resources'

# Set di tutti i ruoli validi (utile per validazione o dropdown)
ALL_ROLES = {
    ROLE_ADMIN,
    ROLE_MANAGER,
    ROLE_EMPLOYEE,
    ROLE_SALES,
    ROLE_HR,
}

# Definizioni dei Permessi (esempi, da espandere)
PERMISSION_VIEW_DASHBOARD = 'view_dashboard'
PERMISSION_ADMIN = 'admin'  # Permesso amministrativo generale
PERMISSION_MANAGE_USERS = 'manage_users' # Creare, modificare, attivare/disattivare utenti
PERMISSION_ASSIGN_ROLES = 'assign_roles'

PERMISSION_VIEW_ALL_PROJECTS = 'view_all_projects'
PERMISSION_CREATE_PROJECT = 'create_project'
PERMISSION_EDIT_PROJECT = 'edit_project' # Modificare dettagli, budget
PERMISSION_DELETE_PROJECT = 'delete_project'
PERMISSION_ASSIGN_TO_PROJECT = 'assign_to_project'
PERMISSION_MANAGE_PROJECT_TASKS = 'manage_project_tasks' # Creare/modificare/eliminare task di progetto
PERMISSION_MANAGE_PROJECT_RESOURCES = 'manage_project_resources' # Gestire le risorse assegnate ai progetti

PERMISSION_VIEW_OWN_TIMESHEETS = 'view_own_timesheets'
PERMISSION_SUBMIT_TIMESHEET = 'submit_timesheet'
PERMISSION_APPROVE_TIMESHEETS = 'approve_timesheets' # Tipicamente per Manager

# Nuovi permessi per il sistema timesheet (Task 3.1)
PERMISSION_VIEW_ALL_TIMESHEETS = 'view_all_timesheets'  # Manager, Admin, HR
PERMISSION_MANAGE_TIMESHEETS = 'manage_timesheets'  # Admin, Manager (per altri utenti)
PERMISSION_VIEW_ALL_TIME_OFF_REQUESTS = 'view_all_time_off_requests'  # Manager, Admin, HR
PERMISSION_MANAGE_TIME_OFF_REQUESTS = 'manage_time_off_requests'  # Admin, Manager (per altri utenti)
PERMISSION_APPROVE_TIME_OFF_REQUESTS = 'approve_time_off_requests'  # Manager, Admin, HR

PERMISSION_VIEW_PERSONNEL_DATA = 'view_personnel_data' # HR e forse Manager per i propri team
PERMISSION_EDIT_PERSONNEL_DATA = 'edit_personnel_data' # HR
PERMISSION_VIEW_CONTRACTS = 'view_contracts' # HR
PERMISSION_MANAGE_CONTRACTS = 'manage_contracts' # HR

PERMISSION_VIEW_CRM = 'view_crm' # Sales, Manager
PERMISSION_MANAGE_CLIENTS = 'manage_clients' # Sales, Manager, Admin
PERMISSION_MANAGE_PROPOSALS = 'manage_proposals' # Sales, Manager, Admin

PERMISSION_VIEW_REPORTS = 'view_reports' # Manager, Admin, HR (specifici)

PERMISSION_VIEW_FUNDING = 'view_funding' # Admin, Manager, HR, Sales
PERMISSION_MANAGE_FUNDING = 'manage_funding' # Admin, Manager, HR

PERMISSION_VIEW_PRODUCTS = 'view_products' # Tutti i loggati, Sales, Manager, Admin
PERMISSION_MANAGE_PRODUCTS = 'manage_products' # Sales, Manager, Admin
PERMISSION_VIEW_PERFORMANCE = 'view_performance' # Tutti i loggati, Manager, Admin, HR
PERMISSION_MANAGE_PERFORMANCE = 'manage_performance' # Manager, Admin, HR
PERMISSION_VIEW_COMMUNICATIONS = 'view_communications' # Tutti i loggati, Manager, Admin, HR
PERMISSION_MANAGE_COMMUNICATIONS = 'manage_communications' # Manager, Admin, HR
PERMISSION_VIEW_STARTUP = 'view_startup' # Tutti i loggati, Manager, Admin, HR
PERMISSION_MANAGE_STARTUP = 'manage_startup' # Manager, Admin, HR

# Mappatura Ruoli -> Permessi
ROLE_PERMISSIONS = {
    ROLE_ADMIN: {
        PERMISSION_ADMIN,
        PERMISSION_MANAGE_USERS,
        PERMISSION_ASSIGN_ROLES,
        PERMISSION_VIEW_ALL_PROJECTS,
        PERMISSION_CREATE_PROJECT,
        PERMISSION_EDIT_PROJECT,
        PERMISSION_DELETE_PROJECT,
        PERMISSION_ASSIGN_TO_PROJECT,
        PERMISSION_MANAGE_PROJECT_TASKS,
        PERMISSION_MANAGE_PROJECT_RESOURCES,
        PERMISSION_APPROVE_TIMESHEETS,
        PERMISSION_VIEW_ALL_TIMESHEETS,
        PERMISSION_MANAGE_TIMESHEETS,
        PERMISSION_VIEW_ALL_TIME_OFF_REQUESTS,
        PERMISSION_MANAGE_TIME_OFF_REQUESTS,
        PERMISSION_APPROVE_TIME_OFF_REQUESTS,
        PERMISSION_VIEW_PERSONNEL_DATA,
        PERMISSION_EDIT_PERSONNEL_DATA,
        PERMISSION_VIEW_CONTRACTS,
        PERMISSION_MANAGE_CONTRACTS,
        PERMISSION_VIEW_CRM,
        PERMISSION_MANAGE_CLIENTS,
        PERMISSION_MANAGE_PROPOSALS,
        PERMISSION_VIEW_REPORTS,
        PERMISSION_VIEW_DASHBOARD,
        PERMISSION_SUBMIT_TIMESHEET,
        PERMISSION_VIEW_OWN_TIMESHEETS,
        PERMISSION_VIEW_FUNDING,
        PERMISSION_MANAGE_FUNDING,
        PERMISSION_VIEW_PRODUCTS,
        PERMISSION_MANAGE_PRODUCTS,
        PERMISSION_VIEW_PERFORMANCE,
        PERMISSION_MANAGE_PERFORMANCE,
        PERMISSION_VIEW_COMMUNICATIONS,
        PERMISSION_MANAGE_COMMUNICATIONS,
        PERMISSION_VIEW_STARTUP,
        PERMISSION_MANAGE_STARTUP,
    },
    ROLE_MANAGER: {
        PERMISSION_VIEW_DASHBOARD,
        PERMISSION_VIEW_ALL_PROJECTS,
        PERMISSION_EDIT_PROJECT,
        PERMISSION_ASSIGN_TO_PROJECT,
        PERMISSION_MANAGE_PROJECT_TASKS,
        PERMISSION_MANAGE_PROJECT_RESOURCES,
        PERMISSION_APPROVE_TIMESHEETS,
        PERMISSION_VIEW_PERSONNEL_DATA,
        PERMISSION_VIEW_CRM,
        PERMISSION_VIEW_REPORTS,
        PERMISSION_SUBMIT_TIMESHEET,
        PERMISSION_VIEW_OWN_TIMESHEETS,
        PERMISSION_MANAGE_CLIENTS,
        PERMISSION_MANAGE_PROPOSALS,
        PERMISSION_VIEW_FUNDING,
        PERMISSION_MANAGE_FUNDING,
        PERMISSION_VIEW_PRODUCTS,
        PERMISSION_MANAGE_PRODUCTS,
        PERMISSION_VIEW_PERFORMANCE,
        PERMISSION_MANAGE_PERFORMANCE,
        PERMISSION_VIEW_COMMUNICATIONS,
        PERMISSION_MANAGE_COMMUNICATIONS,
        PERMISSION_VIEW_STARTUP,
        PERMISSION_MANAGE_STARTUP,
    },
    ROLE_EMPLOYEE: {
        PERMISSION_VIEW_DASHBOARD,
        PERMISSION_VIEW_OWN_TIMESHEETS,
        PERMISSION_SUBMIT_TIMESHEET,
    },
    ROLE_SALES: {
        PERMISSION_VIEW_DASHBOARD,
        PERMISSION_VIEW_CRM,
        PERMISSION_MANAGE_CLIENTS,
        PERMISSION_MANAGE_PROPOSALS,
        PERMISSION_SUBMIT_TIMESHEET,
        PERMISSION_VIEW_OWN_TIMESHEETS,
        PERMISSION_VIEW_REPORTS,
        PERMISSION_VIEW_FUNDING,
        PERMISSION_VIEW_PRODUCTS,
        PERMISSION_MANAGE_PRODUCTS,
    },
    ROLE_HR: {
        PERMISSION_VIEW_DASHBOARD,
        PERMISSION_MANAGE_USERS,
        PERMISSION_VIEW_PERSONNEL_DATA,
        PERMISSION_EDIT_PERSONNEL_DATA,
        PERMISSION_VIEW_CONTRACTS,
        PERMISSION_MANAGE_CONTRACTS,
        PERMISSION_SUBMIT_TIMESHEET,
        PERMISSION_VIEW_OWN_TIMESHEETS,
        PERMISSION_VIEW_REPORTS,
        PERMISSION_VIEW_FUNDING,
        PERMISSION_MANAGE_FUNDING,
        PERMISSION_VIEW_PERFORMANCE,
        PERMISSION_MANAGE_PERFORMANCE,
        PERMISSION_VIEW_COMMUNICATIONS,
        PERMISSION_MANAGE_COMMUNICATIONS,
        PERMISSION_VIEW_STARTUP,
        PERMISSION_MANAGE_STARTUP,
    }
}

ALL_PERMISSIONS = set()
for role in ROLE_PERMISSIONS:
    ALL_PERMISSIONS.update(ROLE_PERMISSIONS[role])

def user_has_permission(user_role, permission_name):
    if not user_role or user_role not in ROLE_PERMISSIONS:
        return False
    if ROLE_ADMIN == user_role:
        return True
    return permission_name in ROLE_PERMISSIONS.get(user_role, set())

def require_permissions(*permissions):
    """Decorator per richiedere permessi specifici."""
    from functools import wraps
    from flask import jsonify, session
    
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Verifica se l'utente è autenticato tramite sessione Flask
            if 'user_id' not in session:
                return jsonify({
                    'success': False,
                    'message': 'Autenticazione richiesta'
                }), 401
            
            # Importa User solo quando necessario per evitare importazioni circolari
            from models import User
            user = User.query.get(session['user_id'])
            
            if not user or not user.is_active:
                return jsonify({
                    'success': False,
                    'message': 'Utente non trovato o inattivo'
                }), 401
            
            user_permissions = ROLE_PERMISSIONS.get(user.role, set())
            
            for permission in permissions:
                if permission not in user_permissions:
                    return jsonify({
                        'success': False,
                        'message': 'Permessi insufficienti'
                    }), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator