import { defineStore } from 'pinia'
import { useAuthStore } from './auth'

export const useTimesheetStore = defineStore('timesheet', {
  state: () => ({
    // Dashboard stats
    stats: {
      weeklyHours: 0,
      monthlyHours: 0,
      pendingApprovals: 0,
      efficiency: 0
    },
    
    // Recent activities
    recentActivities: [],
    
    // Pending approvals (for managers)
    pendingApprovals: [],
    
    // User's timesheet status
    myStatus: {
      status: 'draft',
      totalHours: 0,
      billableHours: 0
    },
    
    // Monthly timesheet data
    currentMonth: new Date().getMonth() + 1,
    currentYear: new Date().getFullYear(),
    projectTasks: [],
    monthlyEntries: {},
    
    // Available projects for timesheet
    availableProjects: [],
    
    // Loading states
    loading: {
      dashboard: false,
      monthlyData: false,
      saving: false
    },
    
    // Error states
    error: null,
    
    // Cache timestamps
    lastFetch: {
      dashboard: null,
      monthlyData: null,
      projects: null
    }
  }),

  getters: {
    // Monthly calculations
    totalHours: (state) => {
      return Object.values(state.monthlyEntries).reduce((total, dayEntries) => {
        return total + Object.values(dayEntries).reduce((dayTotal, hours) => dayTotal + (hours || 0), 0)
      }, 0)
    },
    
    billableHours: (state) => {
      // Calculate based on project billable flags
      return Object.values(state.monthlyEntries).reduce((total, dayEntries) => {
        return total + Object.values(dayEntries).reduce((dayTotal, hours, index) => {
          const project = state.projectTasks[Math.floor(index / state.daysInMonth?.length || 31)]
          return dayTotal + (project?.billable ? (hours || 0) : 0)
        }, 0)
      }, 0)
    },
    
    pendingHours: (state) => {
      return state.myStatus.status === 'submitted' ? state.myStatus.totalHours : 0
    },
    
    activeProjects: (state) => {
      return state.projectTasks.length
    },
    
    daysInMonth: (state) => {
      const days = new Date(state.currentYear, state.currentMonth, 0).getDate()
      return Array.from({ length: days }, (_, i) => i + 1)
    },
    
    canApprove: () => {
      const authStore = useAuthStore()
      return authStore.user?.role === 'manager' || authStore.user?.role === 'admin'
    },
    
    // Check if data needs refresh (5 minutes cache)
    needsRefresh: (state) => {
      const fiveMinutes = 5 * 60 * 1000
      return {
        dashboard: !state.lastFetch.dashboard || Date.now() - state.lastFetch.dashboard > fiveMinutes,
        monthlyData: !state.lastFetch.monthlyData || Date.now() - state.lastFetch.monthlyData > fiveMinutes,
        projects: !state.lastFetch.projects || Date.now() - state.lastFetch.projects > fiveMinutes
      }
    }
  },

  actions: {
    // Dashboard data loading
    async loadDashboardStats() {
      if (this.loading.dashboard || !this.needsRefresh.dashboard) {
        return
      }

      this.loading.dashboard = true
      this.error = null

      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/dashboard/stats', {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        
        this.stats = {
          weeklyHours: result.data?.activities?.recent_timesheets || 0,
          monthlyHours: result.data?.activities?.recent_timesheets || 0,
          pendingApprovals: result.data?.activities?.unread_notifications || 0,
          efficiency: 85 // TODO: Calculate from actual timesheet data
        }
        
        this.lastFetch.dashboard = Date.now()
      } catch (err) {
        this.error = `Errore caricamento statistiche: ${err.message}`
        console.error('Error loading dashboard stats:', err)
      } finally {
        this.loading.dashboard = false
      }
    },

    async loadRecentActivities() {
      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/timesheets/?per_page=5&page=1', {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        const entries = result.data || []

        this.recentActivities = entries.map(entry => ({
          id: entry.id,
          description: `${entry.project_name || 'Progetto'} - ${entry.task_name || 'Task'}`,
          hours: entry.hours,
          created_at: entry.created_at,
          date: entry.date
        }))
      } catch (err) {
        console.error('Error loading recent activities:', err)
        this.recentActivities = []
      }
    },

    async loadPendingApprovals() {
      if (!this.canApprove) return

      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/monthly-timesheets/?status=submitted', {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        this.pendingApprovals = result.data || []
      } catch (err) {
        console.error('Error loading pending approvals:', err)
        this.pendingApprovals = []
      }
    },

    async loadMyStatus() {
      if (this.canApprove) return

      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/monthly-timesheets/generate', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          },
          body: JSON.stringify({
            year: this.currentYear,
            month: this.currentMonth
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        this.myStatus = {
          status: result.data?.status || 'draft',
          totalHours: result.data?.total_hours || 0,
          billableHours: result.data?.billable_hours || 0
        }
      } catch (err) {
        console.error('Error loading my status:', err)
      }
    },

    // Monthly timesheet data - using same API as PersonnelProfile
    async loadMonthlyData(year = this.currentYear, month = this.currentMonth) {
      if (this.loading.monthlyData || (!this.needsRefresh.monthlyData && 
          year === this.currentYear && month === this.currentMonth)) {
        return {
          entries: this.monthlyEntries,
          projects: this.projectTasks
        }
      }

      this.loading.monthlyData = true
      this.error = null

      try {
        const authStore = useAuthStore()
        
        // Calculate start and end dates exactly like PersonnelProfile.vue
        const startDate = new Date(year, month - 1, 1).toISOString().split('T')[0]
        const endDate = new Date(year, month, 0).toISOString().split('T')[0]
        
        // Use same API call as PersonnelProfile.vue but force current user filter
        const userId = authStore.user?.id || ''
        const response = await fetch(`/api/timesheets?start_date=${startDate}&end_date=${endDate}&user_id=${userId}`, {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        
        this.currentYear = year
        this.currentMonth = month
        
        // Check if response follows expected structure
        if (!result.success) {
          throw new Error(result.message || 'API response indicates failure')
        }
        
        // Process timesheet entries into monthly format (same logic as PersonnelProfile)
        const timesheets = result.data || []
        const monthlyEntries = {}
        const projectTasksSet = new Set()
        
        timesheets.forEach(timesheet => {
          const entryDate = timesheet.date
          const taskKey = `${timesheet.project_id}-${timesheet.task_id || 'notask'}`
          
          if (!monthlyEntries[entryDate]) {
            monthlyEntries[entryDate] = {}
          }
          
          if (!monthlyEntries[entryDate][taskKey]) {
            monthlyEntries[entryDate][taskKey] = 0
          }
          
          monthlyEntries[entryDate][taskKey] += parseFloat(timesheet.hours || 0)
          
          // Track unique project-task combinations
          projectTasksSet.add(JSON.stringify({
            id: taskKey,
            project_id: timesheet.project_id,
            task_id: timesheet.task_id || null,
            project_name: timesheet.project_name || 'Progetto Sconosciuto',
            task_name: timesheet.task_name || 'Attività Generica',
            billable: timesheet.billable || false
          }))
        })
        
        this.monthlyEntries = monthlyEntries
        this.projectTasks = Array.from(projectTasksSet).map(pt => JSON.parse(pt))
        
        this.lastFetch.monthlyData = Date.now()
        console.log('Loaded monthly data:', {
          entries: Object.keys(this.monthlyEntries).length,
          projects: this.projectTasks.length,
          totalTimesheets: timesheets.length
        })
        
        return {
          entries: this.monthlyEntries,
          projects: this.projectTasks
        }
      } catch (err) {
        this.error = `Errore caricamento dati mensili: ${err.message}`
        console.error('Error loading monthly data:', err)
        return {
          entries: {},
          projects: []
        }
      } finally {
        this.loading.monthlyData = false
      }
    },

    // Available projects for timesheet
    async loadAvailableProjects() {
      if (this.availableProjects.length > 0 && !this.needsRefresh.projects) {
        return this.availableProjects
      }

      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/projects/', {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        
        // Handle different response structures
        let projects = []
        if (result.data && Array.isArray(result.data.projects)) {
          projects = result.data.projects
        } else if (result.data && Array.isArray(result.data.items)) {
          projects = result.data.items
        } else if (result.data && Array.isArray(result.data)) {
          projects = result.data
        } else if (Array.isArray(result)) {
          projects = result
        }

        this.availableProjects = projects.filter(p => p.status === 'active' || !p.status)
        this.lastFetch.projects = Date.now()
        console.log('Loaded available projects:', this.availableProjects.length, 'projects')
        return this.availableProjects
      } catch (err) {
        console.error('Error loading available projects:', err)
        this.availableProjects = []
        return []
      }
    },

    // Save timesheet entry using same API structure as PersonnelProfile.vue
    async saveEntry(projectTaskId, day, hours) {
      this.loading.saving = true
      
      try {
        const authStore = useAuthStore()
        
        // Parse project and task IDs from projectTaskId
        const [projectId, taskId] = projectTaskId.split('-')
        
        // Create date string
        const dateStr = `${this.currentYear}-${this.currentMonth.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
        
        // Use same payload structure as PersonnelProfile.vue
        const payload = {
          date: dateStr,
          project_id: parseInt(projectId),
          task_id: taskId !== 'notask' ? parseInt(taskId) : null,
          hours: parseFloat(hours),
          description: `Lavoro del ${dateStr}`
        }
        
        const response = await fetch('/api/timesheets', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          },
          body: JSON.stringify(payload)
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        
        // Check response success like PersonnelProfile.vue
        if (!result.success) {
          throw new Error(result.message || 'Errore durante il salvataggio')
        }

        // Update local state
        const entryKey = dateStr
        if (!this.monthlyEntries[entryKey]) {
          this.monthlyEntries[entryKey] = {}
        }
        this.monthlyEntries[entryKey][projectTaskId] = parseFloat(hours) || 0
        
        console.log('Saved timesheet entry:', {
          date: dateStr,
          project: projectId,
          task: taskId,
          hours: hours,
          success: true
        })

        return true
      } catch (err) {
        this.error = `Errore salvataggio ore: ${err.message}`
        console.error('Error saving entry:', err)
        return false
      } finally {
        this.loading.saving = false
      }
    },

    // Add project to timesheet
    async addProjectToTimesheet(projectId, taskId) {
      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/timesheet-projects/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          },
          body: JSON.stringify({
            project_id: projectId,
            task_id: taskId
          })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        // Refresh monthly data to include new project
        await this.loadMonthlyData(this.currentYear, this.currentMonth)
        return true
      } catch (err) {
        this.error = `Errore aggiunta progetto: ${err.message}`
        console.error('Error adding project to timesheet:', err)
        return false
      }
    },

    // Approval actions
    async approveTimesheet(timesheetId) {
      try {
        const authStore = useAuthStore()
        const response = await fetch(`/api/monthly-timesheets/${timesheetId}/approve`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        // Refresh pending approvals
        await this.loadPendingApprovals()
        return true
      } catch (err) {
        this.error = `Errore approvazione: ${err.message}`
        console.error('Error approving timesheet:', err)
        return false
      }
    },

    // Navigation helpers
    navigateMonth(direction) {
      if (direction === 'next') {
        if (this.currentMonth === 12) {
          this.currentMonth = 1
          this.currentYear++
        } else {
          this.currentMonth++
        }
      } else {
        if (this.currentMonth === 1) {
          this.currentMonth = 12
          this.currentYear--
        } else {
          this.currentMonth--
        }
      }
      
      // Load new month data
      this.loadMonthlyData(this.currentYear, this.currentMonth)
    },

    // Refresh all data
    async refreshAll() {
      // Reset cache timestamps to force refresh
      this.lastFetch = {
        dashboard: null,
        monthlyData: null,
        projects: null
      }
      
      await Promise.all([
        this.loadDashboardStats(),
        this.loadRecentActivities(),
        this.loadPendingApprovals(),
        this.loadMyStatus(),
        this.loadMonthlyData()
      ])
    },

    // Approval management
    async loadPendingTimesheets(filters = {}) {
      try {
        const authStore = useAuthStore()
        const params = new URLSearchParams({
          month: filters.month || new Date().getMonth() + 1,
          year: filters.year || new Date().getFullYear()
        })

        if (filters.status) params.append('status', filters.status)
        if (filters.user_id) params.append('user_id', filters.user_id)
        if (filters.search) params.append('search', filters.search)
        if (filters.anomalies_only) params.append('anomalies_only', 'true')

        const response = await fetch(`/api/monthly-timesheets/?${params}`, {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        return result.data || []
      } catch (err) {
        this.error = `Errore caricamento timesheet: ${err.message}`
        console.error('Error loading pending timesheets:', err)
        return []
      }
    },

    async rejectTimesheet(timesheetId, reason) {
      try {
        const authStore = useAuthStore()
        const response = await fetch(`/api/monthly-timesheets/${timesheetId}/reject`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          },
          body: JSON.stringify({ reason })
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        return true
      } catch (err) {
        this.error = `Errore rifiuto timesheet: ${err.message}`
        console.error('Error rejecting timesheet:', err)
        return false
      }
    },

    // Analytics data loading
    async loadAnalyticsData(filters = {}) {
      try {
        const authStore = useAuthStore()
        const params = new URLSearchParams()

        if (filters.start_date) params.append('start_date', filters.start_date)
        if (filters.end_date) params.append('end_date', filters.end_date)
        if (filters.department_id) params.append('department_id', filters.department_id)
        if (filters.project_id) params.append('project_id', filters.project_id)

        const response = await fetch(`/api/timesheets/?${params}`, {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        const timesheets = result.data || []

        // Group by user and calculate analytics
        const userAnalytics = {}
        timesheets.forEach(entry => {
          const userId = entry.user_id || entry.user?.id || 'unknown'
          if (!userAnalytics[userId]) {
            userAnalytics[userId] = {
              id: userId,
              full_name: entry.user?.full_name || entry.user?.name || `User ${userId}`,
              department: entry.user?.department || 'N/A',
              total_hours: 0,
              billable_hours: 0,
              projects: new Set(),
              entries: []
            }
          }

          userAnalytics[userId].total_hours += entry.hours
          if (entry.billable) {
            userAnalytics[userId].billable_hours += entry.hours
          }
          if (entry.project_id) {
            userAnalytics[userId].projects.add(entry.project_id)
          }
          userAnalytics[userId].entries.push(entry)
        })

        // Convert to array and calculate metrics
        return Object.values(userAnalytics).map(user => ({
          ...user,
          active_projects: user.projects.size,
          productivity: user.total_hours > 0 ? Math.round((user.billable_hours / user.total_hours) * 100) : 0,
          revenue: Math.round(user.billable_hours * 50) // Default rate, should come from user profile
        }))
      } catch (err) {
        this.error = `Errore caricamento analytics: ${err.message}`
        console.error('Error loading analytics data:', err)
        return []
      }
    },

    async loadTeamMembers() {
      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/personnel/users', {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        return result.data?.users || result.data || []
      } catch (err) {
        this.error = `Errore caricamento team: ${err.message}`
        console.error('Error loading team members:', err)
        return []
      }
    },

    async loadDepartments() {
      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/personnel/departments', {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        return result.data?.departments || result.data || []
      } catch (err) {
        console.error('Error loading departments:', err)
        return []
      }
    },

    // Bulk operations
    async bulkApproveTimesheets(timesheetIds) {
      try {
        const authStore = useAuthStore()
        const results = await Promise.allSettled(
          timesheetIds.map(id => this.approveTimesheet(id))
        )

        const successful = results.filter(r => r.status === 'fulfilled' && r.value).length
        const failed = results.length - successful

        if (failed > 0) {
          this.error = `${successful} approvati, ${failed} falliti`
        }

        return { successful, failed }
      } catch (err) {
        this.error = `Errore approvazione multipla: ${err.message}`
        return { successful: 0, failed: timesheetIds.length }
      }
    },

    async bulkRejectTimesheets(timesheetIds, reason) {
      try {
        const results = await Promise.allSettled(
          timesheetIds.map(id => this.rejectTimesheet(id, reason))
        )

        const successful = results.filter(r => r.status === 'fulfilled' && r.value).length
        const failed = results.length - successful

        if (failed > 0) {
          this.error = `${successful} rifiutati, ${failed} falliti`
        }

        return { successful, failed }
      } catch (err) {
        this.error = `Errore rifiuto multiplo: ${err.message}`
        return { successful: 0, failed: timesheetIds.length }
      }
    },

    // Export functionality
    async exportTimesheetData(filters = {}) {
      try {
        const authStore = useAuthStore()
        const params = new URLSearchParams(filters)

        const response = await fetch(`/api/timesheets/export?${params}`, {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        // Handle file download
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `timesheet_export_${new Date().toISOString().split('T')[0]}.xlsx`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)

        return true
      } catch (err) {
        this.error = `Errore export: ${err.message}`
        console.error('Error exporting data:', err)
        return false
      }
    },

    // Additional methods needed by TimesheetEntry
    async loadTimesheetHistory(filters = {}) {
      try {
        const authStore = useAuthStore()
        const params = new URLSearchParams()
        
        Object.keys(filters).forEach(key => {
          if (filters[key]) {
            params.append(key, filters[key])
          }
        })

        const response = await fetch(`/api/timesheets/?${params}`, {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        return result.data || []
      } catch (err) {
        this.error = `Errore caricamento storico: ${err.message}`
        console.error('Error loading timesheet history:', err)
        return []
      }
    },

    async loadAnalyticsData(filters = {}) {
      try {
        const authStore = useAuthStore()
        const params = new URLSearchParams()
        
        Object.keys(filters).forEach(key => {
          if (filters[key]) {
            params.append(key, filters[key])
          }
        })

        const response = await fetch(`/api/timesheets/analytics/?${params}`, {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        return result.data || []
      } catch (err) {
        this.error = `Errore caricamento analytics: ${err.message}`
        console.error('Error loading analytics data:', err)
        return []
      }
    },

    async loadDepartments() {
      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/personnel/departments/', {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        return result.data || []
      } catch (err) {
        console.error('Error loading departments:', err)
        return []
      }
    },

    async exportTimesheetData(filters = {}) {
      try {
        const authStore = useAuthStore()
        const params = new URLSearchParams()
        
        Object.keys(filters).forEach(key => {
          if (filters[key]) {
            params.append(key, filters[key])
          }
        })

        const response = await fetch(`/api/timesheets/export/?${params}`, {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        // Handle file download
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `timesheet-export-${new Date().toISOString().split('T')[0]}.xlsx`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        window.URL.revokeObjectURL(url)

        return true
      } catch (err) {
        this.error = `Errore esportazione: ${err.message}`
        console.error('Error exporting timesheet data:', err)
        return false
      }
    },

    async loadTimeOffRequests(filters = {}) {
      try {
        const authStore = useAuthStore()
        const params = new URLSearchParams()
        
        Object.keys(filters).forEach(key => {
          if (filters[key]) {
            params.append(key, filters[key])
          }
        })

        const response = await fetch(`/api/time-off-requests/?${params}`, {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        const result = await response.json()
        return result.data || []
      } catch (err) {
        this.error = `Errore caricamento richieste: ${err.message}`
        console.error('Error loading time off requests:', err)
        return []
      }
    },

    async createTimeOffRequest(data) {
      try {
        const authStore = useAuthStore()
        const response = await fetch('/api/time-off-requests/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          },
          body: JSON.stringify(data)
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        return true
      } catch (err) {
        this.error = `Errore creazione richiesta: ${err.message}`
        console.error('Error creating time off request:', err)
        return false
      }
    },

    async deleteTimeOffRequest(requestId) {
      try {
        const authStore = useAuthStore()
        const response = await fetch(`/api/time-off-requests/${requestId}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': authStore.csrfToken
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        return true
      } catch (err) {
        this.error = `Errore eliminazione richiesta: ${err.message}`
        console.error('Error deleting time off request:', err)
        return false
      }
    },

    // Clear error
    clearError() {
      this.error = null
    }
  }
})