/**
 * Utility functions for timesheet formatting and calculations
 */

// Month names in Italian
export const monthNames = [
  '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'April<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', 'A<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'
]

// Day names in Italian (short form)
export const dayNames = ['Dom', 'Lun', 'Mar', 'Mer', '<PERSON><PERSON>', 'Ven', 'Sab']

/**
 * Format hours for display
 * @param {number} hours - Hours to format
 * @returns {string} Formatted hours string
 */
export const formatHours = (hours) => {
  if (!hours || hours === 0) return '0h'
  return hours % 1 === 0 ? `${hours}h` : `${hours.toFixed(1)}h`
}

/**
 * Format date for display in Italian locale
 * @param {string|Date} dateString - Date to format
 * @returns {string} Formatted date string
 */
export const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('it-IT')
}

/**
 * Format date for API (YYYY-MM-DD)
 * @param {number} year - Year
 * @param {number} month - Month (1-12)
 * @param {number} day - Day (1-31)
 * @returns {string} Formatted date string
 */
export const formatApiDate = (year, month, day) => {
  return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
}

/**
 * Get status CSS classes for timesheet status
 * @param {string} status - Status value
 * @returns {string} CSS classes
 */
export const getStatusClass = (status) => {
  switch (status) {
    case 'approved':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
    case 'submitted':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
    case 'rejected':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
  }
}

/**
 * Get human-readable status text
 * @param {string} status - Status value
 * @returns {string} Human-readable status
 */
export const getStatusText = (status) => {
  switch (status) {
    case 'approved':
      return 'Approvato'
    case 'submitted':
      return 'In Attesa'
    case 'rejected':
      return 'Rifiutato'
    default:
      return 'Bozza'
  }
}

/**
 * Check if a day is today
 * @param {number} day - Day of month
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @returns {boolean} True if day is today
 */
export const isToday = (day, month, year) => {
  const today = new Date()
  return today.getDate() === day && 
         today.getMonth() + 1 === month && 
         today.getFullYear() === year
}

/**
 * Check if a day is weekend
 * @param {number} day - Day of month
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @returns {boolean} True if day is weekend
 */
export const isWeekend = (day, month, year) => {
  const date = new Date(year, month - 1, day)
  const dayOfWeek = date.getDay()
  return dayOfWeek === 0 || dayOfWeek === 6 // Sunday or Saturday
}

/**
 * Get day name for a specific date
 * @param {number} day - Day of month
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @returns {string} Day name (short form)
 */
export const getDayName = (day, month, year) => {
  const date = new Date(year, month - 1, day)
  return dayNames[date.getDay()]
}

/**
 * Get number of days in a month
 * @param {number} year - Year
 * @param {number} month - Month (1-12)
 * @returns {number} Number of days in month
 */
export const getDaysInMonth = (year, month) => {
  return new Date(year, month, 0).getDate()
}

/**
 * Generate array of days for a month
 * @param {number} year - Year
 * @param {number} month - Month (1-12)
 * @returns {number[]} Array of day numbers
 */
export const getMonthDays = (year, month) => {
  const days = getDaysInMonth(year, month)
  return Array.from({ length: days }, (_, i) => i + 1)
}

/**
 * Calculate total hours from entries object
 * @param {Object} entries - Entries object with day keys and hour values
 * @returns {number} Total hours
 */
export const calculateTotalHours = (entries) => {
  return Object.values(entries).reduce((total, dayEntries) => {
    if (typeof dayEntries === 'object') {
      return total + Object.values(dayEntries).reduce((dayTotal, hours) => dayTotal + (hours || 0), 0)
    }
    return total + (dayEntries || 0)
  }, 0)
}

/**
 * Calculate billable hours from entries and project configurations
 * @param {Object} entries - Entries object
 * @param {Array} projects - Project configurations with billable flags
 * @returns {number} Total billable hours
 */
export const calculateBillableHours = (entries, projects) => {
  return Object.values(entries).reduce((total, dayEntries) => {
    if (typeof dayEntries === 'object') {
      return total + Object.entries(dayEntries).reduce((dayTotal, [projectId, hours]) => {
        const project = projects.find(p => p.id === parseInt(projectId))
        return dayTotal + (project?.billable ? (hours || 0) : 0)
      }, 0)
    }
    return total
  }, 0)
}

/**
 * Validate hours input
 * @param {string|number} hours - Hours input to validate
 * @returns {number|null} Validated hours or null if invalid
 */
export const validateHours = (hours) => {
  if (hours === '' || hours === null || hours === undefined) return 0
  
  const numHours = parseFloat(hours)
  if (isNaN(numHours) || numHours < 0 || numHours > 24) return null
  
  // Round to 2 decimal places
  return Math.round(numHours * 100) / 100
}

/**
 * Create timesheet entry key
 * @param {number} year - Year
 * @param {number} month - Month (1-12)
 * @param {number} day - Day (1-31)
 * @param {number} projectTaskId - Project task ID
 * @returns {string} Entry key
 */
export const createEntryKey = (year, month, day, projectTaskId) => {
  return `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}-${projectTaskId}`
}

/**
 * Parse timesheet entry key
 * @param {string} key - Entry key
 * @returns {Object} Parsed key components
 */
export const parseEntryKey = (key) => {
  const [year, month, day, projectTaskId] = key.split('-')
  return {
    year: parseInt(year),
    month: parseInt(month),
    day: parseInt(day),
    projectTaskId: parseInt(projectTaskId)
  }
}